#!/bin/bash

# Script de déploiement rapide pour ERP HUB
# Usage: ./quick_deploy.sh

set -e

echo "🚀 Déploiement rapide ERP HUB"
echo "=============================="

# Vérification Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker non installé"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose non installé"
    exit 1
fi

echo "✅ Docker et Docker Compose disponibles"

# Copie du fichier d'environnement
if [ ! -f ".env" ]; then
    cp .env.production .env
    echo "✅ Fichier .env créé"
fi

# Construction et démarrage
echo "🔨 Construction des images..."
docker-compose -f docker-compose.prod.yml build

echo "🚀 Démarrage des services..."
docker-compose -f docker-compose.prod.yml up -d

echo "⏳ Attente du démarrage des services..."
sleep 30

echo "🔄 Exécution des migrations..."
docker-compose -f docker-compose.prod.yml exec -T backend python manage.py migrate

echo "📁 Collecte des fichiers statiques..."
docker-compose -f docker-compose.prod.yml exec -T backend python manage.py collectstatic --noinput

echo "✅ Déploiement terminé!"
echo ""
echo "📊 Services disponibles:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:8000/api"
echo "   - Admin: http://localhost:8000/admin"
echo "   - Docs: http://localhost:8000/docs"
echo ""
echo "🔍 Vérification des services:"
docker-compose -f docker-compose.prod.yml ps
