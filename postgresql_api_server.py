# 🚀 API FLASK AVEC POSTGRESQL VIA SUBPROCESS
# Contournement du problème psycopg2 sur Windows

from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import json
import os
from datetime import datetime
import secrets
import re

# Configuration de l'application
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)

# Extensions
CORS(app, origins=['*'])  # Permettre toutes les origines pour le développement

def execute_sql(sql_command):
    """Exécuter une commande SQL via Docker exec"""
    try:
        # Échapper les guillemets simples dans la commande SQL
        sql_escaped = sql_command.replace("'", "''")
        
        # Construire la commande Docker
        docker_cmd = [
            'docker', 'exec', 'erp_postgres', 
            'psql', '-U', 'erp_admin', '-d', 'erp_hub', 
            '-t', '-c', sql_command
        ]
        
        # Exécuter la commande
        result = subprocess.run(
            docker_cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            return {'success': True, 'data': result.stdout.strip()}
        else:
            return {'success': False, 'error': result.stderr.strip()}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def parse_sql_result(result_text):
    """Parser le résultat d'une requête SQL"""
    if not result_text:
        return []
    
    lines = result_text.strip().split('\n')
    if len(lines) < 1:
        return []
    
    # Supprimer les lignes vides et les séparateurs
    data_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith('-')]
    
    if not data_lines:
        return []
    
    # Parser chaque ligne
    results = []
    for line in data_lines:
        # Diviser par le pipe (|) et nettoyer
        fields = [field.strip() for field in line.split('|')]
        if len(fields) >= 5:  # Au moins id, name, type, forecast, realized
            results.append(fields)
    
    return results

# ===== ENDPOINTS =====

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifier l'état du serveur et de PostgreSQL"""
    # Test de connexion PostgreSQL
    result = execute_sql("SELECT 1;")
    
    return jsonify({
        'success': True,
        'message': 'Serveur ERP HUB opérationnel',
        'database': 'PostgreSQL',
        'database_status': 'connected' if result['success'] else 'disconnected',
        'timestamp': datetime.now().isoformat()
    }), 200

@app.route('/api/budgets', methods=['GET'])
def get_budgets():
    """Récupérer tous les budgets"""
    try:
        # Requête SQL pour récupérer les budgets
        sql = """
        SELECT id, category_name, category_type, cost_center, cost_center_name, 
               analytic_code, analytic_code_name, responsible, department, 
               forecast, realized, monthly_data, created_date, modified_date
        FROM budgets 
        ORDER BY created_date DESC;
        """
        
        result = execute_sql(sql)
        
        if not result['success']:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
        # Parser les résultats
        raw_data = result['data']
        budgets = []
        
        if raw_data:
            lines = raw_data.strip().split('\n')
            for line in lines:
                if line.strip() and '|' in line:
                    fields = [field.strip() for field in line.split('|')]
                    if len(fields) >= 11:
                        budget = {
                            'id': fields[0],
                            'categoryName': fields[1],
                            'categoryType': fields[2],
                            'costCenter': fields[3] if fields[3] else '',
                            'costCenterName': fields[4] if fields[4] else '',
                            'analyticCode': fields[5] if fields[5] else '',
                            'analyticCodeName': fields[6] if fields[6] else '',
                            'responsible': fields[7] if fields[7] else '',
                            'department': fields[8] if fields[8] else '',
                            'forecast': float(fields[9]) if fields[9] else 0,
                            'realized': float(fields[10]) if fields[10] else 0,
                            'monthlyData': json.loads(fields[11]) if fields[11] and fields[11] != '' else {},
                            'createdDate': fields[12] if len(fields) > 12 else '',
                            'modifiedDate': fields[13] if len(fields) > 13 else ''
                        }
                        budgets.append(budget)
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['id', 'categoryName', 'categoryType']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Construire la requête SQL d'insertion
        sql = f"""
        INSERT INTO budgets (
            id, category_name, category_type, cost_center, cost_center_name,
            analytic_code, analytic_code_name, responsible, department, 
            forecast, realized, monthly_data
        ) VALUES (
            '{budget_data['id']}',
            '{budget_data['categoryName']}',
            '{budget_data['categoryType']}',
            '{budget_data.get('costCenter', '')}',
            '{budget_data.get('costCenterName', '')}',
            '{budget_data.get('analyticCode', '')}',
            '{budget_data.get('analyticCodeName', '')}',
            '{budget_data.get('responsible', '')}',
            '{budget_data.get('department', '')}',
            {budget_data.get('forecast', 0)},
            {budget_data.get('realized', 0)},
            '{json.dumps(budget_data.get('monthlyData', {}))}'
        );
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget créé avec succès',
                'data': budget_data
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        
        # Construire la requête SQL de mise à jour
        sql = f"""
        UPDATE budgets SET
            category_name = '{budget_data['categoryName']}',
            category_type = '{budget_data['categoryType']}',
            cost_center = '{budget_data.get('costCenter', '')}',
            cost_center_name = '{budget_data.get('costCenterName', '')}',
            analytic_code = '{budget_data.get('analyticCode', '')}',
            analytic_code_name = '{budget_data.get('analyticCodeName', '')}',
            responsible = '{budget_data.get('responsible', '')}',
            department = '{budget_data.get('department', '')}',
            forecast = {budget_data.get('forecast', 0)},
            realized = {budget_data.get('realized', 0)},
            monthly_data = '{json.dumps(budget_data.get('monthlyData', {}))}',
            modified_date = CURRENT_TIMESTAMP
        WHERE id = '{budget_id}';
        """
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget mis à jour avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        sql = f"DELETE FROM budgets WHERE id = '{budget_id}';"
        
        result = execute_sql(sql)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Budget supprimé avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/stats', methods=['GET'])
def get_budget_stats():
    """Récupérer les statistiques des budgets"""
    try:
        sql = """
        SELECT
            COUNT(*) as total_budgets,
            SUM(forecast) as total_forecast,
            SUM(realized) as total_realized,
            category_type,
            COUNT(*) as count_by_type
        FROM budgets
        GROUP BY category_type;
        """

        result = execute_sql(sql)

        if result['success']:
            return jsonify({
                'success': True,
                'data': result['data'],
                'message': 'Statistiques récupérées'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS POUR TOUS LES AGENTS ERP =====

# ===== AGENT HR =====
@app.route('/api/employees', methods=['GET'])
def get_employees():
    """Récupérer tous les employés"""
    try:
        sql = "SELECT id, employee_number, first_name, last_name, email, position, department, salary, status FROM employees ORDER BY last_name;"
        result = execute_sql(sql)

        if result['success']:
            employees = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 9:
                            employee = {
                                'id': fields[0],
                                'employeeNumber': fields[1],
                                'firstName': fields[2],
                                'lastName': fields[3],
                                'email': fields[4],
                                'position': fields[5],
                                'department': fields[6],
                                'salary': float(fields[7]) if fields[7] else 0,
                                'status': fields[8]
                            }
                            employees.append(employee)

            return jsonify({'success': True, 'data': employees, 'count': len(employees)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/leaves', methods=['GET'])
def get_leaves():
    """Récupérer tous les congés"""
    try:
        sql = """
        SELECT l.id, l.employee_id, e.first_name, e.last_name, l.leave_type,
               l.start_date, l.end_date, l.days_count, l.status, l.reason
        FROM leaves l
        JOIN employees e ON l.employee_id = e.id
        ORDER BY l.start_date DESC;
        """
        result = execute_sql(sql)

        if result['success']:
            leaves = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 10:
                            leave = {
                                'id': fields[0],
                                'employeeId': fields[1],
                                'employeeName': f"{fields[2]} {fields[3]}",
                                'leaveType': fields[4],
                                'startDate': fields[5],
                                'endDate': fields[6],
                                'daysCount': int(fields[7]) if fields[7] else 0,
                                'status': fields[8],
                                'reason': fields[9]
                            }
                            leaves.append(leave)

            return jsonify({'success': True, 'data': leaves, 'count': len(leaves)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT SALES =====
@app.route('/api/customers', methods=['GET'])
def get_customers():
    """Récupérer tous les clients"""
    try:
        sql = """
        SELECT c.id, c.customer_code, c.company_name, c.contact_person, c.email,
               c.phone, c.city, c.country, c.customer_type, c.credit_limit, c.status
        FROM customers c
        ORDER BY c.company_name;
        """
        result = execute_sql(sql)

        if result['success']:
            customers = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 11:
                            customer = {
                                'id': fields[0],
                                'customerCode': fields[1],
                                'companyName': fields[2],
                                'contactPerson': fields[3],
                                'email': fields[4],
                                'phone': fields[5],
                                'city': fields[6],
                                'country': fields[7],
                                'customerType': fields[8],
                                'creditLimit': float(fields[9]) if fields[9] else 0,
                                'status': fields[10]
                            }
                            customers.append(customer)

            return jsonify({'success': True, 'data': customers, 'count': len(customers)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/opportunities', methods=['GET'])
def get_opportunities():
    """Récupérer toutes les opportunités"""
    try:
        sql = """
        SELECT o.id, o.customer_id, c.company_name, o.title, o.value,
               o.probability, o.stage, o.expected_close_date, o.source
        FROM opportunities o
        JOIN customers c ON o.customer_id = c.id
        ORDER BY o.expected_close_date;
        """
        result = execute_sql(sql)

        if result['success']:
            opportunities = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 9:
                            opportunity = {
                                'id': fields[0],
                                'customerId': fields[1],
                                'customerName': fields[2],
                                'title': fields[3],
                                'value': float(fields[4]) if fields[4] else 0,
                                'probability': float(fields[5]) if fields[5] else 0,
                                'stage': fields[6],
                                'expectedCloseDate': fields[7],
                                'source': fields[8]
                            }
                            opportunities.append(opportunity)

            return jsonify({'success': True, 'data': opportunities, 'count': len(opportunities)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/orders', methods=['GET'])
def get_orders():
    """Récupérer toutes les commandes"""
    try:
        sql = """
        SELECT o.id, o.order_number, o.customer_id, c.company_name, o.order_date,
               o.delivery_date, o.status, o.total_amount, o.payment_status
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        ORDER BY o.order_date DESC;
        """
        result = execute_sql(sql)

        if result['success']:
            orders = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 9:
                            order = {
                                'id': fields[0],
                                'orderNumber': fields[1],
                                'customerId': fields[2],
                                'customerName': fields[3],
                                'orderDate': fields[4],
                                'deliveryDate': fields[5],
                                'status': fields[6],
                                'totalAmount': float(fields[7]) if fields[7] else 0,
                                'paymentStatus': fields[8]
                            }
                            orders.append(order)

            return jsonify({'success': True, 'data': orders, 'count': len(orders)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT PURCHASE =====
@app.route('/api/suppliers', methods=['GET'])
def get_suppliers():
    """Récupérer tous les fournisseurs"""
    try:
        sql = """
        SELECT id, supplier_code, company_name, contact_person, email,
               phone, city, country, industry, payment_terms, status
        FROM suppliers
        ORDER BY company_name;
        """
        result = execute_sql(sql)

        if result['success']:
            suppliers = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 11:
                            supplier = {
                                'id': fields[0],
                                'supplierCode': fields[1],
                                'companyName': fields[2],
                                'contactPerson': fields[3],
                                'email': fields[4],
                                'phone': fields[5],
                                'city': fields[6],
                                'country': fields[7],
                                'industry': fields[8],
                                'paymentTerms': int(fields[9]) if fields[9] else 30,
                                'status': fields[10]
                            }
                            suppliers.append(supplier)

            return jsonify({'success': True, 'data': suppliers, 'count': len(suppliers)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/purchase-orders', methods=['GET'])
def get_purchase_orders():
    """Récupérer tous les bons de commande"""
    try:
        sql = """
        SELECT po.id, po.po_number, po.supplier_id, s.company_name, po.order_date,
               po.expected_delivery_date, po.status, po.total_amount
        FROM purchase_orders po
        JOIN suppliers s ON po.supplier_id = s.id
        ORDER BY po.order_date DESC;
        """
        result = execute_sql(sql)

        if result['success']:
            purchase_orders = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 8:
                            po = {
                                'id': fields[0],
                                'poNumber': fields[1],
                                'supplierId': fields[2],
                                'supplierName': fields[3],
                                'orderDate': fields[4],
                                'expectedDeliveryDate': fields[5],
                                'status': fields[6],
                                'totalAmount': float(fields[7]) if fields[7] else 0
                            }
                            purchase_orders.append(po)

            return jsonify({'success': True, 'data': purchase_orders, 'count': len(purchase_orders)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT STOCK =====
@app.route('/api/products', methods=['GET'])
def get_products():
    """Récupérer tous les produits"""
    try:
        sql = """
        SELECT id, product_code, name, description, category, brand,
               cost_price, selling_price, min_stock_level, status
        FROM products
        ORDER BY name;
        """
        result = execute_sql(sql)

        if result['success']:
            products = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 10:
                            product = {
                                'id': fields[0],
                                'productCode': fields[1],
                                'name': fields[2],
                                'description': fields[3],
                                'category': fields[4],
                                'brand': fields[5],
                                'costPrice': float(fields[6]) if fields[6] else 0,
                                'sellingPrice': float(fields[7]) if fields[7] else 0,
                                'minStockLevel': int(fields[8]) if fields[8] else 0,
                                'status': fields[9]
                            }
                            products.append(product)

            return jsonify({'success': True, 'data': products, 'count': len(products)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/inventory', methods=['GET'])
def get_inventory():
    """Récupérer l'inventaire"""
    try:
        sql = """
        SELECT i.id, i.product_id, p.name, p.product_code, i.warehouse_id, w.name,
               i.quantity_on_hand, i.quantity_reserved, i.quantity_available, i.location
        FROM inventory i
        JOIN products p ON i.product_id = p.id
        JOIN warehouses w ON i.warehouse_id = w.id
        ORDER BY p.name;
        """
        result = execute_sql(sql)

        if result['success']:
            inventory = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 10:
                            item = {
                                'id': fields[0],
                                'productId': fields[1],
                                'productName': fields[2],
                                'productCode': fields[3],
                                'warehouseId': fields[4],
                                'warehouseName': fields[5],
                                'quantityOnHand': int(fields[6]) if fields[6] else 0,
                                'quantityReserved': int(fields[7]) if fields[7] else 0,
                                'quantityAvailable': int(fields[8]) if fields[8] else 0,
                                'location': fields[9]
                            }
                            inventory.append(item)

            return jsonify({'success': True, 'data': inventory, 'count': len(inventory)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT LOGISTICS =====
@app.route('/api/warehouses', methods=['GET'])
def get_warehouses():
    """Récupérer tous les entrepôts"""
    try:
        sql = """
        SELECT id, warehouse_code, name, city, country, capacity,
               current_utilization, warehouse_type, status
        FROM warehouses
        ORDER BY name;
        """
        result = execute_sql(sql)

        if result['success']:
            warehouses = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 9:
                            warehouse = {
                                'id': fields[0],
                                'warehouseCode': fields[1],
                                'name': fields[2],
                                'city': fields[3],
                                'country': fields[4],
                                'capacity': float(fields[5]) if fields[5] else 0,
                                'currentUtilization': float(fields[6]) if fields[6] else 0,
                                'warehouseType': fields[7],
                                'status': fields[8]
                            }
                            warehouses.append(warehouse)

            return jsonify({'success': True, 'data': warehouses, 'count': len(warehouses)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/shipments', methods=['GET'])
def get_shipments():
    """Récupérer toutes les expéditions"""
    try:
        sql = """
        SELECT s.id, s.shipment_number, s.order_id, s.warehouse_id, w.name,
               s.carrier, s.tracking_number, s.ship_date, s.expected_delivery_date, s.status
        FROM shipments s
        JOIN warehouses w ON s.warehouse_id = w.id
        ORDER BY s.ship_date DESC;
        """
        result = execute_sql(sql)

        if result['success']:
            shipments = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 10:
                            shipment = {
                                'id': fields[0],
                                'shipmentNumber': fields[1],
                                'orderId': fields[2],
                                'warehouseId': fields[3],
                                'warehouseName': fields[4],
                                'carrier': fields[5],
                                'trackingNumber': fields[6],
                                'shipDate': fields[7],
                                'expectedDeliveryDate': fields[8],
                                'status': fields[9]
                            }
                            shipments.append(shipment)

            return jsonify({'success': True, 'data': shipments, 'count': len(shipments)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT CRM =====
@app.route('/api/contacts', methods=['GET'])
def get_contacts():
    """Récupérer tous les contacts"""
    try:
        sql = """
        SELECT c.id, c.customer_id, cust.company_name, c.first_name, c.last_name,
               c.title, c.email, c.phone, c.is_primary, c.is_decision_maker
        FROM contacts c
        LEFT JOIN customers cust ON c.customer_id = cust.id
        ORDER BY c.last_name;
        """
        result = execute_sql(sql)

        if result['success']:
            contacts = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 10:
                            contact = {
                                'id': fields[0],
                                'customerId': fields[1],
                                'customerName': fields[2],
                                'firstName': fields[3],
                                'lastName': fields[4],
                                'title': fields[5],
                                'email': fields[6],
                                'phone': fields[7],
                                'isPrimary': fields[8] == 't',
                                'isDecisionMaker': fields[9] == 't'
                            }
                            contacts.append(contact)

            return jsonify({'success': True, 'data': contacts, 'count': len(contacts)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/interactions', methods=['GET'])
def get_interactions():
    """Récupérer toutes les interactions"""
    try:
        sql = """
        SELECT i.id, i.customer_id, c.company_name, i.interaction_type, i.subject,
               i.interaction_date, i.duration_minutes, i.outcome, i.status
        FROM interactions i
        LEFT JOIN customers c ON i.customer_id = c.id
        ORDER BY i.interaction_date DESC;
        """
        result = execute_sql(sql)

        if result['success']:
            interactions = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 9:
                            interaction = {
                                'id': fields[0],
                                'customerId': fields[1],
                                'customerName': fields[2],
                                'interactionType': fields[3],
                                'subject': fields[4],
                                'interactionDate': fields[5],
                                'durationMinutes': int(fields[6]) if fields[6] else 0,
                                'outcome': fields[7],
                                'status': fields[8]
                            }
                            interactions.append(interaction)

            return jsonify({'success': True, 'data': interactions, 'count': len(interactions)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== AGENT BI =====
@app.route('/api/kpis', methods=['GET'])
def get_kpis():
    """Récupérer tous les KPIs"""
    try:
        sql = """
        SELECT id, kpi_name, kpi_type, target_value, current_value,
               unit, frequency, status
        FROM kpis
        ORDER BY kpi_name;
        """
        result = execute_sql(sql)

        if result['success']:
            kpis = []
            if result['data']:
                lines = result['data'].strip().split('\n')
                for line in lines:
                    if line.strip() and '|' in line:
                        fields = [field.strip() for field in line.split('|')]
                        if len(fields) >= 8:
                            kpi = {
                                'id': fields[0],
                                'kpiName': fields[1],
                                'kpiType': fields[2],
                                'targetValue': float(fields[3]) if fields[3] else 0,
                                'currentValue': float(fields[4]) if fields[4] else 0,
                                'unit': fields[5],
                                'frequency': fields[6],
                                'status': fields[7]
                            }
                            kpis.append(kpi)

            return jsonify({'success': True, 'data': kpis, 'count': len(kpis)}), 200
        else:
            return jsonify({'success': False, 'error': result['error']}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== ENDPOINT GLOBAL POUR DASHBOARD =====
@app.route('/api/dashboard', methods=['GET'])
def get_dashboard_data():
    """Récupérer les données du dashboard principal"""
    try:
        # Statistiques globales
        stats = {}

        # Compter les enregistrements dans chaque table
        tables = ['employees', 'customers', 'orders', 'products', 'suppliers', 'opportunities']
        for table in tables:
            result = execute_sql(f"SELECT COUNT(*) FROM {table};")
            if result['success']:
                count = result['data'].strip()
                stats[f'total_{table}'] = int(count) if count.isdigit() else 0

        # CA total des commandes
        result = execute_sql("SELECT SUM(total_amount) FROM orders WHERE status != 'cancelled';")
        if result['success']:
            total_revenue = result['data'].strip()
            stats['total_revenue'] = float(total_revenue) if total_revenue and total_revenue != '' else 0

        # Valeur totale des opportunités
        result = execute_sql("SELECT SUM(value) FROM opportunities WHERE stage NOT IN ('closed_lost');")
        if result['success']:
            total_pipeline = result['data'].strip()
            stats['total_pipeline'] = float(total_pipeline) if total_pipeline and total_pipeline != '' else 0

        return jsonify({
            'success': True,
            'data': stats,
            'message': 'Données dashboard récupérées'
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB avec PostgreSQL...")
    print("🐘 Base de données : PostgreSQL via Docker")
    
    # Test de connexion PostgreSQL
    test_result = execute_sql("SELECT version();")
    if test_result['success']:
        print("✅ Connexion PostgreSQL réussie")
        print(f"📊 Version: {test_result['data'][:50]}...")
    else:
        print("❌ Erreur connexion PostgreSQL:", test_result['error'])
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("📋 Endpoints disponibles :")
    print("   === SYSTÈME ===")
    print("   GET  /api/health           - État du serveur")
    print("   GET  /api/dashboard        - Données dashboard global")
    print("   === FINANCE ===")
    print("   GET  /api/budgets          - Récupérer tous les budgets")
    print("   POST /api/budgets          - Créer un nouveau budget")
    print("   PUT  /api/budgets/<id>     - Mettre à jour un budget")
    print("   DELETE /api/budgets/<id>   - Supprimer un budget")
    print("   GET  /api/budgets/stats    - Statistiques des budgets")
    print("   === HR ===")
    print("   GET  /api/employees        - Récupérer tous les employés")
    print("   GET  /api/leaves           - Récupérer tous les congés")
    print("   === SALES ===")
    print("   GET  /api/customers        - Récupérer tous les clients")
    print("   GET  /api/opportunities    - Récupérer toutes les opportunités")
    print("   GET  /api/orders           - Récupérer toutes les commandes")
    print("   === PURCHASE ===")
    print("   GET  /api/suppliers        - Récupérer tous les fournisseurs")
    print("   GET  /api/purchase-orders  - Récupérer tous les bons de commande")
    print("   === STOCK ===")
    print("   GET  /api/products         - Récupérer tous les produits")
    print("   GET  /api/inventory        - Récupérer l'inventaire")
    print("   === LOGISTICS ===")
    print("   GET  /api/warehouses       - Récupérer tous les entrepôts")
    print("   GET  /api/shipments        - Récupérer toutes les expéditions")
    print("   === CRM ===")
    print("   GET  /api/contacts         - Récupérer tous les contacts")
    print("   GET  /api/interactions     - Récupérer toutes les interactions")
    print("   === BI ===")
    print("   GET  /api/kpis             - Récupérer tous les KPIs")
    
    # Lancer le serveur
    app.run(debug=True, host='0.0.0.0', port=5000)
