@echo off
REM 🚀 DEMARRAGE ERP HUB SECURISE
REM Script pour demarrer l'ERP HUB avec toutes les ameliorations

echo.
echo ========================================
echo 🚀 DEMARRAGE ERP HUB SECURISE
echo ========================================
echo.

REM Verification Docker
echo ℹ️  Verification de Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker n'est pas installe ou accessible
    pause
    exit /b 1
)
echo ✅ Docker OK

REM Verification Docker Compose
echo ℹ️  Verification de Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose n'est pas installe ou accessible
    pause
    exit /b 1
)
echo ✅ Docker Compose OK

REM Creation des repertoires necessaires
echo ℹ️  Creation des repertoires...
if not exist "ssl\certs" mkdir "ssl\certs"
if not exist "ssl\private" mkdir "ssl\private"
if not exist "redis" mkdir "redis"
if not exist "logs" mkdir "logs"
if not exist "backups" mkdir "backups"
echo ✅ Repertoires crees

REM Configuration des variables d'environnement
echo ℹ️  Configuration des variables d'environnement...
if not exist "backend\.env" (
    echo # Configuration ERP HUB Securise > "backend\.env"
    echo JWT_SECRET_KEY=super_secret_jwt_key_2024_secure >> "backend\.env"
    echo SECRET_KEY=super_secret_app_key_2024_secure >> "backend\.env"
    echo FLASK_ENV=production >> "backend\.env"
    echo FLASK_DEBUG=False >> "backend\.env"
    echo API_PORT=5000 >> "backend\.env"
    echo. >> "backend\.env"
    echo # Base de donnees >> "backend\.env"
    echo DB_HOST=localhost >> "backend\.env"
    echo DB_PORT=5432 >> "backend\.env"
    echo DB_NAME=erp_hub >> "backend\.env"
    echo DB_USER=erp_admin >> "backend\.env"
    echo DB_PASSWORD=erp_secure_2024 >> "backend\.env"
    echo. >> "backend\.env"
    echo # Redis >> "backend\.env"
    echo REDIS_HOST=localhost >> "backend\.env"
    echo REDIS_PORT=6379 >> "backend\.env"
    echo REDIS_PASSWORD=erp_redis_secure_2024 >> "backend\.env"
    echo. >> "backend\.env"
    echo # Securite >> "backend\.env"
    echo RATE_LIMIT_ENABLED=True >> "backend\.env"
    echo SECURITY_HEADERS_ENABLED=True >> "backend\.env"
    echo CSRF_PROTECTION_ENABLED=True >> "backend\.env"
    echo ✅ Fichier .env cree
) else (
    echo ⚠️  Fichier .env deja existant
)

REM Arret des services existants
echo ℹ️  Arret des services existants...
docker-compose -f docker-compose-postgresql.yml down >nul 2>&1

REM Demarrage des services
echo ℹ️  Demarrage des services Docker...
docker-compose -f docker-compose-postgresql.yml up -d --build

if %errorlevel% neq 0 (
    echo ❌ Erreur lors du demarrage des services
    pause
    exit /b 1
)

echo ✅ Services Docker demarres

REM Attente du demarrage
echo ℹ️  Attente du demarrage des services...
timeout /t 30 /nobreak >nul

REM Verification PostgreSQL
echo ℹ️  Verification de PostgreSQL...
for /l %%i in (1,1,10) do (
    docker exec erp_postgres pg_isready -U erp_admin -d erp_hub >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL pret
        goto :redis_check
    )
    timeout /t 3 /nobreak >nul
)
echo ⚠️  PostgreSQL pourrait ne pas etre completement pret

:redis_check
REM Verification Redis
echo ℹ️  Verification de Redis...
for /l %%i in (1,1,10) do (
    docker exec erp_redis redis-cli -a erp_redis_secure_2024 ping >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Redis pret
        goto :summary
    )
    timeout /t 3 /nobreak >nul
)
echo ⚠️  Redis pourrait ne pas etre completement pret

:summary
echo.
echo ========================================
echo 🎉 ERP HUB SECURISE DEMARRE !
echo ========================================
echo.
echo 🔒 Ameliorations de securite activees :
echo    ✅ Authentification avec httpOnly cookies
echo    ✅ Protection CSRF
echo    ✅ Rate limiting avance
echo    ✅ Headers de securite HTTP
echo    ✅ Cache Redis integre
echo.
echo 🌐 Acces aux services :
echo    🔒 Application principale : http://localhost:3000
echo    📊 Monitoring Grafana : http://localhost:3000
echo    📈 Metriques Prometheus : http://localhost:9090
echo    🗄️  Base de donnees : localhost:5432
echo    🚀 Cache Redis : localhost:6379
echo.
echo 🔑 Identifiants par defaut :
echo    Application : admin / Admin123!
echo    Grafana : admin / admin_grafana_2024
echo.
echo 📋 Prochaines etapes recommandees :
echo    1. Ouvrir http://localhost:3000 dans votre navigateur
echo    2. Tester la nouvelle interface avec navigation verticale
echo    3. Verifier les fonctionnalites de securite
echo    4. Changer les mots de passe par defaut
echo.
echo 📖 Documentation :
echo    - Ameliorations : SECURITY_IMPROVEMENTS.md
echo    - Guide migration : frontend/SIDEBAR_MIGRATION_GUIDE.md
echo.

REM Ouvrir automatiquement l'application
echo ℹ️  Ouverture de l'application dans le navigateur...
start http://localhost:3000

echo.
echo ✅ Demarrage termine avec succes !
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
