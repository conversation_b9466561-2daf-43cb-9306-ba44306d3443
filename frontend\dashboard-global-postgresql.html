<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Global - ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="sidebar-navigation.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        .dashboard-content {
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            background: rgba(59, 130, 246, 0.1);
            color: #1e293b;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-connected {
            background: #10b981;
        }

        .status-disconnected {
            background: #ef4444;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #1e293b;
            border: 1px solid #cbd5e1;
        }

        .btn-secondary:hover {
            background: #cbd5e1;
        }
        

        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
        }
        
        .stat-card.hr::before { --accent-color: #10b981; }
        .stat-card.sales::before { --accent-color: #3b82f6; }
        .stat-card.purchase::before { --accent-color: #f59e0b; }
        .stat-card.stock::before { --accent-color: #8b5cf6; }
        .stat-card.logistics::before { --accent-color: #06b6d4; }
        .stat-card.finance::before { --accent-color: #ef4444; }
        .stat-card.crm::before { --accent-color: #ec4899; }
        .stat-card.bi::before { --accent-color: #7c3aed; }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-icon {
            font-size: 2rem;
            opacity: 0.8;
        }
        
        .stat-icon.hr { color: #10b981; }
        .stat-icon.sales { color: #3b82f6; }
        .stat-icon.purchase { color: #f59e0b; }
        .stat-icon.stock { color: #8b5cf6; }
        .stat-icon.logistics { color: #06b6d4; }
        .stat-icon.finance { color: #ef4444; }
        .stat-icon.crm { color: #ec4899; }
        .stat-icon.bi { color: #7c3aed; }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .chart-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .loading {
            display: inline-block;
            width: 1.5rem;
            height: 1.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1px solid;
        }
        
        .alert-success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }
        
        .alert-error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }
        
        .alert-info {
            background: #eff6ff;
            border-color: #bfdbfe;
            color: #1e40af;
        }
        
        @media (max-width: 1024px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            

            
            .stats-overview {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- La navigation sera injectée automatiquement par sidebar-navigation.js -->

    <main class="main-content">
        <div class="module-header">
            <div class="module-header-content">
                <div class="module-title">
                    <span class="module-icon material-icons">dashboard</span>
                    <h1>Dashboard Global</h1>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="connection-status">
                        <div class="status-indicator" id="connectionStatus"></div>
                        <span id="connectionText">Connexion...</span>
                    </div>
                    <button class="btn btn-primary" onclick="refreshAllData()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Actualiser
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-content">


        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Statistiques globales -->
        <div class="stats-overview">
            <div class="stat-card hr">
                <div class="stat-header">
                    <span class="stat-icon hr material-icons">people</span>
                </div>
                <div class="stat-value" id="totalEmployees">0</div>
                <div class="stat-label">Employés</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+2 ce mois</span>
                </div>
            </div>

            <div class="stat-card sales">
                <div class="stat-header">
                    <span class="stat-icon sales material-icons">trending_up</span>
                </div>
                <div class="stat-value" id="totalRevenue">0€</div>
                <div class="stat-label">Chiffre d'Affaires</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+15% ce mois</span>
                </div>
            </div>

            <div class="stat-card purchase">
                <div class="stat-header">
                    <span class="stat-icon purchase material-icons">shopping_cart</span>
                </div>
                <div class="stat-value" id="totalPurchases">0€</div>
                <div class="stat-label">Achats</div>
                <div class="stat-change negative">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_down</span>
                    <span>-5% ce mois</span>
                </div>
            </div>

            <div class="stat-card stock">
                <div class="stat-header">
                    <span class="stat-icon stock material-icons">inventory_2</span>
                </div>
                <div class="stat-value" id="totalProducts">0</div>
                <div class="stat-label">Produits</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+3 nouveaux</span>
                </div>
            </div>

            <div class="stat-card logistics">
                <div class="stat-header">
                    <span class="stat-icon logistics material-icons">local_shipping</span>
                </div>
                <div class="stat-value" id="totalShipments">0</div>
                <div class="stat-label">Expéditions</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>98% livrées</span>
                </div>
            </div>

            <div class="stat-card finance">
                <div class="stat-header">
                    <span class="stat-icon finance material-icons">account_balance</span>
                </div>
                <div class="stat-value" id="totalBudgets">0</div>
                <div class="stat-label">Budgets</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>85% utilisés</span>
                </div>
            </div>

            <div class="stat-card crm">
                <div class="stat-header">
                    <span class="stat-icon crm material-icons">contacts</span>
                </div>
                <div class="stat-value" id="totalContacts">0</div>
                <div class="stat-label">Contacts CRM</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>+8 ce mois</span>
                </div>
            </div>

            <div class="stat-card bi">
                <div class="stat-header">
                    <span class="stat-icon bi material-icons">analytics</span>
                </div>
                <div class="stat-value" id="totalKpis">0</div>
                <div class="stat-label">KPIs Actifs</div>
                <div class="stat-change positive">
                    <span class="material-icons" style="font-size: 0.75rem;">trending_up</span>
                    <span>92% objectifs</span>
                </div>
            </div>
        </div>

        <!-- Section des graphiques -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Évolution des Ventes</h3>
                    <button class="btn btn-secondary" onclick="updateSalesChart()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    </button>
                </div>
                <div class="chart-container">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Répartition par Module</h3>
                </div>
                <div class="chart-container">
                    <canvas id="moduleChart"></canvas>
                </div>
            </div>
        </div>
        </div>
    </main>

    <script src="sidebar-navigation.js"></script>
    <script>
        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';
        
        // Variables globales pour les données
        let dashboardData = {};
        let salesChart = null;
        let moduleChart = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });

        // Initialiser le dashboard
        async function initializeDashboard() {
            showAlert('Initialisation du dashboard...', 'info');
            await checkConnection();
            await loadDashboardData();
            initializeCharts();
            updateStatistics();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les données du dashboard
        async function loadDashboardData() {
            try {
                showAlert('Chargement des données depuis PostgreSQL...', 'info');

                // Charger les données principales du dashboard
                const dashboardResponse = await fetch(`${API_BASE_URL}/dashboard`);
                if (dashboardResponse.ok) {
                    const dashboardResult = await dashboardResponse.json();
                    if (dashboardResult.success) {
                        dashboardData = dashboardResult.data;
                    }
                }

                // Charger les données supplémentaires pour les statistiques complètes
                await loadAdditionalData();

                showAlert('Toutes les données chargées avec succès depuis PostgreSQL', 'success');
            } catch (error) {
                console.error('Erreur chargement dashboard:', error);
                showAlert('Erreur lors du chargement des données: ' + error.message, 'error');
                dashboardData = {};
            }
        }

        // Charger les données supplémentaires
        async function loadAdditionalData() {
            try {
                // Charger les données des achats
                const purchaseResponse = await fetch(`${API_BASE_URL}/purchase-orders`);
                if (purchaseResponse.ok) {
                    const purchaseData = await purchaseResponse.json();
                    if (purchaseData.success) {
                        const totalPurchases = purchaseData.data.reduce((sum, po) => sum + (po.totalAmount || 0), 0);
                        dashboardData.total_purchases = totalPurchases;
                        dashboardData.total_purchase_orders = purchaseData.count;
                    }
                }

                // Charger les données des expéditions
                const shipmentsResponse = await fetch(`${API_BASE_URL}/shipments`);
                if (shipmentsResponse.ok) {
                    const shipmentsData = await shipmentsResponse.json();
                    if (shipmentsData.success) {
                        dashboardData.total_shipments = shipmentsData.count;
                    }
                }

                // Charger les données des budgets
                const budgetsResponse = await fetch(`${API_BASE_URL}/budgets`);
                if (budgetsResponse.ok) {
                    const budgetsData = await budgetsResponse.json();
                    if (budgetsData.success) {
                        dashboardData.total_budgets = budgetsData.count;
                    }
                }

                // Charger les données CRM
                const contactsResponse = await fetch(`${API_BASE_URL}/contacts`);
                if (contactsResponse.ok) {
                    const contactsData = await contactsResponse.json();
                    if (contactsData.success) {
                        dashboardData.total_contacts = contactsData.count;
                    }
                }

                // Charger les données BI
                const kpisResponse = await fetch(`${API_BASE_URL}/kpis`);
                if (kpisResponse.ok) {
                    const kpisData = await kpisResponse.json();
                    if (kpisData.success) {
                        dashboardData.total_kpis = kpisData.count;
                    }
                }

                console.log('Données complètes chargées:', dashboardData);
            } catch (error) {
                console.error('Erreur lors du chargement des données supplémentaires:', error);
            }
        }

        // Mettre à jour les statistiques
        function updateStatistics() {
            // Mise à jour avec les vraies données depuis PostgreSQL
            document.getElementById('totalEmployees').textContent = dashboardData.total_employees || 0;
            document.getElementById('totalRevenue').textContent = (dashboardData.total_revenue || 0).toLocaleString() + '€';
            document.getElementById('totalPurchases').textContent = (dashboardData.total_purchases || 0).toLocaleString() + '€';
            document.getElementById('totalProducts').textContent = dashboardData.total_products || 0;
            document.getElementById('totalShipments').textContent = dashboardData.total_shipments || 0;
            document.getElementById('totalBudgets').textContent = dashboardData.total_budgets || 0;
            document.getElementById('totalContacts').textContent = dashboardData.total_contacts || 0;
            document.getElementById('totalKpis').textContent = dashboardData.total_kpis || 0;

            // Log pour debug
            console.log('Statistiques mises à jour:', {
                employees: dashboardData.total_employees,
                revenue: dashboardData.total_revenue,
                purchases: dashboardData.total_purchases,
                products: dashboardData.total_products,
                shipments: dashboardData.total_shipments,
                budgets: dashboardData.total_budgets,
                contacts: dashboardData.total_contacts,
                kpis: dashboardData.total_kpis
            });
        }

        // Initialiser les graphiques
        function initializeCharts() {
            // Graphique des ventes (placeholder)
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                    datasets: [{
                        label: 'Ventes',
                        data: [65000, 75000, 85000, 95000, 105000, 115000],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Graphique de répartition par module
            const moduleCtx = document.getElementById('moduleChart').getContext('2d');
            moduleChart = new Chart(moduleCtx, {
                type: 'doughnut',
                data: {
                    labels: ['HR', 'Sales', 'Purchase', 'Stock', 'Logistics'],
                    datasets: [{
                        data: [15, 30, 20, 25, 10],
                        backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#8b5cf6', '#06b6d4']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Fonctions utilitaires
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshAllData() {
            initializeDashboard();
        }

        function updateSalesChart() {
            // Placeholder pour mise à jour du graphique des ventes
            showAlert('Graphique des ventes mis à jour', 'success');
        }
    </script>
</body>
</html>
