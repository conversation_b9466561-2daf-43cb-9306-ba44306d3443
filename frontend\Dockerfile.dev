FROM node:18-alpine

# Variables d'environnement pour le développement
ENV NODE_ENV=development

# Répertoire de travail
WORKDIR /app

# Installation de git pour certaines dépendances
RUN apk add --no-cache git

# Copie des fichiers de dépendances
COPY package*.json ./

# Installation des dépendances (incluant dev)
RUN npm install

# Copie du code source
COPY . .

# Port d'exposition
EXPOSE 3000
EXPOSE 24678

# Commande de démarrage pour le développement avec hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
