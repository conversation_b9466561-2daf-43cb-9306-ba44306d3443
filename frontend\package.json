{"name": "erp-hub-frontend", "version": "1.0.0", "description": "Frontend React pour ERP HUB - Système ERP modulaire avec architecture d'agents", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "start": "node server.js", "start:prod": "npm run build && node server.js", "serve": "node server.js"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/x-charts": "^7.0.0", "@mui/x-data-grid": "^7.0.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/d3": "^7.4.3", "axios": "^1.6.0", "chart.js": "^4.4.2", "clsx": "^2.1.1", "cors": "^2.8.5", "d3": "^7.9.0", "date-fns": "^2.30.0", "express": "^5.1.0", "framer-motion": "^10.18.0", "http-proxy-middleware": "^3.0.5", "immer": "^10.1.1", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.0", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.18.0", "react-select": "^5.8.0", "react-spring": "^9.7.3", "react-table": "^7.8.0", "recharts": "^2.8.0", "tailwind-merge": "^2.6.0", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@types/lodash": "^4.17.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-grid-layout": "^1.3.5", "@types/react-table": "^7.7.17", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^22.1.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}