# 🧪 TEST DE TOUS LES ENDPOINTS API ERP HUB
# Script pour tester tous les endpoints de l'API PostgreSQL

import requests
import json
from datetime import datetime

# Configuration
API_BASE_URL = 'http://localhost:5000/api'

def test_endpoint(endpoint_name, url):
    """Tester un endpoint spécifique"""
    try:
        print(f"\n🔍 Test {endpoint_name}...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                count = data.get('count', 0)
                print(f"✅ {endpoint_name}: {count} enregistrements")
                return True, count
            else:
                print(f"❌ {endpoint_name}: Erreur API - {data.get('error', 'Erreur inconnue')}")
                return False, 0
        else:
            print(f"❌ {endpoint_name}: HTTP {response.status_code}")
            return False, 0
            
    except requests.exceptions.Timeout:
        print(f"⏱️ {endpoint_name}: Timeout (>10s)")
        return False, 0
    except requests.exceptions.ConnectionError:
        print(f"🔌 {endpoint_name}: Erreur de connexion")
        return False, 0
    except Exception as e:
        print(f"❌ {endpoint_name}: Erreur - {str(e)}")
        return False, 0

def main():
    """Fonction principale de test"""
    print("🧪 TEST COMPLET DE TOUS LES ENDPOINTS API ERP HUB")
    print("=" * 60)
    print(f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 URL de base: {API_BASE_URL}")
    
    # Liste des endpoints à tester
    endpoints = [
        ("Health Check", f"{API_BASE_URL}/health"),
        ("Dashboard", f"{API_BASE_URL}/dashboard"),
        ("Budgets (Finance)", f"{API_BASE_URL}/budgets"),
        ("Employés (HR)", f"{API_BASE_URL}/employees"),
        ("Congés (HR)", f"{API_BASE_URL}/leaves"),
        ("Clients (Sales)", f"{API_BASE_URL}/customers"),
        ("Opportunités (Sales)", f"{API_BASE_URL}/opportunities"),
        ("Commandes (Sales)", f"{API_BASE_URL}/orders"),
        ("Fournisseurs (Purchase)", f"{API_BASE_URL}/suppliers"),
        ("Bons de commande (Purchase)", f"{API_BASE_URL}/purchase-orders"),
        ("Produits (Stock)", f"{API_BASE_URL}/products"),
        ("Inventaire (Stock)", f"{API_BASE_URL}/inventory"),
        ("Entrepôts (Logistics)", f"{API_BASE_URL}/warehouses"),
        ("Expéditions (Logistics)", f"{API_BASE_URL}/shipments"),
        ("Contacts (CRM)", f"{API_BASE_URL}/contacts"),
        ("Interactions (CRM)", f"{API_BASE_URL}/interactions"),
        ("KPIs (BI)", f"{API_BASE_URL}/kpis")
    ]
    
    # Statistiques
    total_tests = len(endpoints)
    successful_tests = 0
    total_records = 0
    
    # Exécuter tous les tests
    for endpoint_name, url in endpoints:
        success, count = test_endpoint(endpoint_name, url)
        if success:
            successful_tests += 1
            total_records += count
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"✅ Tests réussis: {successful_tests}/{total_tests}")
    print(f"📈 Taux de succès: {(successful_tests/total_tests)*100:.1f}%")
    print(f"📋 Total enregistrements: {total_records}")
    
    if successful_tests == total_tests:
        print("\n🎉 TOUS LES ENDPOINTS FONCTIONNENT PARFAITEMENT !")
        print("✅ L'API PostgreSQL est entièrement opérationnelle")
    else:
        failed_tests = total_tests - successful_tests
        print(f"\n⚠️ {failed_tests} endpoint(s) en échec")
        print("🔧 Vérifiez les logs de l'API pour plus de détails")
    
    print("\n🏁 Tests terminés")

if __name__ == "__main__":
    main()
