# 🗄️ CONFIGURATION BASE DE DONNÉES ERP HUB
# Système de sauvegarde persistante avec SQLite/PostgreSQL

import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class ERPDatabase:
    """Gestionnaire de base de données pour ERP HUB"""
    
    def __init__(self, db_path: str = "erp_hub.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialiser la base de données avec les tables nécessaires"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table des budgets
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS budgets (
                id TEXT PRIMARY KEY,
                category_name TEXT NOT NULL,
                category_type TEXT NOT NULL,
                cost_center TEXT,
                cost_center_name TEXT,
                analytic_code TEXT,
                analytic_code_name TEXT,
                responsible TEXT,
                department TEXT,
                notes TEXT,
                forecast REAL DEFAULT 0,
                realized REAL DEFAULT 0,
                monthly_data TEXT,  -- JSON des données mensuelles
                created_date TEXT,
                modified_date TEXT
            )
        ''')
        
        # Table des mouvements de trésorerie
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS movements (
                id TEXT PRIMARY KEY,
                account_id TEXT NOT NULL,
                date TEXT NOT NULL,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                type TEXT NOT NULL,
                category TEXT,
                created_date TEXT,
                modified_date TEXT
            )
        ''')
        
        # Table des comptes bancaires
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                bank TEXT NOT NULL,
                type TEXT NOT NULL,
                balance REAL DEFAULT 0,
                alert_threshold REAL DEFAULT 0,
                iban TEXT,
                created_date TEXT,
                modified_date TEXT
            )
        ''')
        
        # Table de l'historique des actions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS action_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                data TEXT NOT NULL,  -- JSON des données
                timestamp TEXT NOT NULL,
                user_id TEXT DEFAULT 'default'
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Base de données initialisée : {self.db_path}")
    
    # ===== OPÉRATIONS CRUD BUDGETS =====
    
    def create_budget(self, budget_data: Dict) -> bool:
        """Créer un nouveau budget"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO budgets (
                    id, category_name, category_type, cost_center, cost_center_name,
                    analytic_code, analytic_code_name, responsible, department, notes,
                    forecast, realized, monthly_data, created_date, modified_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                budget_data['id'],
                budget_data['categoryName'],
                budget_data['categoryType'],
                budget_data.get('costCenter', ''),
                budget_data.get('costCenterName', ''),
                budget_data.get('analyticCode', ''),
                budget_data.get('analyticCodeName', ''),
                budget_data.get('responsible', ''),
                budget_data.get('department', ''),
                budget_data.get('notes', ''),
                budget_data.get('forecast', 0),
                budget_data.get('realized', 0),
                json.dumps(budget_data.get('monthlyData', {})),
                budget_data.get('createdDate', datetime.now().isoformat()),
                budget_data.get('modifiedDate', datetime.now().isoformat())
            ))
            
            conn.commit()
            conn.close()
            print(f"✅ Budget créé : {budget_data['id']}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création budget : {e}")
            return False
    
    def read_budgets(self) -> List[Dict]:
        """Lire tous les budgets"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM budgets ORDER BY created_date DESC')
            rows = cursor.fetchall()
            
            budgets = []
            for row in rows:
                budget = {
                    'id': row[0],
                    'categoryName': row[1],
                    'categoryType': row[2],
                    'costCenter': row[3],
                    'costCenterName': row[4],
                    'analyticCode': row[5],
                    'analyticCodeName': row[6],
                    'responsible': row[7],
                    'department': row[8],
                    'notes': row[9],
                    'forecast': row[10],
                    'realized': row[11],
                    'monthlyData': json.loads(row[12]) if row[12] else {},
                    'createdDate': row[13],
                    'modifiedDate': row[14]
                }
                budgets.append(budget)
            
            conn.close()
            print(f"✅ {len(budgets)} budgets chargés")
            return budgets
            
        except Exception as e:
            print(f"❌ Erreur lecture budgets : {e}")
            return []
    
    def update_budget(self, budget_id: str, budget_data: Dict) -> bool:
        """Mettre à jour un budget"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE budgets SET
                    category_name = ?, category_type = ?, cost_center = ?, cost_center_name = ?,
                    analytic_code = ?, analytic_code_name = ?, responsible = ?, department = ?,
                    notes = ?, forecast = ?, realized = ?, monthly_data = ?, modified_date = ?
                WHERE id = ?
            ''', (
                budget_data['categoryName'],
                budget_data['categoryType'],
                budget_data.get('costCenter', ''),
                budget_data.get('costCenterName', ''),
                budget_data.get('analyticCode', ''),
                budget_data.get('analyticCodeName', ''),
                budget_data.get('responsible', ''),
                budget_data.get('department', ''),
                budget_data.get('notes', ''),
                budget_data.get('forecast', 0),
                budget_data.get('realized', 0),
                json.dumps(budget_data.get('monthlyData', {})),
                datetime.now().isoformat(),
                budget_id
            ))
            
            conn.commit()
            conn.close()
            print(f"✅ Budget mis à jour : {budget_id}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur mise à jour budget : {e}")
            return False
    
    def delete_budget(self, budget_id: str) -> bool:
        """Supprimer un budget"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM budgets WHERE id = ?', (budget_id,))
            
            conn.commit()
            conn.close()
            print(f"✅ Budget supprimé : {budget_id}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur suppression budget : {e}")
            return False
    
    # ===== HISTORIQUE DES ACTIONS =====
    
    def save_action_history(self, action: str, data: Dict, user_id: str = 'default') -> bool:
        """Sauvegarder une action dans l'historique"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO action_history (action, data, timestamp, user_id)
                VALUES (?, ?, ?, ?)
            ''', (action, json.dumps(data), datetime.now().isoformat(), user_id))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde historique : {e}")
            return False
    
    # ===== UTILITAIRES =====
    
    def get_database_stats(self) -> Dict:
        """Obtenir les statistiques de la base de données"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM budgets')
            budget_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM movements')
            movement_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM accounts')
            account_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM action_history')
            history_count = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'budgets': budget_count,
                'movements': movement_count,
                'accounts': account_count,
                'history_actions': history_count,
                'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            }
            
        except Exception as e:
            print(f"❌ Erreur statistiques : {e}")
            return {}

# ===== EXEMPLE D'UTILISATION =====

if __name__ == "__main__":
    # Initialiser la base de données
    db = ERPDatabase()
    
    # Exemple de création d'un budget
    budget_example = {
        'id': 'budget_test_001',
        'categoryName': 'Marketing Digital',
        'categoryType': 'expense',
        'costCenter': 'CC001',
        'costCenterName': 'Marketing',
        'analyticCode': 'AC001',
        'analyticCodeName': 'Publicité',
        'responsible': 'Jean Dupont',
        'department': 'Marketing',
        'notes': 'Budget pour campagnes digitales 2024',
        'forecast': 50000,
        'realized': 12500,
        'monthlyData': {
            'janvier': {'forecast': 4167, 'realized': 4200},
            'février': {'forecast': 4167, 'realized': 3800},
            'mars': {'forecast': 4167, 'realized': 4500}
        }
    }
    
    # Créer le budget
    db.create_budget(budget_example)
    
    # Lire tous les budgets
    budgets = db.read_budgets()
    print(f"Budgets en base : {len(budgets)}")
    
    # Statistiques
    stats = db.get_database_stats()
    print(f"Statistiques base : {stats}")
