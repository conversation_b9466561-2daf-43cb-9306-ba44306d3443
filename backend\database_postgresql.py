# 🗄️ CONFIGURATION BASE DE DONNÉES POSTGRESQL POUR ERP HUB
# Système de sauvegarde persistante avec PostgreSQL

import psycopg2
import psycopg2.extras
import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class ERPDatabasePostgreSQL:
    """Gestionnaire de base de données PostgreSQL pour ERP HUB"""
    
    def __init__(self):
        self.connection_params = {
            'host': 'localhost',
            'port': 5432,
            'database': 'erp_hub',
            'user': 'erp_admin',
            'password': 'erp_secure_2024'
        }
        self.init_database()
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return psycopg2.connect(**self.connection_params)
    
    def init_database(self):
        """Initialiser la base de données avec les tables nécessaires"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Table des budgets
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS budgets (
                    id VARCHAR(255) PRIMARY KEY,
                    category_name VARCHAR(255) NOT NULL,
                    category_type VARCHAR(50) NOT NULL,
                    cost_center VARCHAR(100),
                    cost_center_name VARCHAR(255),
                    analytic_code VARCHAR(100),
                    analytic_code_name VARCHAR(255),
                    responsible VARCHAR(255),
                    department VARCHAR(255),
                    notes TEXT,
                    forecast DECIMAL(15,2) DEFAULT 0,
                    realized DECIMAL(15,2) DEFAULT 0,
                    monthly_data JSONB,
                    created_date TIMESTAMP,
                    modified_date TIMESTAMP
                )
            ''')
            
            # Table des mouvements de trésorerie
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS movements (
                    id VARCHAR(255) PRIMARY KEY,
                    account_id VARCHAR(255) NOT NULL,
                    date DATE NOT NULL,
                    description TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    category VARCHAR(100),
                    created_date TIMESTAMP,
                    modified_date TIMESTAMP
                )
            ''')
            
            # Table des comptes bancaires
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    bank VARCHAR(255) NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    balance DECIMAL(15,2) DEFAULT 0,
                    alert_threshold DECIMAL(15,2) DEFAULT 0,
                    iban VARCHAR(34),
                    created_date TIMESTAMP,
                    modified_date TIMESTAMP
                )
            ''')
            
            # Table de l'historique des actions
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS action_history (
                    id SERIAL PRIMARY KEY,
                    action VARCHAR(255) NOT NULL,
                    data JSONB NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    user_id VARCHAR(255) DEFAULT 'default'
                )
            ''')
            
            # Table des employés (pour le module RH)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id VARCHAR(255) PRIMARY KEY,
                    first_name VARCHAR(255) NOT NULL,
                    last_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) UNIQUE,
                    phone VARCHAR(50),
                    position VARCHAR(255),
                    department VARCHAR(255),
                    hire_date DATE,
                    salary DECIMAL(15,2),
                    status VARCHAR(50) DEFAULT 'active',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des congés
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leaves (
                    id VARCHAR(255) PRIMARY KEY,
                    employee_id VARCHAR(255) REFERENCES employees(id),
                    leave_type VARCHAR(100) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    days_count INTEGER NOT NULL,
                    status VARCHAR(50) DEFAULT 'pending',
                    reason TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des contacts CRM
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS contacts (
                    id VARCHAR(255) PRIMARY KEY,
                    company_name VARCHAR(255),
                    contact_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255),
                    phone VARCHAR(50),
                    address TEXT,
                    contact_type VARCHAR(50),
                    status VARCHAR(50) DEFAULT 'active',
                    notes TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des KPIs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kpis (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    category VARCHAR(100),
                    current_value DECIMAL(15,2),
                    target_value DECIMAL(15,2),
                    unit VARCHAR(50),
                    period VARCHAR(50),
                    status VARCHAR(50),
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Tables pour la gestion documentaire
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS invoices_clients (
                    id VARCHAR(255) PRIMARY KEY,
                    invoice_number VARCHAR(50) UNIQUE NOT NULL,
                    client_id VARCHAR(255),
                    client_name VARCHAR(255) NOT NULL,
                    invoice_date DATE NOT NULL,
                    due_date DATE,
                    amount_ht DECIMAL(15,2) NOT NULL,
                    amount_tva DECIMAL(15,2) DEFAULT 0,
                    amount_ttc DECIMAL(15,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    payment_method VARCHAR(50),
                    description TEXT,
                    file_path VARCHAR(500),
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS invoices_suppliers (
                    id VARCHAR(255) PRIMARY KEY,
                    invoice_number VARCHAR(50) NOT NULL,
                    supplier_id VARCHAR(255),
                    supplier_name VARCHAR(255) NOT NULL,
                    invoice_date DATE NOT NULL,
                    due_date DATE,
                    amount_ht DECIMAL(15,2) NOT NULL,
                    amount_tva DECIMAL(15,2) DEFAULT 0,
                    amount_ttc DECIMAL(15,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'received',
                    payment_method VARCHAR(50),
                    description TEXT,
                    file_path VARCHAR(500),
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_orders (
                    id VARCHAR(255) PRIMARY KEY,
                    order_number VARCHAR(50) UNIQUE NOT NULL,
                    supplier_id VARCHAR(255),
                    supplier_name VARCHAR(255) NOT NULL,
                    order_date DATE NOT NULL,
                    expected_delivery DATE,
                    amount_ht DECIMAL(15,2) NOT NULL,
                    amount_tva DECIMAL(15,2) DEFAULT 0,
                    amount_ttc DECIMAL(15,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    description TEXT,
                    file_path VARCHAR(500),
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS quotes (
                    id VARCHAR(255) PRIMARY KEY,
                    quote_number VARCHAR(50) UNIQUE NOT NULL,
                    client_id VARCHAR(255),
                    client_name VARCHAR(255) NOT NULL,
                    quote_date DATE NOT NULL,
                    validity_date DATE,
                    amount_ht DECIMAL(15,2) NOT NULL,
                    amount_tva DECIMAL(15,2) DEFAULT 0,
                    amount_ttc DECIMAL(15,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'draft',
                    description TEXT,
                    file_path VARCHAR(500),
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS delivery_notes (
                    id VARCHAR(255) PRIMARY KEY,
                    delivery_number VARCHAR(50) UNIQUE NOT NULL,
                    client_id VARCHAR(255),
                    client_name VARCHAR(255) NOT NULL,
                    delivery_date DATE NOT NULL,
                    order_reference VARCHAR(50),
                    description TEXT,
                    file_path VARCHAR(500),
                    status VARCHAR(20) DEFAULT 'delivered',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Index pour la recherche documentaire
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_clients_date ON invoices_clients(invoice_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_clients_number ON invoices_clients(invoice_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_clients_client ON invoices_clients(client_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_date ON invoices_suppliers(invoice_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_number ON invoices_suppliers(invoice_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_supplier ON invoices_suppliers(supplier_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_purchase_orders_number ON purchase_orders(order_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_quotes_date ON quotes(quote_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_quotes_number ON quotes(quote_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_delivery_notes_date ON delivery_notes(delivery_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_delivery_notes_number ON delivery_notes(delivery_number)')

            conn.commit()
            cursor.close()
            conn.close()
            print(f"✅ Base de données PostgreSQL initialisée : erp_hub")
            
        except Exception as e:
            print(f"❌ Erreur initialisation PostgreSQL : {e}")
    
    # ===== OPÉRATIONS CRUD BUDGETS =====
    
    def create_budget(self, budget_data: Dict) -> bool:
        """Créer un nouveau budget"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO budgets (
                    id, category_name, category_type, cost_center, cost_center_name,
                    analytic_code, analytic_code_name, responsible, department, notes,
                    forecast, realized, monthly_data, created_date, modified_date
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                budget_data['id'],
                budget_data['categoryName'],
                budget_data['categoryType'],
                budget_data.get('costCenter', ''),
                budget_data.get('costCenterName', ''),
                budget_data.get('analyticCode', ''),
                budget_data.get('analyticCodeName', ''),
                budget_data.get('responsible', ''),
                budget_data.get('department', ''),
                budget_data.get('notes', ''),
                budget_data.get('forecast', 0),
                budget_data.get('realized', 0),
                json.dumps(budget_data.get('monthlyData', {})),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            print(f"✅ Budget créé dans PostgreSQL : {budget_data['id']}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création budget PostgreSQL : {e}")
            return False
    
    def read_budgets(self) -> List[Dict]:
        """Lire tous les budgets"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute('SELECT * FROM budgets ORDER BY created_date DESC')
            rows = cursor.fetchall()
            
            budgets = []
            for row in rows:
                budget = {
                    'id': row['id'],
                    'categoryName': row['category_name'],
                    'categoryType': row['category_type'],
                    'costCenter': row['cost_center'],
                    'costCenterName': row['cost_center_name'],
                    'analyticCode': row['analytic_code'],
                    'analyticCodeName': row['analytic_code_name'],
                    'responsible': row['responsible'],
                    'department': row['department'],
                    'notes': row['notes'],
                    'forecast': float(row['forecast']) if row['forecast'] else 0,
                    'realized': float(row['realized']) if row['realized'] else 0,
                    'monthlyData': row['monthly_data'] if row['monthly_data'] else {},
                    'priority': row.get('priority', 'medium'),  # Nouvelle colonne priority
                    'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                    'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
                }
                budgets.append(budget)
            
            cursor.close()
            conn.close()
            print(f"✅ {len(budgets)} budgets chargés depuis PostgreSQL")
            return budgets
            
        except Exception as e:
            print(f"❌ Erreur lecture budgets PostgreSQL : {e}")
            return []
    
    def update_budget(self, budget_id: str, budget_data: Dict) -> bool:
        """Mettre à jour un budget"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE budgets SET
                    category_name = %s, category_type = %s, cost_center = %s, cost_center_name = %s,
                    analytic_code = %s, analytic_code_name = %s, responsible = %s, department = %s,
                    notes = %s, forecast = %s, realized = %s, monthly_data = %s, modified_date = %s
                WHERE id = %s
            ''', (
                budget_data['categoryName'],
                budget_data['categoryType'],
                budget_data.get('costCenter', ''),
                budget_data.get('costCenterName', ''),
                budget_data.get('analyticCode', ''),
                budget_data.get('analyticCodeName', ''),
                budget_data.get('responsible', ''),
                budget_data.get('department', ''),
                budget_data.get('notes', ''),
                budget_data.get('forecast', 0),
                budget_data.get('realized', 0),
                json.dumps(budget_data.get('monthlyData', {})),
                datetime.now(),
                budget_id
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            print(f"✅ Budget mis à jour dans PostgreSQL : {budget_id}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur mise à jour budget PostgreSQL : {e}")
            return False
    
    def delete_budget(self, budget_id: str) -> bool:
        """Supprimer un budget"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM budgets WHERE id = %s', (budget_id,))
            
            conn.commit()
            cursor.close()
            conn.close()
            print(f"✅ Budget supprimé de PostgreSQL : {budget_id}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur suppression budget PostgreSQL : {e}")
            return False
    
    # ===== HISTORIQUE DES ACTIONS =====
    
    def save_action_history(self, action: str, data: Dict, user_id: str = 'default') -> bool:
        """Sauvegarder une action dans l'historique"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO action_history (action, data, timestamp, user_id)
                VALUES (%s, %s, %s, %s)
            ''', (action, json.dumps(data), datetime.now(), user_id))
            
            conn.commit()
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde historique PostgreSQL : {e}")
            return False
    
    # ===== UTILITAIRES =====
    
    def get_database_stats(self) -> Dict:
        """Obtenir les statistiques de la base de données"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM budgets')
            budget_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM movements')
            movement_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM accounts')
            account_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM action_history')
            history_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM employees')
            employee_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM contacts')
            contact_count = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            return {
                'budgets': budget_count,
                'movements': movement_count,
                'accounts': account_count,
                'history_actions': history_count,
                'employees': employee_count,
                'contacts': contact_count,
                'database_type': 'PostgreSQL'
            }
            
        except Exception as e:
            print(f"❌ Erreur statistiques PostgreSQL : {e}")
            return {'database_type': 'PostgreSQL (erreur)'}
