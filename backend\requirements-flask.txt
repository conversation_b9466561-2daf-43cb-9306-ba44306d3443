# 📦 DÉPENDANCES PYTHON POUR API ERP HUB POSTGRESQL
# Version production avec sécurité et performance

# Framework web
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3

# Base de données PostgreSQL
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5

# Sécurité et authentification
bcrypt==4.0.1
cryptography==41.0.4
PyJWT==2.8.0

# Validation et sérialisation
marshmallow==3.20.1
Flask-Marshmallow==0.15.0

# Monitoring et logs
prometheus-client==0.17.1
structlog==23.1.0

# Cache et sessions
redis==4.6.0
Flask-Session==0.5.0

# Utilitaires
python-dotenv==1.0.0
requests==2.31.0
python-dateutil==2.8.2

# Production WSGI
gunicorn==21.2.0
gevent==23.7.0

# Intelligence Artificielle
openai==1.3.0
scikit-learn==1.3.0
pandas==2.0.3
numpy==1.24.3

# Développement et tests (optionnel)
pytest==7.4.2
pytest-flask==1.2.0
coverage==7.3.1
