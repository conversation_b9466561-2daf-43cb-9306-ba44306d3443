import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { fileURLToPath, URL } from 'node:url'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],

  // Configuration du serveur de développement
  server: {
    host: '0.0.0.0',
    port: 5173,
    open: true,
    cors: true,
    hmr: {
      port: 24678,
      host: 'localhost'
    },
    watch: {
      usePolling: true,
      interval: 1000
    }
  },

  // Configuration des alias de chemins
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@/components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@/pages': fileURLToPath(new URL('./src/pages', import.meta.url)),
      '@/agents': fileURLToPath(new URL('./src/agents', import.meta.url)),
      '@/modules': fileURLToPath(new URL('./src/modules', import.meta.url)),
      '@/services': fileURLToPath(new URL('./src/services', import.meta.url)),
      '@/utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
      '@/hooks': fileURLToPath(new URL('./src/hooks', import.meta.url)),
      '@/store': fileURLToPath(new URL('./src/store', import.meta.url)),
      '@/assets': fileURLToPath(new URL('./src/assets', import.meta.url))
    }
  },

  // Configuration du build optimisée pour la scalabilité
  build: {
    outDir: 'dist',
    sourcemap: true,
    target: 'esnext',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          // Séparation des chunks pour un meilleur caching
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material', '@mui/x-charts', '@mui/x-data-grid'],
          router: ['react-router-dom'],
          query: ['react-query'],
          ui: ['@headlessui/react', '@heroicons/react'],
          charts: ['recharts', 'chart.js', 'react-chartjs-2'],
          utils: ['axios', 'date-fns', 'clsx', 'lodash'],
          // Chunks spécifiques aux agents ERP
          agents: ['./src/pages/agents'],
          services: ['./src/services'],
        }
      }
    },
    // Optimisations pour les gros projets
    chunkSizeWarningLimit: 1000,
    assetsInlineLimit: 4096,
  },

  // Optimisations pour le développement
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'react-router-dom',
      'react-query'
    ],
    exclude: ['@vite/client', '@vite/env']
  },

  // Configuration des variables d'environnement
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  },

  // Configuration CSS
  css: {
    postcss: './postcss.config.js'
  }
})
