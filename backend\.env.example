# Configuration ERP HUB - Intelligence Artificielle
# Copiez ce fichier vers .env et remplissez vos clés API

# OpenAI API Key (optionnel - pour l'assistant conversationnel avancé)
# Obtenez votre clé sur: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Configuration Base de Données PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=erp_hub
DB_USER=erp_admin
DB_PASSWORD=erp_secure_password

# Configuration Serveur
FLASK_ENV=development
FLASK_DEBUG=True
API_PORT=5000

# Sécurité
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# Logs
LOG_LEVEL=INFO
LOG_FILE=erp_hub.log

# IA Configuration
AI_ENABLED=True
AI_FALLBACK_MODE=True
AI_CACHE_DURATION=300  # 5 minutes

# Limites API
API_RATE_LIMIT=100  # requêtes par minute
AI_RATE_LIMIT=20    # requêtes IA par minute

# Notifications
EMAIL_ENABLED=False
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Monitoring
MONITORING_ENABLED=True
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_INTERVAL=30  # secondes
