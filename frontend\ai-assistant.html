<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👨‍💼 Expert Contrôle de Gestion IA - ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #1f2937;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea 30%, #764ba2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: #1f2937;
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .ai-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            height: calc(100vh - 120px);
        }
        
        .chat-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .chat-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .chat-subtitle {
            opacity: 0.9;
            font-size: 0.875rem;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .message {
            max-width: 80%;
            padding: 1rem;
            border-radius: 1rem;
            word-wrap: break-word;
        }
        
        .message.user {
            align-self: flex-end;
            background: #667eea;
            color: white;
            border-bottom-right-radius: 0.25rem;
        }
        
        .message.ai {
            align-self: flex-start;
            background: #f3f4f6;
            color: #1f2937;
            border-bottom-left-radius: 0.25rem;
        }
        
        .message-time {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 0.5rem;
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 0.5rem;
        }
        
        .input-field {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-field:focus {
            border-color: #667eea;
        }
        
        .send-btn {
            padding: 0.75rem 1rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .send-btn:hover {
            background: #5a67d8;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .insights-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem;
            overflow-y: auto;
        }
        
        .panel-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .insight-card {
            background: rgba(255, 255, 255, 0.7);
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .insight-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .insight-content {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .quick-btn {
            padding: 0.5rem;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.75rem;
            text-align: center;
            transition: all 0.3s;
        }
        
        .quick-btn:hover {
            background: rgba(102, 126, 234, 0.2);
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .status-dot.disconnected {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .ai-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">👨‍💼 Expert Contrôle de Gestion IA - ERP HUB</div>
        <div class="nav-buttons">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Connexion...</span>
            </div>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
            <a href="finance-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">account_balance</span>
                Finance
            </a>
            <a href="rh-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">people</span>
                RH
            </a>
            <a href="crm-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">contacts</span>
                CRM
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="ai-container">
            <!-- Section Chat -->
            <div class="chat-section">
                <div class="chat-header">
                    <h1 class="chat-title">Assistant IA ERP</h1>
                    <p class="chat-subtitle">Posez vos questions sur vos données d'entreprise</p>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai">
                        <div>👋 Bonjour ! Je suis votre assistant IA pour l'ERP HUB. Comment puis-je vous aider aujourd'hui ?</div>
                        <div class="message-time" id="welcomeTime"></div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <input type="text" class="input-field" id="messageInput" placeholder="Tapez votre message..." maxlength="500">
                    <button class="send-btn" id="sendBtn">
                        <span class="material-icons">send</span>
                    </button>
                </div>
            </div>
            
            <!-- Panel Insights -->
            <div class="insights-panel">
                <h2 class="panel-title">👨‍💼 Expert Contrôle de Gestion</h2>
                
                <div class="quick-actions">
                    <button class="quick-btn" onclick="analyzeVariances()">⚖️ Analyse des Écarts</button>
                    <button class="quick-btn" onclick="askQuickQuestion('Analyse de performance budgétaire')">📊 Performance Budgétaire</button>
                    <button class="quick-btn" onclick="askQuickQuestion('Tableau de bord de gestion')">📈 Tableau de Bord</button>
                    <button class="quick-btn" onclick="askQuickQuestion('Prévisions et tendances')">🔮 Prévisions</button>
                    <button class="quick-btn" onclick="askQuickQuestion('Recommandations stratégiques')">💡 Recommandations</button>
                    <button class="quick-btn" onclick="openDocumentSearch()">📄 Recherche Documentaire</button>
                </div>
                
                <div id="insightsContainer">
                    <div class="insight-card">
                        <div class="insight-title">🤖 IA Initialisée</div>
                        <div class="insight-content">L'assistant IA est prêt à analyser vos données ERP.</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        let isConnected = false;
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeChat();
            checkConnection();
            loadInitialInsights();
            
            // Event listeners
            document.getElementById('sendBtn').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // Afficher l'heure de bienvenue
            document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();
        });
        
        // Vérifier la connexion API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    updateConnectionStatus(true);
                } else {
                    updateConnectionStatus(false);
                }
            } catch (error) {
                updateConnectionStatus(false);
            }
        }
        
        // Mettre à jour le statut de connexion
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (connected) {
                statusDot.className = 'status-dot';
                statusText.textContent = 'IA Connectée';
            } else {
                statusDot.className = 'status-dot disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }
        
        // Initialiser le chat
        function initializeChat() {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Envoyer un message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            // Désactiver le bouton d'envoi
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';
            
            try {
                // Envoyer à l'IA
                const response = await fetch(`${API_BASE_URL}/ai/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addMessage(data.response, 'ai');
                    
                    // Mettre à jour les insights si pertinent
                    if (message.toLowerCase().includes('budget') || message.toLowerCase().includes('kpi')) {
                        loadRelevantInsights(message);
                    }
                } else {
                    addMessage('Désolé, je n\'ai pas pu traiter votre demande. Erreur: ' + data.error, 'ai');
                }
                
            } catch (error) {
                addMessage('Erreur de connexion. Veuillez réessayer.', 'ai');
            }
            
            // Réactiver le bouton
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<span class="material-icons">send</span>';
        }
        
        // Ajouter un message au chat
        function addMessage(content, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div>${content}</div>
                <div class="message-time">${time}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Question rapide
        function askQuickQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        // Fonction pour analyser les écarts budgétaires (expertise contrôle de gestion)
        async function analyzeVariances() {
            try {
                showTyping();

                const response = await fetch('http://localhost:5000/api/ai/analyze-variances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                hideTyping();

                if (data.success) {
                    displayVarianceAnalysis(data.expert_analysis);
                } else {
                    addMessage('system', `❌ Erreur: ${data.error}`);
                }
            } catch (error) {
                hideTyping();
                addMessage('system', '❌ Erreur de connexion au serveur');
            }
        }

        // Afficher l'analyse des écarts de manière structurée
        function displayVarianceAnalysis(analysis) {
            let message = `👨‍💼 **ANALYSE EXPERTE DES ÉCARTS BUDGÉTAIRES**\n\n`;

            // Profil du contrôleur
            message += `**🎯 CONTRÔLEUR DE GESTION :**\n`;
            message += `• ${analysis.controller_profile.role}\n`;
            message += `• ${analysis.controller_profile.experience}\n`;
            message += `• Analyse du ${analysis.controller_profile.analysis_date}\n\n`;

            // Synthèse globale
            const summary = analysis.global_summary;
            message += `**📊 SYNTHÈSE GLOBALE :**\n`;
            message += `• Prévisionnel total : ${summary.total_forecast.toLocaleString()} €\n`;
            message += `• Réalisé total : ${summary.total_realized.toLocaleString()} €\n`;
            message += `• Écart global : ${summary.global_variance_amount.toLocaleString()} € (${summary.global_variance_rate:+.1f}%)\n`;
            message += `• Taux de réalisation : ${summary.realization_rate.toFixed(1)}%\n\n`;

            // Alertes
            const alerts = analysis.alerts;
            if (alerts.critical_variances > 0) {
                message += `**🚨 ALERTES CRITIQUES :**\n`;
                message += `• ${alerts.critical_variances} écart(s) critique(s) détecté(s)\n`;
                alerts.critical_details.forEach(variance => {
                    message += `• ${variance.category}: ${variance.variance_percentage:+.1f}% (${variance.variance_type})\n`;
                });
                message += `\n`;
            }

            if (alerts.significant_variances > 0) {
                message += `**⚠️ ÉCARTS SIGNIFICATIFS :**\n`;
                message += `• ${alerts.significant_variances} écart(s) significatif(s)\n\n`;
            }

            // Recommandations expertes
            message += `**💡 RECOMMANDATIONS EXPERTES :**\n`;
            analysis.expert_recommendations.forEach(rec => {
                message += `${rec}\n`;
            });
            message += `\n`;

            // Actions correctives
            message += `**🎯 PLAN D'ACTIONS CORRECTIVES :**\n`;
            analysis.next_actions.forEach(action => {
                message += `${action}\n`;
            });

            addMessage('ai', message);
        }

        // Fonction pour ouvrir la recherche documentaire
        function openDocumentSearch() {
            const searchHtml = `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 10px 0;">
                    <h3>🔍 Recherche Documentaire</h3>
                    <div style="margin: 15px 0;">
                        <input type="text" id="docSearchInput" placeholder="Rechercher factures, devis, bons de commande..."
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div style="display: flex; gap: 10px; margin: 10px 0;">
                        <button onclick="searchDocuments()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                            🔍 Rechercher
                        </button>
                        <button onclick="getDocumentStats()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                            📊 Statistiques
                        </button>
                    </div>
                    <div id="searchResults"></div>
                </div>
            `;

            addMessage('system', searchHtml);

            // Focus sur le champ de recherche
            setTimeout(() => {
                const input = document.getElementById('docSearchInput');
                if (input) input.focus();
            }, 100);
        }

        // Fonction de recherche documentaire
        async function searchDocuments() {
            const query = document.getElementById('docSearchInput').value;
            if (!query.trim()) return;

            try {
                const response = await fetch('http://localhost:5000/api/documents/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query: query })
                });

                const data = await response.json();

                if (data.success) {
                    displaySearchResults(data);
                } else {
                    document.getElementById('searchResults').innerHTML = `<p style="color: red;">❌ ${data.error}</p>`;
                }
            } catch (error) {
                document.getElementById('searchResults').innerHTML = `<p style="color: red;">❌ Erreur de connexion</p>`;
            }
        }

        // Afficher les résultats de recherche
        function displaySearchResults(data) {
            const resultsDiv = document.getElementById('searchResults');

            if (data.total_found === 0) {
                resultsDiv.innerHTML = `<p>Aucun document trouvé pour "${data.query}"</p>`;
                return;
            }

            let html = `<h4>📄 ${data.total_found} document(s) trouvé(s)</h4>`;

            Object.entries(data.results).forEach(([type, typeData]) => {
                html += `
                    <div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <h5>${typeData.icon} ${typeData.name} (${typeData.count})</h5>
                `;

                typeData.documents.slice(0, 3).forEach(doc => {
                    html += `
                        <div style="margin: 5px 0; padding: 8px; background: white; border-radius: 3px;">
                            <strong>${doc.invoice_number || doc.order_number || doc.quote_number || doc.delivery_number}</strong><br>
                            <small>${doc.client_name || doc.supplier_name} - ${doc.amount_ttc_formatted || 'N/A'}</small>
                        </div>
                    `;
                });

                if (typeData.count > 3) {
                    html += `<small>... et ${typeData.count - 3} autre(s)</small>`;
                }

                html += `</div>`;
            });

            resultsDiv.innerHTML = html;
        }

        // Obtenir les statistiques des documents
        async function getDocumentStats() {
            try {
                const response = await fetch('http://localhost:5000/api/documents/statistics');
                const data = await response.json();

                if (data.success) {
                    let message = `📊 **STATISTIQUES DOCUMENTAIRES**\n\n`;

                    Object.entries(data.statistics).forEach(([type, stats]) => {
                        message += `**${stats.icon} ${stats.name} :**\n`;
                        message += `• Nombre : ${stats.count}\n`;
                        message += `• Montant total : ${stats.total_amount_formatted}\n\n`;
                    });

                    addMessage('ai', message);
                } else {
                    addMessage('system', `❌ Erreur: ${data.error}`);
                }
            } catch (error) {
                addMessage('system', '❌ Erreur de connexion au serveur');
            }
        }
        
        // Charger les insights initiaux
        async function loadInitialInsights() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai/insights`);
                const data = await response.json();
                
                if (data.success) {
                    displayInsights(data);
                }
            } catch (error) {
                console.error('Erreur chargement insights:', error);
            }
        }
        
        // Charger des insights pertinents
        async function loadRelevantInsights(message) {
            try {
                if (message.toLowerCase().includes('budget')) {
                    const response = await fetch(`${API_BASE_URL}/ai/predict-budgets`, {
                        method: 'POST'
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        addInsightCard('💰 Prédictions Budgétaires', 
                            `Taux de réalisation global: ${data.summary?.realization_rate}%`);
                    }
                }
                
                if (message.toLowerCase().includes('kpi')) {
                    const response = await fetch(`${API_BASE_URL}/ai/analyze-kpis`, {
                        method: 'POST'
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        addInsightCard('📈 Analyse KPIs', 
                            `Performance moyenne: ${data.summary?.avg_performance}%`);
                    }
                }
            } catch (error) {
                console.error('Erreur insights:', error);
            }
        }
        
        // Afficher les insights
        function displayInsights(data) {
            const container = document.getElementById('insightsContainer');
            
            if (data.budget_insights && data.budget_insights.length > 0) {
                data.budget_insights.forEach(insight => {
                    addInsightCard('💡 Insight Budget', insight);
                });
            }
            
            if (data.anomalies && data.anomalies.length > 0) {
                addInsightCard('⚠️ Anomalies Détectées', 
                    `${data.anomalies.length} anomalie(s) trouvée(s)`);
            }
        }
        
        // Ajouter une carte insight
        function addInsightCard(title, content) {
            const container = document.getElementById('insightsContainer');
            const card = document.createElement('div');
            card.className = 'insight-card';
            card.innerHTML = `
                <div class="insight-title">${title}</div>
                <div class="insight-content">${content}</div>
            `;
            container.appendChild(card);
        }
        
        // Vérifier la connexion périodiquement
        setInterval(checkConnection, 30000);
    </script>
</body>
</html>
