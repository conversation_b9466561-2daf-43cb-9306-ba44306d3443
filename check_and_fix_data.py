#!/usr/bin/env python3
"""
Script pour vérifier et corriger les données existantes
"""

import psycopg2
import sys
from datetime import datetime, date

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'erp_hub',
    'user': 'erp_admin',
    'password': 'erp_secure_2024'
}

def check_and_fix_data():
    """Vérifier et corriger les données existantes"""
    
    try:
        # Connexion à la base de données
        print("🔗 Connexion à PostgreSQL...")
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. Vérifier les employés existants
        print("👥 Vérification des employés existants...")
        cursor.execute("SELECT id, first_name, last_name FROM employees ORDER BY id")
        employees = cursor.fetchall()
        
        print(f"Employés trouvés ({len(employees)}) :")
        employee_ids = []
        for emp in employees:
            print(f"   - ID: {emp[0]}, Nom: {emp[1]} {emp[2]}")
            employee_ids.append(emp[0])
        
        if len(employee_ids) < 3:
            print("❌ Pas assez d'employés. Ajout d'employés supplémentaires...")
            
            # Ajouter des employés manquants
            new_employees = [
                ('emp-001', 'Marie', 'Martin', '<EMAIL>', '+33 1 23 45 67 89', 'Directrice Générale', 'Direction', date(2020, 1, 15), 75000.00, 'active'),
                ('emp-002', 'Pierre', 'Durand', '<EMAIL>', '+33 1 98 76 54 32', 'Responsable IT', 'Informatique', date(2021, 3, 10), 55000.00, 'active'),
                ('emp-003', 'Sophie', 'Leroy', '<EMAIL>', '+33 1 11 22 33 44', 'Chef de Projet', 'Projets', date(2022, 6, 1), 48000.00, 'active')
            ]
            
            for emp in new_employees:
                if emp[0] not in employee_ids:
                    cursor.execute("""
                        INSERT INTO employees (id, first_name, last_name, email, phone, position, department, hire_date, salary, status, created_date, modified_date)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (id) DO NOTHING
                    """, emp + (datetime.now(), datetime.now()))
                    print(f"   ✅ Employé ajouté: {emp[1]} {emp[2]} ({emp[0]})")
                    employee_ids.append(emp[0])
        
        # Utiliser les IDs d'employés existants
        emp_id_1 = employee_ids[0] if len(employee_ids) > 0 else 'emp-001'
        emp_id_2 = employee_ids[1] if len(employee_ids) > 1 else 'emp-002'
        emp_id_3 = employee_ids[2] if len(employee_ids) > 2 else 'emp-003'
        
        print(f"\n📊 Utilisation des IDs d'employés: {emp_id_1}, {emp_id_2}, {emp_id_3}")
        
        # 2. Insérer les données avec les bons IDs
        print("\n📊 Insertion des données de test...")
        
        # Clients
        print("👥 Insertion des clients...")
        customers_data = [
            ('cust-001', 'CLI001', 'TechCorp Solutions', 'Marie Dubois', '<EMAIL>', '+33 1 23 45 67 89', '123 Avenue des Champs', 'Paris', '75008', 'France', 'Technologie', 'client', 150000.00, 30, emp_id_1, 'active'),
            ('cust-002', 'CLI002', 'InnovateX', 'Jean Martin', '<EMAIL>', '+33 1 98 76 54 32', '456 Rue de la Paix', 'Lyon', '69000', 'France', 'Innovation', 'client', 100000.00, 45, emp_id_2, 'active'),
            ('cust-003', 'CLI003', 'DataFlow Systems', 'Sophie Laurent', '<EMAIL>', '+33 1 11 22 33 44', '789 Boulevard Tech', 'Marseille', '13000', 'France', 'Data Analytics', 'prospect', 75000.00, 30, emp_id_3, 'active')
        ]
        
        for customer in customers_data:
            cursor.execute("""
                INSERT INTO customers (id, customer_code, company_name, contact_person, email, phone, address, city, postal_code, country, industry, customer_type, credit_limit, payment_terms, sales_rep_id, status, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, customer + (datetime.now(), datetime.now()))
        
        # Fournisseurs
        print("🏭 Insertion des fournisseurs...")
        suppliers_data = [
            ('sup-001', 'FOUR001', 'TechSupply Pro', 'Michel Leroy', '<EMAIL>', '+33 1 44 55 66 77', '100 Rue de la Tech', 'Paris', '75001', 'France', 'Matériel informatique', 30, 'A+', True, 'active'),
            ('sup-002', 'FOUR002', 'Software Solutions', 'Claire Petit', '<EMAIL>', '+33 1 33 44 55 66', '200 Avenue du Code', 'Lyon', '69001', 'France', 'Logiciels', 45, 'A', True, 'active'),
            ('sup-003', 'FOUR003', 'CloudServices Inc', 'David Wilson', '<EMAIL>', '****** 123 4567', '300 Cloud Street', 'San Francisco', '94105', 'USA', 'Services Cloud', 60, 'A+', False, 'active')
        ]
        
        for supplier in suppliers_data:
            cursor.execute("""
                INSERT INTO suppliers (id, supplier_code, company_name, contact_person, email, phone, address, city, postal_code, country, industry, payment_terms, credit_rating, preferred, status, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, supplier + (datetime.now(), datetime.now()))
        
        # Entrepôts
        print("🏢 Insertion des entrepôts...")
        warehouses_data = [
            ('wh-001', 'WH001', 'Entrepôt Central Paris', '500 Zone Industrielle Nord', 'Roissy', '95700', 'France', emp_id_1, 10000.00, 65.5, 'main', 'active'),
            ('wh-002', 'WH002', 'Dépôt Lyon', '250 Zone Logistique Sud', 'Lyon', '69007', 'France', emp_id_2, 5000.00, 45.2, 'regional', 'active'),
            ('wh-003', 'WH003', 'Centre Distribution Marseille', '150 Port de Commerce', 'Marseille', '13016', 'France', emp_id_3, 3000.00, 78.9, 'distribution', 'active')
        ]
        
        for warehouse in warehouses_data:
            cursor.execute("""
                INSERT INTO warehouses (id, warehouse_code, name, address, city, postal_code, country, manager_id, capacity, current_utilization, warehouse_type, status, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, warehouse + (datetime.now(), datetime.now()))
        
        # Produits
        print("📦 Insertion des produits...")
        products_data = [
            ('prod-001', 'PROD001', 'Serveur Dell PowerEdge', 'Serveur haute performance pour datacenter', 'Serveurs', 'Dell', 'Unité', 25.5, '43x68x8 cm', 2500.00, 3500.00, 5, 50, 10, 'sup-001', '1234567890123', 'active'),
            ('prod-002', 'PROD002', 'Licence Office 365', 'Suite bureautique Microsoft Office 365 Business', 'Logiciels', 'Microsoft', 'Licence', 0.0, 'Numérique', 120.00, 180.00, 100, 1000, 200, 'sup-002', '2345678901234', 'active'),
            ('prod-003', 'PROD003', 'Switch Cisco 24 ports', 'Commutateur réseau 24 ports Gigabit', 'Réseau', 'Cisco', 'Unité', 2.8, '44x25x4 cm', 450.00, 650.00, 10, 100, 20, 'sup-001', '3456789012345', 'active'),
            ('prod-004', 'PROD004', 'Service Cloud AWS', 'Crédit de service cloud computing AWS', 'Services', 'Amazon', 'Crédit', 0.0, 'Numérique', 800.00, 1200.00, 50, 500, 100, 'sup-003', '4567890123456', 'active')
        ]
        
        for product in products_data:
            cursor.execute("""
                INSERT INTO products (id, product_code, name, description, category, brand, unit_of_measure, weight, dimensions, cost_price, selling_price, min_stock_level, max_stock_level, reorder_point, supplier_id, barcode, status, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, product + (datetime.now(), datetime.now()))
        
        # Inventaire
        print("📊 Insertion de l'inventaire...")
        inventory_data = [
            ('inv-001', 'prod-001', 'wh-001', 25, 5, date(2024, 6, 1), datetime.now(), 'A-01-15', 'BATCH001', None),
            ('inv-002', 'prod-002', 'wh-001', 500, 50, date(2024, 6, 1), datetime.now(), 'B-02-10', 'LIC2024', date(2025, 12, 31)),
            ('inv-003', 'prod-003', 'wh-002', 45, 10, date(2024, 6, 1), datetime.now(), 'C-01-05', 'NET001', None),
            ('inv-004', 'prod-004', 'wh-001', 200, 25, date(2024, 6, 1), datetime.now(), 'D-03-20', 'CLOUD24', date(2024, 12, 31))
        ]
        
        for inventory in inventory_data:
            cursor.execute("""
                INSERT INTO inventory (id, product_id, warehouse_id, quantity_on_hand, quantity_reserved, last_count_date, last_movement_date, location, batch_number, expiry_date, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, inventory + (datetime.now(), datetime.now()))
        
        # Opportunités
        print("💼 Insertion des opportunités...")
        opportunities_data = [
            ('opp-001', 'cust-001', 'Migration ERP Cloud', 'Migration complète vers solution ERP cloud avec formation équipe', 120000.00, 85, 'negotiation', date(2024, 7, 15), None, emp_id_1, 'Référence client', 'SAP', 'Finaliser contrat et conditions'),
            ('opp-002', 'cust-002', 'Système CRM Intégré', 'Implémentation CRM avec intégration API existante', 85000.00, 70, 'proposal', date(2024, 7, 30), None, emp_id_2, 'Site web', 'Salesforce', 'Présentation solution technique'),
            ('opp-003', 'cust-003', 'Analytics Dashboard', 'Tableau de bord analytics temps réel pour données business', 45000.00, 60, 'qualification', date(2024, 8, 15), None, emp_id_3, 'Salon professionnel', 'Tableau', 'Démonstration produit')
        ]
        
        for opportunity in opportunities_data:
            cursor.execute("""
                INSERT INTO opportunities (id, customer_id, title, description, value, probability, stage, expected_close_date, actual_close_date, sales_rep_id, source, competitor, next_action, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, opportunity + (datetime.now(), datetime.now()))
        
        # Commandes
        print("📋 Insertion des commandes...")
        orders_data = [
            ('ord-001', 'CMD-2024-001', 'cust-001', None, date(2024, 5, 28), date(2024, 7, 15), 'confirmed', 25000.00, 5000.00, 0.00, '123 Avenue des Champs, Paris 75008', '123 Avenue des Champs, Paris 75008', 'Virement', 'paid', emp_id_1, 'Formation confirmée pour juillet'),
            ('ord-002', 'CMD-2024-002', 'cust-002', None, date(2024, 6, 5), date(2024, 6, 20), 'shipped', 32000.00, 6400.00, 2000.00, '456 Rue de la Paix, Lyon 69000', '456 Rue de la Paix, Lyon 69000', 'Carte bancaire', 'paid', emp_id_2, 'Livraison express demandée')
        ]
        
        for order in orders_data:
            cursor.execute("""
                INSERT INTO orders (id, order_number, customer_id, opportunity_id, order_date, delivery_date, status, total_amount, tax_amount, discount_amount, shipping_address, billing_address, payment_method, payment_status, sales_rep_id, notes, created_date, modified_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, order + (datetime.now(), datetime.now()))
        
        # Valider les changements
        conn.commit()
        
        # Vérifier les données insérées
        print("\n📊 Vérification des données insérées...")
        
        tables_to_check = ['customers', 'suppliers', 'warehouses', 'products', 'inventory', 'opportunities', 'orders']
        
        for table in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   - {table}: {count} enregistrements")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Données de test insérées avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'insertion des données: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Vérification et insertion des données de test ERP HUB...")
    success = check_and_fix_data()
    
    if success:
        print("\n✅ Toutes les données de test sont maintenant disponibles !")
        print("🔗 Vous pouvez maintenant tester les endpoints API avec des données réelles.")
    else:
        print("\n❌ Échec de l'insertion des données.")
        sys.exit(1)
