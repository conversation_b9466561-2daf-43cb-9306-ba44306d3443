@echo off
REM 🚀 DEMARRAGE RAPIDE ERP HUB
REM Script pour demarrer rapidement l'ERP HUB sans Docker

echo.
echo ========================================
echo 🚀 DEMARRAGE RAPIDE ERP HUB
echo ========================================
echo.

REM Verification Python
echo ℹ️  Verification de Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python n'est pas installe
    echo 📥 Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)
echo ✅ Python OK

REM Verification Node.js
echo ℹ️  Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Node.js non trouve, utilisation du serveur Python
) else (
    echo ✅ Node.js OK
)

REM Creation des repertoires necessaires
echo ℹ️  Creation des repertoires...
if not exist "logs" mkdir "logs"
if not exist "backups" mkdir "backups"
echo ✅ Repertoires crees

REM Installation des dependances Python si necessaire
echo ℹ️  Verification des dependances Python...
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installation de Flask...
    pip install flask flask-cors
)

python -c "import psycopg2" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installation de psycopg2...
    pip install psycopg2-binary
)

echo ✅ Dependances Python OK

REM Demarrage du serveur backend Python
echo ℹ️  Demarrage du serveur backend...
cd backend
start "ERP Backend" python api_server.py
cd ..

REM Attendre que le backend soit pret
echo ℹ️  Attente du demarrage du backend...
timeout /t 5 /nobreak >nul

REM Demarrage du serveur frontend
echo ℹ️  Demarrage du serveur frontend...

REM Aller dans le repertoire frontend
cd frontend

REM Verifier si Node.js est disponible pour un serveur plus avance
node --version >nul 2>&1
if %errorlevel% equ 0 (
    REM Utiliser http-server si disponible
    npx http-server --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 🌐 Demarrage avec http-server...
        start "ERP Frontend" npx http-server . -p 3000 -c-1 --cors
    ) else (
        echo 📦 Installation de http-server...
        npm install -g http-server
        start "ERP Frontend" npx http-server . -p 3000 -c-1 --cors
    )
) else (
    REM Utiliser le serveur Python simple
    echo 🌐 Demarrage avec serveur Python...
    start "ERP Frontend" python -m http.server 3000
)

REM Retourner au repertoire racine
cd ..

REM Attendre que le frontend soit pret
echo ℹ️  Attente du demarrage du frontend...
timeout /t 3 /nobreak >nul

REM Verification que les serveurs sont actifs
echo ℹ️  Verification des serveurs...

REM Test du backend
curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend actif sur http://localhost:5000
) else (
    echo ⚠️  Backend en cours de demarrage...
)

REM Test du frontend
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend actif sur http://localhost:3000
) else (
    echo ⚠️  Frontend en cours de demarrage...
)

echo.
echo ========================================
echo 🎉 ERP HUB DEMARRE !
echo ========================================
echo.
echo 🌐 Acces aux services :
echo    🏠 Application principale : http://localhost:3000
echo    🔧 API Backend : http://localhost:5000
echo    📊 Health Check : http://localhost:5000/api/health
echo.
echo 🔑 Identifiants par defaut :
echo    Utilisateur : admin
echo    Mot de passe : admin123
echo.
echo 🎨 Nouvelles fonctionnalites :
echo    ✅ Navigation verticale moderne
echo    ✅ Interface responsive
echo    ✅ Modules simplifies
echo    ✅ Performance optimisee
echo.
echo 📋 Prochaines etapes :
echo    1. Ouvrir http://localhost:3000 dans votre navigateur
echo    2. Tester la nouvelle interface avec sidebar
echo    3. Explorer les modules Finance, CRM, RH, Stocks
echo    4. Verifier les ameliorations de performance
echo.

REM Ouvrir automatiquement l'application
echo ℹ️  Ouverture de l'application dans le navigateur...
timeout /t 2 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ Demarrage termine avec succes !
echo.
echo 💡 Conseils :
echo    - Utilisez Ctrl+C dans les fenetres de serveur pour arreter
echo    - Rechargez la page si elle ne s'affiche pas immediatement
echo    - Consultez les logs en cas de probleme
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
