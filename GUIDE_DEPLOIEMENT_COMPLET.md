# 🚀 **GUIDE DE DÉPLOIEMENT COMPLET - ERP HUB POSTGRESQL**

## **📋 PRÉREQUIS SYSTÈME**

### **🖥️ Configuration Minimale Requise**
- **OS** : Windows 10/11, macOS 10.15+, ou Linux Ubuntu 18.04+
- **RAM** : 4 GB minimum (8 GB recommandé)
- **Stockage** : 2 GB d'espace libre
- **R<PERSON>eau** : Connexion Internet pour téléchargement des dépendances

### **🛠️ Logiciels Requis**

#### **1. Python 3.8+**
```bash
# Vérifier la version Python
python --version
# ou
python3 --version
```

#### **2. Docker & Docker Compose**
```bash
# Vérifier Docker
docker --version
docker-compose --version
```

#### **3. Git (optionnel)**
```bash
git --version
```

---

## **📦 ÉTAPE 1 : INSTALLATION DES PRÉREQUIS**

### **🐍 Installation Python (si nécessaire)**

#### **Windows :**
1. Télécharger Python depuis [python.org](https://python.org)
2. Cocher "Add Python to PATH" lors de l'installation
3. Redémarrer le terminal

#### **macOS :**
```bash
# Avec Homebrew
brew install python3
```

#### **Linux (Ubuntu/Debian) :**
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### **🐳 Installation Docker**

#### **Windows :**
1. Télécharger Docker Desktop depuis [docker.com](https://docker.com)
2. Installer et redémarrer
3. Vérifier que Docker fonctionne

#### **macOS :**
```bash
# Avec Homebrew
brew install --cask docker
```

#### **Linux (Ubuntu) :**
```bash
# Installation Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Installation Docker Compose
sudo apt install docker-compose
```

---

## **📁 ÉTAPE 2 : RÉCUPÉRATION DU PROJET**

### **Option A : Téléchargement Direct**
1. Télécharger le dossier `ERP_HUB` complet
2. Extraire dans `C:\ERP_HUB` (Windows) ou `~/ERP_HUB` (macOS/Linux)

### **Option B : Clonage Git (si disponible)**
```bash
git clone [URL_DU_REPO] ERP_HUB
cd ERP_HUB
```

### **📂 Structure des Fichiers Essentiels**
```
ERP_HUB/
├── postgresql_api_server.py          # Serveur API principal
├── docker-compose-postgresql.yml     # Configuration Docker
├── create_all_erp_tables.sql        # Script création tables
├── insert_demo_data_all_agents.sql  # Données de démonstration
├── start_erp_postgresql.bat         # Script Windows
├── start_erp_postgresql.sh          # Script Linux/Mac
├── test_all_endpoints.py            # Tests API
└── frontend/
    ├── dashboard-global-postgresql.html
    ├── hr-management-postgresql.html
    ├── sales-management-postgresql.html
    ├── purchase-management-postgresql.html
    ├── stock-management-postgresql.html
    ├── logistics-management-postgresql.html
    ├── crm-management-postgresql.html
    └── bi-management-postgresql.html
```

---

## **⚙️ ÉTAPE 3 : INSTALLATION DES DÉPENDANCES**

### **🐍 Dépendances Python**
```bash
# Naviguer vers le dossier du projet
cd ERP_HUB

# Installer les dépendances Python
pip install flask flask-cors psycopg2-binary requests python-dotenv
```

### **🔧 Vérification des Dépendances**
```bash
# Créer un fichier requirements.txt si nécessaire
echo "flask==2.3.3
flask-cors==4.0.0
psycopg2-binary==2.9.7
requests==2.31.0
python-dotenv==1.0.0" > requirements.txt

# Installer depuis requirements.txt
pip install -r requirements.txt
```

---

## **🗄️ ÉTAPE 4 : CONFIGURATION DE LA BASE DE DONNÉES**

### **🐳 Démarrage PostgreSQL avec Docker**

#### **Windows :**
```cmd
# Ouvrir PowerShell en tant qu'administrateur
cd C:\ERP_HUB
docker-compose -f docker-compose-postgresql.yml up -d
```

#### **macOS/Linux :**
```bash
cd ~/ERP_HUB
docker-compose -f docker-compose-postgresql.yml up -d
```

### **📊 Création des Tables et Données**
```bash
# Attendre 30 secondes que PostgreSQL démarre complètement
# Puis exécuter les scripts SQL

# Création des tables
docker exec erp_postgres psql -U erp_admin -d erp_hub -f /docker-entrypoint-initdb.d/create_all_erp_tables.sql

# Insertion des données de démonstration
docker exec erp_postgres psql -U erp_admin -d erp_hub -f /docker-entrypoint-initdb.d/insert_demo_data_all_agents.sql
```

### **✅ Vérification de la Base de Données**
```bash
# Vérifier que PostgreSQL fonctionne
docker ps | grep erp_postgres

# Tester la connexion
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "SELECT COUNT(*) FROM employees;"
```

---

## **🚀 ÉTAPE 5 : DÉMARRAGE DU SYSTÈME**

### **🖥️ Méthode Automatique (Recommandée)**

#### **Windows :**
```cmd
# Double-cliquer sur start_erp_postgresql.bat
# OU en ligne de commande :
start_erp_postgresql.bat
```

#### **macOS/Linux :**
```bash
# Rendre le script exécutable
chmod +x start_erp_postgresql.sh

# Exécuter le script
./start_erp_postgresql.sh
```

### **⚙️ Méthode Manuelle**

#### **1. Démarrer PostgreSQL**
```bash
docker-compose -f docker-compose-postgresql.yml up -d
```

#### **2. Démarrer l'API**
```bash
python postgresql_api_server.py
```

### **📱 Accès aux Interfaces**

Une fois le système démarré, ouvrir un navigateur web et accéder à :

- **🏠 Dashboard Global** : `file:///[CHEMIN]/ERP_HUB/frontend/dashboard-global-postgresql.html`
- **👥 Agent HR** : `file:///[CHEMIN]/ERP_HUB/frontend/hr-management-postgresql.html`
- **💼 Agent Sales** : `file:///[CHEMIN]/ERP_HUB/frontend/sales-management-postgresql.html`
- **🛒 Agent Purchase** : `file:///[CHEMIN]/ERP_HUB/frontend/purchase-management-postgresql.html`
- **📦 Agent Stock** : `file:///[CHEMIN]/ERP_HUB/frontend/stock-management-postgresql.html`
- **🚚 Agent Logistics** : `file:///[CHEMIN]/ERP_HUB/frontend/logistics-management-postgresql.html`
- **💼 Agent CRM** : `file:///[CHEMIN]/ERP_HUB/frontend/crm-management-postgresql.html`
- **📊 Agent BI** : `file:///[CHEMIN]/ERP_HUB/frontend/bi-management-postgresql.html`

---

## **🔍 ÉTAPE 6 : VÉRIFICATION DU SYSTÈME**

### **🧪 Test des Endpoints API**
```bash
# Exécuter le script de test
python test_all_endpoints.py
```

**Résultat attendu :**
```
✅ Tests réussis: 17/17
📈 Taux de succès: 100.0%
📋 Total enregistrements: 53
🎉 TOUS LES ENDPOINTS FONCTIONNENT PARFAITEMENT !
```

### **🌐 Test des Interfaces Web**
1. Ouvrir le Dashboard Global
2. Vérifier que toutes les statistiques s'affichent
3. Tester la navigation vers chaque module
4. Vérifier que les données se chargent dans les tableaux

### **📊 Données de Démonstration Incluses**
- **HR** : 8 employés + 3 congés
- **Sales** : 3 clients + 3 opportunités + 2 commandes
- **Purchase** : 3 fournisseurs + 2 bons de commande
- **Stock** : 4 produits + 4 inventaires
- **Logistics** : 3 entrepôts + 2 expéditions
- **Finance** : 3 budgets
- **CRM** : 4 contacts + 4 interactions
- **BI** : 5 KPIs

---

## **🔧 DÉPANNAGE**

### **❌ Problèmes Courants**

#### **1. Port 5000 déjà utilisé**
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID [PID] /F

# macOS/Linux
lsof -ti:5000 | xargs kill -9
```

#### **2. Docker ne démarre pas**
```bash
# Redémarrer Docker
docker system prune -f
docker-compose -f docker-compose-postgresql.yml down
docker-compose -f docker-compose-postgresql.yml up -d
```

#### **3. Erreur de connexion PostgreSQL**
```bash
# Vérifier les logs
docker logs erp_postgres

# Redémarrer PostgreSQL
docker restart erp_postgres
```

#### **4. Problème de pare-feu**
- **Windows** : Autoriser Python et Docker dans le pare-feu Windows
- **macOS** : Vérifier les préférences de sécurité
- **Linux** : Configurer ufw si nécessaire

### **🔌 Ports Utilisés**
- **5000** : API Flask
- **5432** : PostgreSQL (Docker)
- **80/443** : Interfaces web (fichiers locaux)

---

## **📱 ACCÈS RÉSEAU (OPTIONNEL)**

### **🌐 Partage sur le Réseau Local**

#### **1. Modifier l'API pour accepter les connexions réseau**
Dans `postgresql_api_server.py`, ligne finale :
```python
# Remplacer
app.run(debug=True, host='127.0.0.1', port=5000)

# Par
app.run(debug=True, host='0.0.0.0', port=5000)
```

#### **2. Modifier les URLs dans les pages HTML**
Remplacer `http://localhost:5000` par `http://[IP_DU_SERVEUR]:5000`

#### **3. Trouver l'IP du serveur**
```bash
# Windows
ipconfig | findstr IPv4

# macOS/Linux
ifconfig | grep inet
```

---

## **🎯 PROCHAINES ÉTAPES**

Une fois le système installé et fonctionnel :

1. **✅ Tester toutes les fonctionnalités**
2. **🔐 Configurer l'authentification** (si nécessaire)
3. **📊 Personnaliser les données** selon vos besoins
4. **🔄 Configurer les sauvegardes** automatiques
5. **📈 Monitorer les performances**

---

## **📞 SUPPORT**

En cas de problème :
1. Vérifier les logs : `docker logs erp_postgres`
2. Tester les endpoints : `python test_all_endpoints.py`
3. Redémarrer le système complet
4. Consulter la documentation technique

**🎉 Félicitations ! Votre système ERP HUB PostgreSQL est maintenant opérationnel !**
