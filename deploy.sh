#!/bin/bash

# Script de déploiement en production pour ERP HUB
# Usage: ./deploy.sh [environment]

set -e  # Arrêter en cas d'erreur

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="erp-hub"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deploy.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Vérification des prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    # Docker
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé"
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose n'est pas installé"
    fi
    
    # Fichier d'environnement
    if [ ! -f ".env.${ENVIRONMENT}" ]; then
        error "Fichier .env.${ENVIRONMENT} manquant"
    fi
    
    log "✅ Prérequis validés"
}

# Sauvegarde de la base de données
backup_database() {
    log "Sauvegarde de la base de données..."
    
    # Créer le répertoire de sauvegarde
    mkdir -p "$BACKUP_DIR"
    
    # Nom du fichier de sauvegarde
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Sauvegarde si le conteneur existe
    if docker ps -a --format 'table {{.Names}}' | grep -q "${PROJECT_NAME}_postgres"; then
        docker exec "${PROJECT_NAME}_postgres_prod" pg_dump -U erp_user_prod erp_hub_prod > "$BACKUP_FILE"
        log "✅ Sauvegarde créée: $BACKUP_FILE"
    else
        warning "Aucune base de données existante à sauvegarder"
    fi
}

# Arrêt des services existants
stop_services() {
    log "Arrêt des services existants..."
    
    if [ -f "docker-compose.prod.yml" ]; then
        docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" down
        log "✅ Services arrêtés"
    else
        warning "Aucun service à arrêter"
    fi
}

# Construction des images
build_images() {
    log "Construction des images Docker..."
    
    # Copier le fichier d'environnement
    cp ".env.${ENVIRONMENT}" .env
    
    # Construction avec cache
    docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" build --parallel
    
    log "✅ Images construites"
}

# Démarrage des services
start_services() {
    log "Démarrage des services..."
    
    # Démarrage en arrière-plan
    docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" up -d
    
    log "✅ Services démarrés"
}

# Migrations de base de données
run_migrations() {
    log "Exécution des migrations..."
    
    # Attendre que la base de données soit prête
    sleep 10
    
    # Migrations
    docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" exec backend python manage.py migrate
    
    log "✅ Migrations exécutées"
}

# Collecte des fichiers statiques
collect_static() {
    log "Collecte des fichiers statiques..."
    
    docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" exec backend python manage.py collectstatic --noinput
    
    log "✅ Fichiers statiques collectés"
}

# Création du superutilisateur
create_superuser() {
    log "Création du superutilisateur..."
    
    # Vérifier si le superutilisateur existe déjà
    if docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" exec backend python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); print(User.objects.filter(is_superuser=True).exists())" | grep -q "True"; then
        info "Superutilisateur déjà existant"
    else
        info "Création du superutilisateur interactif..."
        docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" exec backend python manage.py createsuperuser
    fi
}

# Vérification de la santé des services
health_check() {
    log "Vérification de la santé des services..."
    
    # Attendre que les services soient prêts
    sleep 30
    
    # Vérifier le backend
    if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
        log "✅ Backend opérationnel"
    else
        error "❌ Backend non accessible"
    fi
    
    # Vérifier le frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log "✅ Frontend opérationnel"
    else
        error "❌ Frontend non accessible"
    fi
    
    # Vérifier la base de données
    if docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" exec postgres pg_isready -U erp_user_prod > /dev/null 2>&1; then
        log "✅ Base de données opérationnelle"
    else
        error "❌ Base de données non accessible"
    fi
}

# Nettoyage des anciennes images
cleanup() {
    log "Nettoyage des anciennes images..."
    
    # Supprimer les images non utilisées
    docker image prune -f
    
    # Supprimer les volumes non utilisés
    docker volume prune -f
    
    log "✅ Nettoyage terminé"
}

# Affichage des logs
show_logs() {
    log "Affichage des logs des services..."
    
    docker-compose -f docker-compose.prod.yml --env-file ".env.${ENVIRONMENT}" logs --tail=50
}

# Fonction principale
main() {
    log "🚀 Début du déploiement ERP HUB en ${ENVIRONMENT}"
    
    # Créer le répertoire de logs
    mkdir -p logs
    
    # Étapes du déploiement
    check_prerequisites
    backup_database
    stop_services
    build_images
    start_services
    run_migrations
    collect_static
    health_check
    cleanup
    
    log "🎉 Déploiement terminé avec succès!"
    log "📊 Services disponibles:"
    log "   - Frontend: http://localhost:3000"
    log "   - Backend API: http://localhost:8000/api"
    log "   - Admin Django: http://localhost:8000/admin"
    log "   - Documentation API: http://localhost:8000/docs"
    log "   - Monitoring Grafana: http://localhost:3001"
    log "   - Prometheus: http://localhost:9090"
    
    # Proposer la création du superutilisateur
    read -p "Voulez-vous créer un superutilisateur? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_superuser
    fi
    
    # Proposer l'affichage des logs
    read -p "Voulez-vous voir les logs? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_logs
    fi
}

# Gestion des signaux
trap 'error "Déploiement interrompu"' INT TERM

# Exécution
main "$@"
