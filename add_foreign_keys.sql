-- 🔗 AJOUT DES CLÉS ÉTRANGÈRES POUR ANALYSES CROISÉES
-- Script pour créer les relations entre tables

-- D'abord, créons les tables de référence manquantes

-- Table des clients/contacts (pour centraliser les informations)
CREATE TABLE IF NOT EXISTS clients (
    id VARCHAR(255) PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    client_type VARCHAR(20) DEFAULT 'client' CHECK (client_type IN ('client', 'prospect', 'fournisseur')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des fournisseurs (si pas déjà dans clients)
CREATE TABLE IF NOT EXISTS suppliers (
    id VARCHAR(255) PRIMARY KEY,
    company_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    contact_name VA<PERSON>HAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    supplier_type VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des employés (pour les responsables budgets)
CREATE TABLE IF NOT EXISTS employees (
    id VARCHAR(255) PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(50),
    position VARCHAR(100),
    department VARCHAR(100),
    salary DECIMAL(10,2),
    hire_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated')),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des centres de coûts
CREATE TABLE IF NOT EXISTS cost_centers (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id VARCHAR(255),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des codes analytiques
CREATE TABLE IF NOT EXISTS analytic_codes (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insérer des données de référence
INSERT INTO employees (id, first_name, last_name, email, position, department, salary) VALUES
('emp_001', 'Jean', 'Dupont', '<EMAIL>', 'Directeur Marketing', 'Marketing', 65000),
('emp_002', 'Marie', 'Martin', '<EMAIL>', 'Développeuse', 'IT', 55000),
('emp_003', 'Pierre', 'Durand', '<EMAIL>', 'Commercial', 'Ventes', 45000),
('emp_004', 'Sophie', 'Leroy', '<EMAIL>', 'Responsable IT', 'Informatique', 60000),
('emp_005', 'Luc', 'Bernard', '<EMAIL>', 'Contrôleur de Gestion', 'Finance', 58000)
ON CONFLICT (id) DO NOTHING;

INSERT INTO cost_centers (id, name, description, manager_id, department) VALUES
('CC001', 'Marketing', 'Centre de coût Marketing', 'emp_001', 'Marketing'),
('CC002', 'Commercial', 'Centre de coût Commercial', 'emp_003', 'Commercial'),
('CC003', 'RH', 'Centre de coût Ressources Humaines', 'emp_005', 'Ressources Humaines'),
('CC004', 'IT', 'Centre de coût Informatique', 'emp_004', 'Informatique'),
('CC005', 'Finance', 'Centre de coût Finance', 'emp_005', 'Finance')
ON CONFLICT (id) DO NOTHING;

INSERT INTO analytic_codes (id, name, description, category) VALUES
('AC001', 'Publicité', 'Dépenses publicitaires', 'Marketing'),
('AC002', 'Ventes', 'Revenus des ventes', 'Commercial'),
('AC003', 'Formation', 'Formation du personnel', 'RH'),
('AC004', 'Matériel', 'Investissements matériels', 'IT'),
('AC005', 'Intérêts', 'Charges financières', 'Finance')
ON CONFLICT (id) DO NOTHING;

INSERT INTO clients (id, company_name, contact_name, email, phone, client_type) VALUES
('client_001', 'TechCorp SA', 'Sophie Leblanc', '<EMAIL>', '+33123456789', 'client'),
('client_002', 'InnoSoft SARL', 'Marc Rousseau', '<EMAIL>', '+33987654321', 'prospect'),
('client_003', 'DataSys Ltd', 'Anna Schmidt', '<EMAIL>', '+33456789123', 'client'),
('client_004', 'StartupTech SARL', 'Thomas Petit', '<EMAIL>', '+33789123456', 'prospect'),
('client_005', 'RetailCorp SA', 'Julie Moreau', '<EMAIL>', '+33321654987', 'client'),
('client_006', 'FinanceGroup Ltd', 'David Wilson', '<EMAIL>', '+33654987321', 'prospect')
ON CONFLICT (id) DO NOTHING;

INSERT INTO suppliers (id, company_name, contact_name, email, phone, supplier_type) VALUES
('supplier_001', 'Microsoft France', 'Jean-Paul Martin', '<EMAIL>', '+33140000000', 'Software'),
('supplier_002', 'Amazon Web Services', 'Sarah Johnson', '<EMAIL>', '+33140000001', 'Cloud'),
('supplier_003', 'Dell Technologies', 'Marco Rossi', '<EMAIL>', '+33140000002', 'Hardware'),
('supplier_004', 'HP Enterprise', 'Lisa Chen', '<EMAIL>', '+33140000003', 'Hardware'),
('supplier_005', 'Oracle Corporation', 'Robert Smith', '<EMAIL>', '+33140000004', 'Database'),
('supplier_006', 'Cisco Systems', 'Maria Garcia', '<EMAIL>', '+***********', 'Network')
ON CONFLICT (id) DO NOTHING;

-- Maintenant, ajoutons les clés étrangères

-- Relations pour la table budgets
ALTER TABLE budgets 
ADD CONSTRAINT fk_budgets_cost_center 
FOREIGN KEY (cost_center) REFERENCES cost_centers(id);

ALTER TABLE budgets 
ADD CONSTRAINT fk_budgets_analytic_code 
FOREIGN KEY (analytic_code) REFERENCES analytic_codes(id);

-- Relations pour la table movements
ALTER TABLE movements 
ADD CONSTRAINT fk_movements_account 
FOREIGN KEY (account_id) REFERENCES accounts(id);

-- Relations pour les factures clients
ALTER TABLE invoices_clients 
ADD CONSTRAINT fk_invoices_clients_client 
FOREIGN KEY (client_id) REFERENCES clients(id);

-- Relations pour les factures fournisseurs
ALTER TABLE invoices_suppliers 
ADD CONSTRAINT fk_invoices_suppliers_supplier 
FOREIGN KEY (supplier_id) REFERENCES suppliers(id);

-- Relations pour les bons de commande
ALTER TABLE purchase_orders 
ADD CONSTRAINT fk_purchase_orders_supplier 
FOREIGN KEY (supplier_id) REFERENCES suppliers(id);

-- Relations pour les devis
ALTER TABLE quotes 
ADD CONSTRAINT fk_quotes_client 
FOREIGN KEY (client_id) REFERENCES clients(id);

-- Relations pour les bons de livraison
ALTER TABLE delivery_notes 
ADD CONSTRAINT fk_delivery_notes_client 
FOREIGN KEY (client_id) REFERENCES clients(id);

-- Relations pour les centres de coûts
ALTER TABLE cost_centers 
ADD CONSTRAINT fk_cost_centers_manager 
FOREIGN KEY (manager_id) REFERENCES employees(id);

-- Mettre à jour les données existantes avec les bonnes relations
UPDATE invoices_clients SET client_id = 'client_001' WHERE client_name = 'TechCorp SA';
UPDATE invoices_clients SET client_id = 'client_002' WHERE client_name = 'InnoSoft SARL';
UPDATE invoices_clients SET client_id = 'client_003' WHERE client_name = 'DataSys Ltd';

UPDATE quotes SET client_id = 'client_004' WHERE client_name = 'StartupTech SARL';
UPDATE quotes SET client_id = 'client_005' WHERE client_name = 'RetailCorp SA';
UPDATE quotes SET client_id = 'client_006' WHERE client_name = 'FinanceGroup Ltd';

UPDATE invoices_suppliers SET supplier_id = 'supplier_001' WHERE supplier_name = 'Microsoft France';
UPDATE invoices_suppliers SET supplier_id = 'supplier_002' WHERE supplier_name = 'Amazon Web Services';
UPDATE invoices_suppliers SET supplier_id = 'supplier_003' WHERE supplier_name = 'Dell Technologies';

UPDATE purchase_orders SET supplier_id = 'supplier_004' WHERE supplier_name = 'HP Enterprise';
UPDATE purchase_orders SET supplier_id = 'supplier_005' WHERE supplier_name = 'Oracle Corporation';
UPDATE purchase_orders SET supplier_id = 'supplier_006' WHERE supplier_name = 'Cisco Systems';

-- Index pour optimiser les jointures
CREATE INDEX IF NOT EXISTS idx_budgets_cost_center_fk ON budgets(cost_center);
CREATE INDEX IF NOT EXISTS idx_budgets_analytic_code_fk ON budgets(analytic_code);
CREATE INDEX IF NOT EXISTS idx_movements_account_fk ON movements(account_id);
CREATE INDEX IF NOT EXISTS idx_invoices_clients_client_fk ON invoices_clients(client_id);
CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_supplier_fk ON invoices_suppliers(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier_fk ON purchase_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_quotes_client_fk ON quotes(client_id);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_client_fk ON delivery_notes(client_id);

SELECT 'Relations et clés étrangères créées avec succès pour analyses croisées !' as message;
