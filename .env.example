# Configuration de base
ENVIRONMENT=development
DEBUG=True

# Base de données PostgreSQL
DATABASE_URL=postgresql://erp_user:erp_password@localhost:5432/erp_hub_dev
POSTGRES_DB=erp_hub_dev
POSTGRES_USER=erp_user
POSTGRES_PASSWORD=erp_password

# Django Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=1440  # minutes (24 hours)

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration (pour les notifications)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_APP_NAME=ERP HUB
VITE_APP_VERSION=1.0.0

# Agent Configuration
OPENAI_API_KEY=your-openai-api-key-for-ai-agents
AGENT_MANAGER_PORT=8001
AGENT_COMMUNICATION_PROTOCOL=http

# Logging
LOG_LEVEL=DEBUG
LOG_FILE=logs/erp_hub.log

# Security
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# File Upload
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
