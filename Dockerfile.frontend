# 🌐 Dockerfile pour Frontend ERP HUB
# Configuration optimisée pour servir les fichiers HTML/CSS/JS

# Étape 1: Serveur de développement Node.js
FROM node:20-alpine as development

WORKDIR /app

# Installer les outils de serveur
RUN npm install -g http-server live-server serve

# Copier les fichiers frontend
COPY frontend/ ./

# Exposer le port
EXPOSE 8080

# Commande par défaut pour développement
CMD ["http-server", ".", "-p", "8080", "--cors", "-o"]

# Étape 2: Production avec Nginx
FROM nginx:alpine as production

# Copier les fichiers statiques
COPY frontend/ /usr/share/nginx/html/

# Configuration Nginx personnalisée
COPY nginx.conf /etc/nginx/nginx.conf

# Exposer le port
EXPOSE 80

# Commande par défaut
CMD ["nginx", "-g", "daemon off;"]
