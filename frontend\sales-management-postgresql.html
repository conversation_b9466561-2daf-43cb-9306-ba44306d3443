<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales - Gestion Commerciale | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #3b82f6 30%, #2563eb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        

        
        .dashboard-grid {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .stat-card.primary { border-left: 4px solid #3b82f6; }
        .stat-card.success { border-left: 4px solid #10b981; }
        .stat-card.warning { border-left: 4px solid #f59e0b; }
        .stat-card.info { border-left: 4px solid #06b6d4; }
        .stat-card.secondary { border-left: 4px solid #8b5cf6; }
        .stat-card.danger { border-left: 4px solid #ef4444; }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .stat-trend {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.125rem 0.5rem;
            border-radius: 0.375rem;
            background: #f3f4f6;
            color: #374151;
        }

        .stat-trend.positive {
            background: #d1fae5;
            color: #065f46;
        }

        .stat-trend.negative {
            background: #fee2e2;
            color: #991b1b;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .chart-card {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .chart-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-header h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: 0.5rem;
        }

        .form-select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background: white;
        }

        .chart-content {
            padding: 1.5rem;
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .goals-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .goal-card, .forecast-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
        }

        .goal-card h3, .forecast-card h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .goal-progress {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .goal-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .goal-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .goal-value {
            font-size: 0.75rem;
            color: #6b7280;
            text-align: right;
        }

        .forecast-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .forecast-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .forecast-icon {
            font-size: 1.25rem;
            margin-top: 0.125rem;
        }

        .forecast-text {
            flex: 1;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .forecast-text strong {
            color: #374151;
        }

        .forecast-text span {
            color: #6b7280;
        }

        /* Styles pour le dashboard summary */
        .dashboard-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
        }

        .summary-card h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .action-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .action-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: #f9fafb;
            border-radius: 0.5rem;
            border-left: 3px solid #3b82f6;
        }

        .action-icon {
            font-size: 1.25rem;
        }

        .action-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .action-content strong {
            font-size: 0.875rem;
            color: #1f2937;
        }

        .action-meta {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .team-performance {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .performance-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.5rem;
        }

        .performance-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .performance-content {
            flex: 1;
        }

        .performance-content strong {
            font-size: 0.875rem;
            color: #1f2937;
            display: block;
            margin-bottom: 0.25rem;
        }

        .performance-stats {
            display: flex;
            gap: 1rem;
        }

        .performance-stats .stat {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .performance-score {
            font-size: 1rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
        }

        .performance-score.success {
            background: #d1fae5;
            color: #065f46;
        }

        .performance-score.warning {
            background: #fef3c7;
            color: #92400e;
        }

        /* Styles pour le pipeline */
        .pipeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .pipeline-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .pipeline-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .pipeline-kanban {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
            min-height: 600px;
        }

        .kanban-column {
            background: #f9fafb;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .kanban-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .kanban-header h3 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .kanban-count {
            background: #3b82f6;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.125rem 0.5rem;
            border-radius: 0.75rem;
            min-width: 1.5rem;
            text-align: center;
        }

        .kanban-content {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            min-height: 500px;
        }

        .opportunity-card {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s;
        }

        .opportunity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .opportunity-card h4 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .opportunity-card .customer {
            font-size: 0.75rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .opportunity-card .value {
            font-size: 1rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .opportunity-card .probability {
            font-size: 0.75rem;
            color: #374151;
        }

        /* Styles pour les sections améliorées */
        .section-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .filters-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .action-buttons {
            display: flex;
            gap: 0.25rem;
        }

        .btn-icon {
            padding: 0.375rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .btn-icon:hover {
            transform: scale(1.05);
        }

        .btn-icon.edit {
            background: #3b82f6;
            color: white;
        }

        .btn-icon.delete {
            background: #ef4444;
            color: white;
        }

        .btn-icon.view {
            background: #10b981;
            color: white;
        }

        .btn-icon .material-icons {
            font-size: 1rem;
        }

        /* Styles pour les modales */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
            padding: 0.25rem;
        }

        .modal-close:hover {
            color: #374151;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Responsive design amélioré */
        @media (max-width: 1024px) {
            .pipeline-kanban {
                grid-template-columns: repeat(3, 1fr);
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .pipeline-kanban {
                grid-template-columns: 1fr;
            }

            .filters-bar {
                flex-direction: column;
            }

            .search-input {
                min-width: auto;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .tab-text {
                display: none;
            }

            .dashboard-summary {
                grid-template-columns: 1fr;
            }
        }

        /* Styles pour la barre de probabilité */
        .probability-bar {
            position: relative;
            width: 100%;
            height: 20px;
            background: #f3f4f6;
            border-radius: 10px;
            overflow: hidden;
        }

        .probability-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .probability-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.75rem;
            font-weight: 600;
            color: #1f2937;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        /* Styles pour les cartes d'opportunité urgentes */
        .opportunity-card.urgent {
            border-left: 4px solid #ef4444;
            background: #fef2f2;
        }

        .opportunity-card .close-date {
            font-size: 0.6875rem;
            color: #ef4444;
            font-weight: 600;
            margin-top: 0.25rem;
        }

        /* Animation de chargement améliorée */
        .loading {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            border: 3px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Styles pour les notifications toast */
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1100;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .toast {
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
            min-width: 300px;
            animation: slideIn 0.3s ease;
        }

        .toast.success { border-left-color: #10b981; }
        .toast.warning { border-left-color: #f59e0b; }
        .toast.error { border-left-color: #ef4444; }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Amélioration des tableaux */
        .data-table tbody tr:hover {
            background: #f8fafc;
            transform: scale(1.001);
            transition: all 0.2s ease;
        }

        .data-table th {
            position: sticky;
            top: 0;
            background: #f9fafb;
            z-index: 10;
        }

        /* Styles pour les badges de statut améliorés */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            line-height: 1;
        }

        .badge::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }

        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.25rem;
            overflow-x: auto;
            padding-bottom: 0;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.25rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
            position: relative;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .nav-tab:hover {
            color: #3b82f6;
            background: #f8fafc;
        }

        .nav-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #eff6ff;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-text {
            font-size: 0.875rem;
        }

        .tab-badge {
            background: #ef4444;
            color: white;
            font-size: 0.625rem;
            font-weight: 600;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            min-width: 1rem;
            text-align: center;
            line-height: 1;
        }

        .tab-badge:empty {
            display: none;
        }

        .tab-badge.success {
            background: #10b981;
        }

        .tab-badge.warning {
            background: #f59e0b;
        }

        .tab-badge.info {
            background: #06b6d4;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>
    <header class="header">
        <div class="logo">💼 Sales</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">


        <!-- Dashboard Avancé -->
        <div class="dashboard-grid">
            <!-- KPIs Principaux -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalCustomers">0</div>
                        <div class="stat-label">Total Clients</div>
                        <div class="stat-trend" id="customersTrend">+0%</div>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📈</div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalOpportunities">0</div>
                        <div class="stat-label">Opportunités</div>
                        <div class="stat-trend" id="opportunitiesTrend">+0%</div>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">🛒</div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalOrders">0</div>
                        <div class="stat-label">Commandes</div>
                        <div class="stat-trend" id="ordersTrend">+0%</div>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalRevenue">0€</div>
                        <div class="stat-label">Chiffre d'Affaires</div>
                        <div class="stat-trend" id="revenueTrend">+0%</div>
                    </div>
                </div>
                <div class="stat-card secondary">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-value" id="conversionRate">0%</div>
                        <div class="stat-label">Taux de Conversion</div>
                        <div class="stat-trend" id="conversionTrend">+0%</div>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-content">
                        <div class="stat-value" id="avgSaleCycle">0j</div>
                        <div class="stat-label">Cycle de Vente Moyen</div>
                        <div class="stat-trend" id="cycleTrend">+0%</div>
                    </div>
                </div>
            </div>

            <!-- Graphiques et Analyses -->
            <div class="charts-grid">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Pipeline des Ventes</h3>
                        <div class="chart-controls">
                            <select id="pipelineFilter" class="form-select">
                                <option value="all">Toutes les étapes</option>
                                <option value="prospecting">Prospection</option>
                                <option value="qualification">Qualification</option>
                                <option value="proposal">Proposition</option>
                                <option value="negotiation">Négociation</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="pipelineChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Évolution des Ventes</h3>
                        <div class="chart-controls">
                            <select id="salesPeriod" class="form-select">
                                <option value="7d">7 derniers jours</option>
                                <option value="30d">30 derniers jours</option>
                                <option value="90d">90 derniers jours</option>
                                <option value="1y">1 an</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Objectifs et Prévisions -->
            <div class="goals-section">
                <div class="goal-card">
                    <h3>Objectifs du Mois</h3>
                    <div class="goal-progress">
                        <div class="goal-item">
                            <span class="goal-label">Chiffre d'Affaires</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="revenueProgress" style="width: 0%"></div>
                            </div>
                            <span class="goal-value" id="revenueGoal">0€ / 100,000€</span>
                        </div>
                        <div class="goal-item">
                            <span class="goal-label">Nouvelles Opportunités</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="opportunitiesProgress" style="width: 0%"></div>
                            </div>
                            <span class="goal-value" id="opportunitiesGoal">0 / 50</span>
                        </div>
                        <div class="goal-item">
                            <span class="goal-label">Commandes Fermées</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="ordersProgress" style="width: 0%"></div>
                            </div>
                            <span class="goal-value" id="ordersGoal">0 / 25</span>
                        </div>
                    </div>
                </div>

                <div class="forecast-card">
                    <h3>Prévisions IA</h3>
                    <div class="forecast-content">
                        <div class="forecast-item">
                            <span class="forecast-icon">🎯</span>
                            <div class="forecast-text">
                                <strong>Prévision fin de mois :</strong>
                                <span id="monthForecast">Calcul en cours...</span>
                            </div>
                        </div>
                        <div class="forecast-item">
                            <span class="forecast-icon">📊</span>
                            <div class="forecast-text">
                                <strong>Probabilité d'atteinte :</strong>
                                <span id="achievementProbability">Calcul en cours...</span>
                            </div>
                        </div>
                        <div class="forecast-item">
                            <span class="forecast-icon">💡</span>
                            <div class="forecast-text">
                                <strong>Recommandation :</strong>
                                <span id="aiRecommendation">Analyse en cours...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets avancée -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')" data-tab="dashboard">
                <span class="material-icons">dashboard</span>
                <span class="tab-text">Dashboard</span>
                <span class="tab-badge" id="dashboardBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('pipeline')" data-tab="pipeline">
                <span class="material-icons">timeline</span>
                <span class="tab-text">Pipeline</span>
                <span class="tab-badge" id="pipelineBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('customers')" data-tab="customers">
                <span class="material-icons">people</span>
                <span class="tab-text">Clients</span>
                <span class="tab-badge" id="customersBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('opportunities')" data-tab="opportunities">
                <span class="material-icons">trending_up</span>
                <span class="tab-text">Opportunités</span>
                <span class="tab-badge" id="opportunitiesBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('quotes')" data-tab="quotes">
                <span class="material-icons">description</span>
                <span class="tab-text">Devis</span>
                <span class="tab-badge" id="quotesBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('orders')" data-tab="orders">
                <span class="material-icons">shopping_cart</span>
                <span class="tab-text">Commandes</span>
                <span class="tab-badge" id="ordersBadge"></span>
            </button>
            <button class="nav-tab" onclick="showTab('analytics')" data-tab="analytics">
                <span class="material-icons">analytics</span>
                <span class="tab-text">Analyses</span>
                <span class="tab-badge" id="analyticsBadge"></span>
            </button>
        </nav>

        <!-- Onglet Dashboard -->
        <div id="dashboard" class="tab-content active">
            <!-- Le dashboard est déjà affiché au-dessus -->
            <div class="dashboard-summary">
                <div class="summary-card">
                    <h3>🎯 Actions Prioritaires</h3>
                    <div class="action-list" id="priorityActions">
                        <div class="action-item">
                            <span class="action-icon">📞</span>
                            <div class="action-content">
                                <strong>Relancer 3 prospects chauds</strong>
                                <span class="action-meta">Échéance aujourd'hui</span>
                            </div>
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📝</span>
                            <div class="action-content">
                                <strong>Finaliser 2 devis en attente</strong>
                                <span class="action-meta">Valeur: 45,000€</span>
                            </div>
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🤝</span>
                            <div class="action-content">
                                <strong>Négociation avec TechCorp</strong>
                                <span class="action-meta">Opportunité: 120,000€</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="summary-card">
                    <h3>📊 Performance de l'Équipe</h3>
                    <div class="team-performance" id="teamPerformance">
                        <div class="performance-item">
                            <div class="performance-avatar">👨‍💼</div>
                            <div class="performance-content">
                                <strong>Marie Dubois</strong>
                                <div class="performance-stats">
                                    <span class="stat">15 opportunités</span>
                                    <span class="stat">85,000€ pipeline</span>
                                </div>
                            </div>
                            <div class="performance-score success">92%</div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-avatar">👨‍💼</div>
                            <div class="performance-content">
                                <strong>Jean Martin</strong>
                                <div class="performance-stats">
                                    <span class="stat">12 opportunités</span>
                                    <span class="stat">67,000€ pipeline</span>
                                </div>
                            </div>
                            <div class="performance-score warning">78%</div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-avatar">👩‍💼</div>
                            <div class="performance-content">
                                <strong>Sophie Laurent</strong>
                                <div class="performance-stats">
                                    <span class="stat">18 opportunités</span>
                                    <span class="stat">95,000€ pipeline</span>
                                </div>
                            </div>
                            <div class="performance-score success">96%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Pipeline -->
        <div id="pipeline" class="tab-content">
            <div class="pipeline-header">
                <h2>Pipeline des Ventes</h2>
                <div class="pipeline-controls">
                    <button class="btn btn-primary" onclick="createOpportunity()">
                        <span class="material-icons" style="font-size: 1rem;">add</span>
                        Nouvelle Opportunité
                    </button>
                    <select id="pipelineView" class="form-select">
                        <option value="kanban">Vue Kanban</option>
                        <option value="list">Vue Liste</option>
                        <option value="forecast">Prévisions</option>
                    </select>
                </div>
            </div>

            <div class="pipeline-kanban" id="pipelineKanban">
                <div class="kanban-column" data-stage="prospecting">
                    <div class="kanban-header">
                        <h3>Prospection</h3>
                        <span class="kanban-count" id="prospectingCount">0</span>
                    </div>
                    <div class="kanban-content" id="prospectingCards">
                        <!-- Les cartes seront ajoutées dynamiquement -->
                    </div>
                </div>

                <div class="kanban-column" data-stage="qualification">
                    <div class="kanban-header">
                        <h3>Qualification</h3>
                        <span class="kanban-count" id="qualificationCount">0</span>
                    </div>
                    <div class="kanban-content" id="qualificationCards">
                        <!-- Les cartes seront ajoutées dynamiquement -->
                    </div>
                </div>

                <div class="kanban-column" data-stage="proposal">
                    <div class="kanban-header">
                        <h3>Proposition</h3>
                        <span class="kanban-count" id="proposalCount">0</span>
                    </div>
                    <div class="kanban-content" id="proposalCards">
                        <!-- Les cartes seront ajoutées dynamiquement -->
                    </div>
                </div>

                <div class="kanban-column" data-stage="negotiation">
                    <div class="kanban-header">
                        <h3>Négociation</h3>
                        <span class="kanban-count" id="negotiationCount">0</span>
                    </div>
                    <div class="kanban-content" id="negotiationCards">
                        <!-- Les cartes seront ajoutées dynamiquement -->
                    </div>
                </div>

                <div class="kanban-column" data-stage="closed_won">
                    <div class="kanban-header">
                        <h3>Gagné</h3>
                        <span class="kanban-count" id="closedWonCount">0</span>
                    </div>
                    <div class="kanban-content" id="closedWonCards">
                        <!-- Les cartes seront ajoutées dynamiquement -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Clients -->
        <div id="customers" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">👥 Clients</h2>
                    <button class="btn btn-primary" onclick="loadCustomers()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Client</th>
                                    <th>Entreprise</th>
                                    <th>Contact</th>
                                    <th>Email</th>
                                    <th>Ville</th>
                                    <th>Type</th>
                                    <th>Limite Crédit</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des clients...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Opportunités -->
        <div id="opportunities" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📈 Opportunités</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportOpportunities()">
                            <span class="material-icons" style="font-size: 1rem;">download</span>
                            Exporter
                        </button>
                        <button class="btn btn-primary" onclick="createOpportunity()">
                            <span class="material-icons" style="font-size: 1rem;">add</span>
                            Nouvelle Opportunité
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <div class="filters-bar">
                        <input type="text" id="opportunitySearch" placeholder="Rechercher une opportunité..." class="search-input">
                        <select id="stageFilter" class="form-select">
                            <option value="">Toutes les étapes</option>
                            <option value="prospecting">Prospection</option>
                            <option value="qualification">Qualification</option>
                            <option value="proposal">Proposition</option>
                            <option value="negotiation">Négociation</option>
                            <option value="closed_won">Gagné</option>
                            <option value="closed_lost">Perdu</option>
                        </select>
                        <select id="probabilityFilter" class="form-select">
                            <option value="">Toutes probabilités</option>
                            <option value="high">Élevée (>70%)</option>
                            <option value="medium">Moyenne (30-70%)</option>
                            <option value="low">Faible (&lt;30%)</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Titre</th>
                                    <th>Valeur</th>
                                    <th>Probabilité</th>
                                    <th>Étape</th>
                                    <th>Date Prévue</th>
                                    <th>Commercial</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="opportunitiesTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des opportunités...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Devis -->
        <div id="quotes" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📝 Devis</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportQuotes()">
                            <span class="material-icons" style="font-size: 1rem;">download</span>
                            Exporter
                        </button>
                        <button class="btn btn-primary" onclick="createQuote()">
                            <span class="material-icons" style="font-size: 1rem;">add</span>
                            Nouveau Devis
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <div class="filters-bar">
                        <input type="text" id="quoteSearch" placeholder="Rechercher un devis..." class="search-input">
                        <select id="quoteStatusFilter" class="form-select">
                            <option value="">Tous les statuts</option>
                            <option value="draft">Brouillon</option>
                            <option value="sent">Envoyé</option>
                            <option value="accepted">Accepté</option>
                            <option value="rejected">Refusé</option>
                            <option value="expired">Expiré</option>
                        </select>
                        <input type="date" id="quoteDateFilter" class="form-select">
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>N° Devis</th>
                                    <th>Client</th>
                                    <th>Date Création</th>
                                    <th>Date Expiration</th>
                                    <th>Montant HT</th>
                                    <th>Montant TTC</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="quotesTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des devis...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📈 Opportunités</h2>
                    <button class="btn btn-primary" onclick="loadOpportunities()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Titre</th>
                                    <th>Valeur</th>
                                    <th>Probabilité</th>
                                    <th>Étape</th>
                                    <th>Date Prévue</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody id="opportunitiesTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des opportunités...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Commandes -->
        <div id="orders" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🛒 Commandes</h2>
                    <button class="btn btn-primary" onclick="loadOrders()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>N° Commande</th>
                                    <th>Client</th>
                                    <th>Date Commande</th>
                                    <th>Date Livraison</th>
                                    <th>Montant Total</th>
                                    <th>Statut</th>
                                    <th>Paiement</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des commandes...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Variables globales
        let customers = [];
        let opportunities = [];
        let orders = [];
        let quotes = [];

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }
        let currentTab = 'dashboard';
        let charts = {};

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Objectifs mensuels (à configurer)
        const MONTHLY_GOALS = {
            revenue: 100000,
            opportunities: 50,
            orders: 25
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            await loadAllData();
            initializeCharts();
            setupEventListeners();
            updateDashboard();
        }

        function setupEventListeners() {
            // Recherche en temps réel
            document.getElementById('opportunitySearch')?.addEventListener('input', filterOpportunities);
            document.getElementById('quoteSearch')?.addEventListener('input', filterQuotes);

            // Filtres
            document.getElementById('stageFilter')?.addEventListener('change', filterOpportunities);
            document.getElementById('probabilityFilter')?.addEventListener('change', filterOpportunities);
            document.getElementById('quoteStatusFilter')?.addEventListener('change', filterQuotes);
            document.getElementById('quoteDateFilter')?.addEventListener('change', filterQuotes);

            // Graphiques
            document.getElementById('pipelineFilter')?.addEventListener('change', updatePipelineChart);
            document.getElementById('salesPeriod')?.addEventListener('change', updateSalesChart);
        }

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await Promise.all([
                loadCustomers(),
                loadOpportunities(),
                loadOrders(),
                loadQuotes()
            ]);
            updateStats();
            updateTabBadges();
        }

        // Mettre à jour le dashboard
        function updateDashboard() {
            updateStats();
            updateGoalsProgress();
            updateForecasts();
            updatePipelineKanban();
            updateTabBadges();
        }

        // Mettre à jour les statistiques avancées
        function updateStats() {
            const totalCustomers = customers.length;
            const totalOpportunities = opportunities.length;
            const totalOrders = orders.length;
            const totalRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

            // Calculs avancés
            const wonOpportunities = opportunities.filter(opp => opp.stage === 'closed_won').length;
            const conversionRate = totalOpportunities > 0 ? (wonOpportunities / totalOpportunities * 100) : 0;

            // Cycle de vente moyen (simulation)
            const avgSaleCycle = 45; // jours

            // Mise à jour des valeurs
            document.getElementById('totalCustomers').textContent = totalCustomers;
            document.getElementById('totalOpportunities').textContent = totalOpportunities;
            document.getElementById('totalOrders').textContent = totalOrders;
            document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);
            document.getElementById('conversionRate').textContent = conversionRate.toFixed(1) + '%';
            document.getElementById('avgSaleCycle').textContent = avgSaleCycle + 'j';

            // Mise à jour des tendances (simulation)
            updateTrend('customersTrend', 12);
            updateTrend('opportunitiesTrend', 8);
            updateTrend('ordersTrend', -3);
            updateTrend('revenueTrend', 15);
            updateTrend('conversionTrend', 5);
            updateTrend('cycleTrend', -8);
        }

        function updateTrend(elementId, percentage) {
            const element = document.getElementById(elementId);
            if (element) {
                const isPositive = percentage >= 0;
                element.textContent = (isPositive ? '+' : '') + percentage + '%';
                element.className = 'stat-trend ' + (isPositive ? 'positive' : 'negative');
            }
        }

        // Mettre à jour les objectifs
        function updateGoalsProgress() {
            const currentRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
            const currentOpportunities = opportunities.length;
            const currentOrders = orders.length;

            updateProgressBar('revenueProgress', 'revenueGoal', currentRevenue, MONTHLY_GOALS.revenue, formatCurrency);
            updateProgressBar('opportunitiesProgress', 'opportunitiesGoal', currentOpportunities, MONTHLY_GOALS.opportunities);
            updateProgressBar('ordersProgress', 'ordersGoal', currentOrders, MONTHLY_GOALS.orders);
        }

        function updateProgressBar(progressId, goalId, current, target, formatter = null) {
            const progressElement = document.getElementById(progressId);
            const goalElement = document.getElementById(goalId);

            if (progressElement && goalElement) {
                const percentage = Math.min((current / target) * 100, 100);
                progressElement.style.width = percentage + '%';

                const currentText = formatter ? formatter(current) : current;
                const targetText = formatter ? formatter(target) : target;
                goalElement.textContent = `${currentText} / ${targetText}`;
            }
        }

        // Prévisions IA (simulation)
        function updateForecasts() {
            const pipelineValue = opportunities
                .filter(opp => !['closed_won', 'closed_lost'].includes(opp.stage))
                .reduce((sum, opp) => sum + (opp.value * (opp.probability / 100)), 0);

            const monthForecast = pipelineValue * 0.7; // Simulation
            const achievementProbability = Math.min(95, Math.max(20, (monthForecast / MONTHLY_GOALS.revenue) * 100));

            document.getElementById('monthForecast').textContent = formatCurrency(monthForecast);
            document.getElementById('achievementProbability').textContent = achievementProbability.toFixed(0) + '%';

            // Recommandation IA
            let recommendation = '';
            if (achievementProbability > 80) {
                recommendation = 'Excellent ! Vous êtes en bonne voie pour dépasser vos objectifs.';
            } else if (achievementProbability > 60) {
                recommendation = 'Bon rythme. Concentrez-vous sur les opportunités à forte probabilité.';
            } else {
                recommendation = 'Attention ! Intensifiez la prospection et accélérez les négociations.';
            }

            document.getElementById('aiRecommendation').textContent = recommendation;
        }

        // Mettre à jour les badges des onglets
        function updateTabBadges() {
            const urgentOpportunities = opportunities.filter(opp => {
                const closeDate = new Date(opp.expectedCloseDate);
                const today = new Date();
                const daysUntilClose = (closeDate - today) / (1000 * 60 * 60 * 24);
                return daysUntilClose <= 7 && !['closed_won', 'closed_lost'].includes(opp.stage);
            }).length;

            const pendingQuotes = quotes.filter(quote => quote.status === 'sent').length;
            const overdueOrders = orders.filter(order => {
                const deliveryDate = new Date(order.deliveryDate);
                const today = new Date();
                return deliveryDate < today && order.status !== 'delivered';
            }).length;

            setBadge('pipelineBadge', urgentOpportunities, 'warning');
            setBadge('quotesBadge', pendingQuotes, 'info');
            setBadge('ordersBadge', overdueOrders, 'danger');
        }

        function setBadge(elementId, count, type = '') {
            const element = document.getElementById(elementId);
            if (element) {
                if (count > 0) {
                    element.textContent = count;
                    element.className = `tab-badge ${type}`;
                } else {
                    element.textContent = '';
                    element.className = 'tab-badge';
                }
            }
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les clients depuis PostgreSQL
        async function loadCustomers() {
            try {
                showAlert('Chargement des clients depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/customers`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        customers = data.data || [];
                        renderCustomersTable();
                        showAlert(`${customers.length} clients chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement clients:', error);
                showAlert('Erreur lors du chargement des clients: ' + error.message, 'error');
                customers = [];
                renderCustomersTable();
            }
        }

        // Charger les opportunités depuis PostgreSQL
        async function loadOpportunities() {
            try {
                const response = await fetch(`${API_BASE_URL}/opportunities`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        opportunities = data.data || [];
                        renderOpportunitiesTable();
                        console.log(`${opportunities.length} opportunités chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement opportunités:', error);
                showAlert('Erreur lors du chargement des opportunités: ' + error.message, 'error');
                opportunities = [];
                renderOpportunitiesTable();
            }
        }

        // Charger les devis depuis PostgreSQL
        async function loadQuotes() {
            try {
                const response = await fetch(`${API_BASE_URL}/quotes`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        quotes = data.data || [];
                        renderQuotesTable();
                        console.log(`${quotes.length} devis chargés depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement devis:', error);
                showAlert('Erreur lors du chargement des devis: ' + error.message, 'error');
                quotes = [];
                renderQuotesTable();
            }
        }

        // Charger les commandes depuis PostgreSQL
        async function loadOrders() {
            try {
                const response = await fetch(`${API_BASE_URL}/orders`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        orders = data.data || [];
                        renderOrdersTable();
                        console.log(`${orders.length} commandes chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement commandes:', error);
                showAlert('Erreur lors du chargement des commandes: ' + error.message, 'error');
                orders = [];
                renderOrdersTable();
            }
        }

        // Afficher le tableau des clients
        function renderCustomersTable() {
            const tbody = document.getElementById('customersTableBody');
            
            if (customers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun client trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = customers.map(customer => `
                <tr>
                    <td><strong>${customer.customerCode}</strong></td>
                    <td>${customer.companyName}</td>
                    <td>${customer.contactPerson || 'N/A'}</td>
                    <td>${customer.email || 'N/A'}</td>
                    <td>${customer.city || 'N/A'}</td>
                    <td>${getCustomerTypeBadge(customer.customerType)}</td>
                    <td>${customer.creditLimit ? customer.creditLimit.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getStatusBadge(customer.status)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour le pipeline Kanban
        function updatePipelineKanban() {
            const stages = ['prospecting', 'qualification', 'proposal', 'negotiation', 'closed_won'];

            stages.forEach(stage => {
                const stageOpportunities = opportunities.filter(opp => opp.stage === stage);
                const container = document.getElementById(stage + 'Cards');
                const countElement = document.getElementById(stage + 'Count');

                if (container && countElement) {
                    countElement.textContent = stageOpportunities.length;
                    container.innerHTML = stageOpportunities.map(opp => createOpportunityCard(opp)).join('');
                }
            });
        }

        function createOpportunityCard(opportunity) {
            const daysUntilClose = opportunity.expectedCloseDate ?
                Math.ceil((new Date(opportunity.expectedCloseDate) - new Date()) / (1000 * 60 * 60 * 24)) : null;

            const urgentClass = daysUntilClose !== null && daysUntilClose <= 7 ? 'urgent' : '';

            return `
                <div class="opportunity-card ${urgentClass}" onclick="editOpportunity('${opportunity.id}')">
                    <h4>${opportunity.title}</h4>
                    <div class="customer">${opportunity.customerName}</div>
                    <div class="value">${formatCurrency(opportunity.value)}</div>
                    <div class="probability">${opportunity.probability}% de probabilité</div>
                    ${daysUntilClose !== null ? `<div class="close-date">${daysUntilClose}j restants</div>` : ''}
                </div>
            `;
        }

        // Afficher le tableau des opportunités amélioré
        function renderOpportunitiesTable() {
            const tbody = document.getElementById('opportunitiesTableBody');
            
            if (opportunities.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune opportunité trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = opportunities.map(opp => `
                <tr>
                    <td><strong>${opp.customerName}</strong></td>
                    <td>${opp.title}</td>
                    <td>${formatCurrency(opp.value)}</td>
                    <td>
                        <div class="probability-bar">
                            <div class="probability-fill" style="width: ${opp.probability}%"></div>
                            <span class="probability-text">${opp.probability}%</span>
                        </div>
                    </td>
                    <td>${getStageBadge(opp.stage)}</td>
                    <td>${formatDate(opp.expectedCloseDate)}</td>
                    <td>${opp.salesRep || 'Non assigné'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon view" onclick="viewOpportunity('${opp.id}')" title="Voir">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="btn-icon edit" onclick="editOpportunity('${opp.id}')" title="Modifier">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="btn-icon delete" onclick="deleteOpportunity('${opp.id}')" title="Supprimer">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des devis
        function renderQuotesTable() {
            const tbody = document.getElementById('quotesTableBody');

            if (quotes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun devis trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = quotes.map(quote => `
                <tr>
                    <td><strong>${quote.quoteNumber}</strong></td>
                    <td>${quote.customerName}</td>
                    <td>${formatDate(quote.createdDate)}</td>
                    <td>${formatDate(quote.expirationDate)}</td>
                    <td>${formatCurrency(quote.amountHT)}</td>
                    <td>${formatCurrency(quote.amountTTC)}</td>
                    <td>${getQuoteStatusBadge(quote.status)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon view" onclick="viewQuote('${quote.id}')" title="Voir">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="btn-icon edit" onclick="editQuote('${quote.id}')" title="Modifier">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="btn-icon" onclick="downloadQuote('${quote.id}')" title="Télécharger PDF" style="background: #8b5cf6;">
                                <span class="material-icons">download</span>
                            </button>
                            <button class="btn-icon delete" onclick="deleteQuote('${quote.id}')" title="Supprimer">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des commandes amélioré
        function renderOrdersTable() {
            const tbody = document.getElementById('ordersTableBody');
            
            if (orders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune commande trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = orders.map(order => `
                <tr>
                    <td><strong>${order.orderNumber}</strong></td>
                    <td>${order.customerName}</td>
                    <td>${formatDate(order.orderDate)}</td>
                    <td>${formatDate(order.deliveryDate)}</td>
                    <td>${order.totalAmount ? order.totalAmount.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getOrderStatusBadge(order.status)}</td>
                    <td>${getPaymentStatusBadge(order.paymentStatus)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalCustomers = customers.length;
            const totalOpportunities = opportunities.length;
            const totalOrders = orders.length;
            const totalRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);

            document.getElementById('totalCustomers').textContent = totalCustomers;
            document.getElementById('totalOpportunities').textContent = totalOpportunities;
            document.getElementById('totalOrders').textContent = totalOrders;
            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString() + '€';
        }

        // Gestion des onglets améliorée
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Afficher le contenu de l'onglet sélectionné
            const tabContent = document.getElementById(tabName);
            if (tabContent) {
                tabContent.classList.add('active');
            }

            // Activer l'onglet sélectionné
            const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }

            currentTab = tabName;

            // Charger les données spécifiques à l'onglet si nécessaire
            if (tabName === 'pipeline') {
                updatePipelineKanban();
            }
        }

        // Fonctions de filtrage
        function filterOpportunities() {
            const searchTerm = document.getElementById('opportunitySearch')?.value.toLowerCase() || '';
            const stageFilter = document.getElementById('stageFilter')?.value || '';
            const probabilityFilter = document.getElementById('probabilityFilter')?.value || '';

            let filteredOpportunities = opportunities.filter(opp => {
                const matchesSearch = opp.title.toLowerCase().includes(searchTerm) ||
                                    opp.customerName.toLowerCase().includes(searchTerm);
                const matchesStage = !stageFilter || opp.stage === stageFilter;

                let matchesProbability = true;
                if (probabilityFilter === 'high') matchesProbability = opp.probability > 70;
                else if (probabilityFilter === 'medium') matchesProbability = opp.probability >= 30 && opp.probability <= 70;
                else if (probabilityFilter === 'low') matchesProbability = opp.probability < 30;

                return matchesSearch && matchesStage && matchesProbability;
            });

            renderFilteredOpportunities(filteredOpportunities);
        }

        function filterQuotes() {
            const searchTerm = document.getElementById('quoteSearch')?.value.toLowerCase() || '';
            const statusFilter = document.getElementById('quoteStatusFilter')?.value || '';
            const dateFilter = document.getElementById('quoteDateFilter')?.value || '';

            let filteredQuotes = quotes.filter(quote => {
                const matchesSearch = quote.quoteNumber.toLowerCase().includes(searchTerm) ||
                                    quote.customerName.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || quote.status === statusFilter;
                const matchesDate = !dateFilter || quote.createdDate.startsWith(dateFilter);

                return matchesSearch && matchesStatus && matchesDate;
            });

            renderFilteredQuotes(filteredQuotes);
        }

        function renderFilteredOpportunities(filteredOpportunities) {
            const tbody = document.getElementById('opportunitiesTableBody');
            if (filteredOpportunities.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune opportunité ne correspond aux critères de recherche
                        </td>
                    </tr>
                `;
                return;
            }

            // Utiliser la même logique de rendu que renderOpportunitiesTable
            tbody.innerHTML = filteredOpportunities.map(opp => `
                <tr>
                    <td><strong>${opp.customerName}</strong></td>
                    <td>${opp.title}</td>
                    <td>${formatCurrency(opp.value)}</td>
                    <td>
                        <div class="probability-bar">
                            <div class="probability-fill" style="width: ${opp.probability}%"></div>
                            <span class="probability-text">${opp.probability}%</span>
                        </div>
                    </td>
                    <td>${getStageBadge(opp.stage)}</td>
                    <td>${formatDate(opp.expectedCloseDate)}</td>
                    <td>${opp.salesRep || 'Non assigné'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon view" onclick="viewOpportunity('${opp.id}')" title="Voir">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="btn-icon edit" onclick="editOpportunity('${opp.id}')" title="Modifier">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="btn-icon delete" onclick="deleteOpportunity('${opp.id}')" title="Supprimer">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function renderFilteredQuotes(filteredQuotes) {
            const tbody = document.getElementById('quotesTableBody');
            if (filteredQuotes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun devis ne correspond aux critères de recherche
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredQuotes.map(quote => `
                <tr>
                    <td><strong>${quote.quoteNumber}</strong></td>
                    <td>${quote.customerName}</td>
                    <td>${formatDate(quote.createdDate)}</td>
                    <td>${formatDate(quote.expirationDate)}</td>
                    <td>${formatCurrency(quote.amountHT)}</td>
                    <td>${formatCurrency(quote.amountTTC)}</td>
                    <td>${getQuoteStatusBadge(quote.status)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon view" onclick="viewQuote('${quote.id}')" title="Voir">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="btn-icon edit" onclick="editQuote('${quote.id}')" title="Modifier">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="btn-icon" onclick="downloadQuote('${quote.id}')" title="Télécharger PDF" style="background: #8b5cf6;">
                                <span class="material-icons">download</span>
                            </button>
                            <button class="btn-icon delete" onclick="deleteQuote('${quote.id}')" title="Supprimer">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'blocked': '<span class="badge badge-danger">Bloqué</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getCustomerTypeBadge(type) {
            const badges = {
                'prospect': '<span class="badge badge-info">Prospect</span>',
                'client': '<span class="badge badge-success">Client</span>',
                'partner': '<span class="badge badge-warning">Partenaire</span>'
            };
            return badges[type] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getStageBadge(stage) {
            const badges = {
                'prospecting': '<span class="badge badge-info">Prospection</span>',
                'qualification': '<span class="badge badge-warning">Qualification</span>',
                'proposal': '<span class="badge badge-warning">Proposition</span>',
                'negotiation': '<span class="badge badge-warning">Négociation</span>',
                'closed_won': '<span class="badge badge-success">Gagné</span>',
                'closed_lost': '<span class="badge badge-danger">Perdu</span>'
            };
            return badges[stage] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getOrderStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'confirmed': '<span class="badge badge-info">Confirmé</span>',
                'shipped': '<span class="badge badge-warning">Expédié</span>',
                'delivered': '<span class="badge badge-success">Livré</span>',
                'cancelled': '<span class="badge badge-danger">Annulé</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getPaymentStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'partial': '<span class="badge badge-warning">Partiel</span>',
                'paid': '<span class="badge badge-success">Payé</span>',
                'overdue': '<span class="badge badge-danger">En retard</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function formatCurrency(amount) {
            if (!amount && amount !== 0) return 'N/A';
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR'
            }).format(amount);
        }

        function getQuoteStatusBadge(status) {
            const badges = {
                'draft': '<span class="badge badge-warning">Brouillon</span>',
                'sent': '<span class="badge badge-info">Envoyé</span>',
                'accepted': '<span class="badge badge-success">Accepté</span>',
                'rejected': '<span class="badge badge-danger">Refusé</span>',
                'expired': '<span class="badge badge-warning">Expiré</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        // Fonctions d'action pour les opportunités
        function createOpportunity() {
            showAlert('Fonction de création d\'opportunité à implémenter', 'info');
        }

        function viewOpportunity(id) {
            const opportunity = opportunities.find(opp => opp.id === id);
            if (opportunity) {
                showAlert(`Affichage de l'opportunité: ${opportunity.title}`, 'info');
            }
        }

        function editOpportunity(id) {
            const opportunity = opportunities.find(opp => opp.id === id);
            if (opportunity) {
                showAlert(`Édition de l'opportunité: ${opportunity.title}`, 'info');
            }
        }

        function deleteOpportunity(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette opportunité ?')) {
                showAlert('Fonction de suppression à implémenter', 'warning');
            }
        }

        // Fonctions d'action pour les devis
        function createQuote() {
            showAlert('Fonction de création de devis à implémenter', 'info');
        }

        function viewQuote(id) {
            const quote = quotes.find(q => q.id === id);
            if (quote) {
                showAlert(`Affichage du devis: ${quote.quoteNumber}`, 'info');
            }
        }

        function editQuote(id) {
            const quote = quotes.find(q => q.id === id);
            if (quote) {
                showAlert(`Édition du devis: ${quote.quoteNumber}`, 'info');
            }
        }

        function deleteQuote(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce devis ?')) {
                showAlert('Fonction de suppression à implémenter', 'warning');
            }
        }

        function downloadQuote(id) {
            const quote = quotes.find(q => q.id === id);
            if (quote) {
                showAlert(`Téléchargement du devis: ${quote.quoteNumber}`, 'info');
            }
        }

        // Fonctions d'export
        function exportOpportunities() {
            showAlert('Export des opportunités en cours...', 'info');
        }

        function exportQuotes() {
            showAlert('Export des devis en cours...', 'info');
        }

        // Fonctions pour les graphiques (à implémenter avec Chart.js)
        function initializeCharts() {
            // Placeholder pour l'initialisation des graphiques
            console.log('Initialisation des graphiques...');
        }

        function updatePipelineChart() {
            console.log('Mise à jour du graphique pipeline...');
        }

        function updateSalesChart() {
            console.log('Mise à jour du graphique des ventes...');
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
