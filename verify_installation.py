# 🔍 SCRIPT DE VÉRIFICATION DE L'INSTALLATION ERP HUB
# Vérifie que tous les composants fonctionnent correctement

import os
import sys
import subprocess
import requests
import time
from datetime import datetime

def print_header():
    """Affiche l'en-tête du script"""
    print("=" * 70)
    print("🔍 VÉRIFICATION DE L'INSTALLATION ERP HUB POSTGRESQL")
    print("=" * 70)
    print(f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_file_exists(file_path, description):
    """Vérifie qu'un fichier existe"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - MANQUANT")
        return False

def check_command(command, description):
    """Vérifie qu'une commande est disponible"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description}: Disponible")
            return True
        else:
            print(f"❌ {description}: Non disponible")
            return False
    except Exception as e:
        print(f"❌ {description}: Erreur - {e}")
        return False

def check_docker_container(container_name):
    """Vérifie qu'un conteneur Docker fonctionne"""
    try:
        result = subprocess.run(f"docker ps --filter name={container_name} --format '{{{{.Names}}}}'", 
                              shell=True, capture_output=True, text=True)
        if container_name in result.stdout:
            print(f"✅ Conteneur Docker {container_name}: En cours d'exécution")
            return True
        else:
            print(f"❌ Conteneur Docker {container_name}: Non trouvé")
            return False
    except Exception as e:
        print(f"❌ Conteneur Docker {container_name}: Erreur - {e}")
        return False

def check_api_endpoint(url, description):
    """Vérifie qu'un endpoint API répond"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ {description}: Accessible (HTTP {response.status_code})")
            return True
        else:
            print(f"❌ {description}: Erreur HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {description}: Connexion refusée")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {description}: Timeout")
        return False
    except Exception as e:
        print(f"❌ {description}: Erreur - {e}")
        return False

def main():
    """Fonction principale de vérification"""
    print_header()
    
    # Compteurs
    total_checks = 0
    passed_checks = 0
    
    # 1. Vérification des fichiers essentiels
    print("📁 VÉRIFICATION DES FICHIERS ESSENTIELS")
    print("-" * 50)
    
    essential_files = [
        ("postgresql_api_server.py", "Serveur API principal"),
        ("docker-compose-postgresql.yml", "Configuration Docker"),
        ("create_all_erp_tables.sql", "Script création tables"),
        ("insert_demo_data_all_agents.sql", "Données de démonstration"),
        ("requirements.txt", "Dépendances Python"),
        ("frontend/dashboard-global-postgresql.html", "Dashboard Global"),
        ("frontend/hr-management-postgresql.html", "Page HR"),
        ("frontend/sales-management-postgresql.html", "Page Sales"),
        ("frontend/purchase-management-postgresql.html", "Page Purchase"),
        ("frontend/stock-management-postgresql.html", "Page Stock"),
        ("frontend/logistics-management-postgresql.html", "Page Logistics"),
        ("frontend/crm-management-postgresql.html", "Page CRM"),
        ("frontend/bi-management-postgresql.html", "Page BI"),
    ]
    
    for file_path, description in essential_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 2. Vérification des prérequis système
    print("🛠️ VÉRIFICATION DES PRÉREQUIS SYSTÈME")
    print("-" * 50)
    
    system_requirements = [
        ("python --version", "Python"),
        ("docker --version", "Docker"),
        ("docker-compose --version", "Docker Compose"),
    ]
    
    for command, description in system_requirements:
        total_checks += 1
        if check_command(command, description):
            passed_checks += 1
    
    print()
    
    # 3. Vérification des dépendances Python
    print("🐍 VÉRIFICATION DES DÉPENDANCES PYTHON")
    print("-" * 50)
    
    python_modules = [
        "flask",
        "psycopg2",
        "requests",
    ]
    
    for module in python_modules:
        total_checks += 1
        try:
            __import__(module)
            print(f"✅ Module Python {module}: Installé")
            passed_checks += 1
        except ImportError:
            print(f"❌ Module Python {module}: Non installé")
    
    print()
    
    # 4. Vérification des conteneurs Docker
    print("🐳 VÉRIFICATION DES CONTENEURS DOCKER")
    print("-" * 50)
    
    docker_containers = [
        "erp_postgres",
    ]
    
    for container in docker_containers:
        total_checks += 1
        if check_docker_container(container):
            passed_checks += 1
    
    print()
    
    # 5. Vérification des endpoints API
    print("🌐 VÉRIFICATION DES ENDPOINTS API")
    print("-" * 50)
    
    api_endpoints = [
        ("http://localhost:5000/api/health", "Health Check"),
        ("http://localhost:5000/api/dashboard", "Dashboard"),
        ("http://localhost:5000/api/employees", "Employés"),
        ("http://localhost:5000/api/customers", "Clients"),
        ("http://localhost:5000/api/products", "Produits"),
    ]
    
    for url, description in api_endpoints:
        total_checks += 1
        if check_api_endpoint(url, description):
            passed_checks += 1
    
    print()
    
    # 6. Résumé final
    print("=" * 70)
    print("📊 RÉSUMÉ DE LA VÉRIFICATION")
    print("=" * 70)
    
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"✅ Tests réussis: {passed_checks}/{total_checks}")
    print(f"📈 Taux de succès: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 INSTALLATION EXCELLENTE ! Tous les composants fonctionnent.")
        status = "EXCELLENT"
    elif success_rate >= 75:
        print("✅ INSTALLATION BONNE. Quelques problèmes mineurs détectés.")
        status = "BON"
    elif success_rate >= 50:
        print("⚠️ INSTALLATION PARTIELLE. Plusieurs problèmes détectés.")
        status = "PARTIEL"
    else:
        print("❌ INSTALLATION DÉFAILLANTE. Problèmes majeurs détectés.")
        status = "DÉFAILLANT"
    
    print()
    
    # 7. Recommandations
    if success_rate < 100:
        print("🔧 RECOMMANDATIONS")
        print("-" * 50)
        
        if passed_checks < total_checks:
            print("1. Vérifiez les éléments marqués ❌ ci-dessus")
            print("2. Installez les dépendances manquantes:")
            print("   pip install -r requirements.txt")
            print("3. Démarrez Docker Desktop si nécessaire")
            print("4. Lancez le système avec start_erp_postgresql.bat/.sh")
            print("5. Relancez cette vérification")
    
    print()
    print("🏁 Vérification terminée")
    
    return status

if __name__ == "__main__":
    try:
        status = main()
        
        # Code de sortie selon le statut
        if status == "EXCELLENT":
            sys.exit(0)
        elif status == "BON":
            sys.exit(1)
        elif status == "PARTIEL":
            sys.exit(2)
        else:
            sys.exit(3)
            
    except KeyboardInterrupt:
        print("\n🛑 Vérification interrompue par l'utilisateur")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        sys.exit(1)
