// 🔐 GESTIONNAIRE D'AUTHENTIFICATION ERP HUB
// Système complet de connexion, permissions et sécurité

class ERPAuthManager {
    constructor(apiBaseUrl = 'http://localhost:5000/api') {
        this.apiBaseUrl = apiBaseUrl;
        this.token = localStorage.getItem('erp_access_token');
        this.user = JSON.parse(localStorage.getItem('erp_user') || 'null');
        this.permissions = JSON.parse(localStorage.getItem('erp_permissions') || '{}');
        
        // Vérifier l'état de connexion au démarrage
        this.checkAuthStatus();
    }

    // ===== AUTHENTIFICATION =====

    async login(username, password) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const result = await response.json();

            if (result.success) {
                // Sauvegarder les informations de connexion
                this.token = result.access_token;
                this.user = result.user;
                
                localStorage.setItem('erp_access_token', this.token);
                localStorage.setItem('erp_user', JSON.stringify(this.user));
                
                // Charger les permissions
                await this.loadUserPermissions();
                
                console.log('✅ Connexion réussie');
                this.showAuthStatus('success', `Bienvenue ${this.user.first_name} !`);
                
                return { success: true, user: this.user };
            } else {
                console.log('❌ Échec de la connexion');
                this.showAuthStatus('error', result.error);
                return { success: false, error: result.error };
            }
        } catch (error) {
            console.error('❌ Erreur de connexion:', error);
            this.showAuthStatus('error', 'Erreur de connexion au serveur');
            return { success: false, error: 'Erreur de connexion' };
        }
    }

    async logout() {
        try {
            // Nettoyer les données locales
            this.token = null;
            this.user = null;
            this.permissions = {};
            
            localStorage.removeItem('erp_access_token');
            localStorage.removeItem('erp_user');
            localStorage.removeItem('erp_permissions');
            
            console.log('✅ Déconnexion réussie');
            this.showAuthStatus('info', 'Déconnexion réussie');
            
            // Rediriger vers la page de connexion
            this.redirectToLogin();
            
            return true;
        } catch (error) {
            console.error('❌ Erreur de déconnexion:', error);
            return false;
        }
    }

    async checkAuthStatus() {
        if (!this.token || !this.user) {
            return false;
        }

        try {
            // Vérifier la validité du token
            const response = await fetch(`${this.apiBaseUrl}/auth/profile`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.user = result.user;
                    localStorage.setItem('erp_user', JSON.stringify(this.user));
                    await this.loadUserPermissions();
                    return true;
                }
            }
            
            // Token invalide, déconnecter
            await this.logout();
            return false;
            
        } catch (error) {
            console.error('❌ Erreur vérification auth:', error);
            return false;
        }
    }

    async loadUserPermissions() {
        // Pour l'instant, simuler les permissions basées sur le rôle
        // Dans une vraie implémentation, ceci viendrait de l'API
        const rolePermissions = {
            'admin': {
                'budgets': ['create', 'read', 'update', 'delete', 'export', 'import'],
                'movements': ['create', 'read', 'update', 'delete', 'export', 'import'],
                'accounts': ['create', 'read', 'update', 'delete', 'export', 'import'],
                'users': ['create', 'read', 'update', 'delete'],
                'reports': ['read', 'export']
            },
            'manager': {
                'budgets': ['create', 'read', 'update', 'delete', 'export'],
                'movements': ['create', 'read', 'update', 'export'],
                'accounts': ['read', 'export'],
                'reports': ['read', 'export']
            },
            'user': {
                'budgets': ['read', 'export'],
                'movements': ['read'],
                'accounts': ['read'],
                'reports': ['read']
            }
        };

        this.permissions = rolePermissions[this.user?.role] || rolePermissions['user'];
        localStorage.setItem('erp_permissions', JSON.stringify(this.permissions));
    }

    // ===== GESTION DES PERMISSIONS =====

    hasPermission(module, action) {
        if (!this.permissions || !this.permissions[module]) {
            return false;
        }
        return this.permissions[module].includes(action);
    }

    requirePermission(module, action) {
        if (!this.hasPermission(module, action)) {
            this.showAuthStatus('error', `Permission refusée : ${action} sur ${module}`);
            throw new Error(`Permission refusée : ${action} sur ${module}`);
        }
        return true;
    }

    // ===== REQUÊTES AUTHENTIFIÉES =====

    async authenticatedRequest(url, options = {}) {
        if (!this.token) {
            throw new Error('Non authentifié');
        }

        const headers = {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
            ...options.headers
        };

        try {
            const response = await fetch(url, {
                ...options,
                headers
            });

            // Vérifier si le token a expiré
            if (response.status === 401) {
                await this.logout();
                throw new Error('Session expirée, veuillez vous reconnecter');
            }

            return response;
        } catch (error) {
            console.error('❌ Erreur requête authentifiée:', error);
            throw error;
        }
    }

    // ===== INTERFACE UTILISATEUR =====

    showAuthStatus(type, message) {
        // Créer ou mettre à jour l'indicateur d'authentification
        let indicator = document.querySelector('.auth-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auth-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            document.body.appendChild(indicator);
        }

        // Styles selon le type
        const styles = {
            'success': 'background: #10b981; border-left: 4px solid #059669;',
            'error': 'background: #ef4444; border-left: 4px solid #dc2626;',
            'warning': 'background: #f59e0b; border-left: 4px solid #d97706;',
            'info': 'background: #3b82f6; border-left: 4px solid #2563eb;'
        };

        indicator.style.cssText += styles[type] || styles['info'];
        indicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span class="material-icons" style="font-size: 18px;">
                    ${type === 'success' ? 'check_circle' : 
                      type === 'error' ? 'error' : 
                      type === 'warning' ? 'warning' : 'info'}
                </span>
                <span>${message}</span>
            </div>
        `;

        // Masquer après 5 secondes
        setTimeout(() => {
            if (indicator && indicator.parentNode) {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    if (indicator && indicator.parentNode) {
                        indicator.parentNode.removeChild(indicator);
                    }
                }, 300);
            }
        }, 5000);
    }

    createLoginForm() {
        return `
            <div class="login-container" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            ">
                <div class="login-form" style="
                    background: white;
                    padding: 40px;
                    border-radius: 16px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    width: 100%;
                    max-width: 400px;
                    text-align: center;
                ">
                    <div style="margin-bottom: 30px;">
                        <h1 style="color: #1f2937; margin: 0 0 8px 0; font-size: 28px;">ERP HUB</h1>
                        <p style="color: #6b7280; margin: 0; font-size: 16px;">Connexion sécurisée</p>
                    </div>
                    
                    <form id="loginForm" style="text-align: left;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; color: #374151; font-weight: 500;">
                                Nom d'utilisateur
                            </label>
                            <input 
                                type="text" 
                                id="username" 
                                required
                                style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    transition: border-color 0.2s;
                                    box-sizing: border-box;
                                "
                                placeholder="Votre nom d'utilisateur"
                            >
                        </div>
                        
                        <div style="margin-bottom: 30px;">
                            <label style="display: block; margin-bottom: 8px; color: #374151; font-weight: 500;">
                                Mot de passe
                            </label>
                            <input 
                                type="password" 
                                id="password" 
                                required
                                style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    transition: border-color 0.2s;
                                    box-sizing: border-box;
                                "
                                placeholder="Votre mot de passe"
                            >
                        </div>
                        
                        <button 
                            type="submit"
                            style="
                                width: 100%;
                                padding: 14px;
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                border: none;
                                border-radius: 8px;
                                font-size: 16px;
                                font-weight: 600;
                                cursor: pointer;
                                transition: transform 0.2s;
                            "
                            onmouseover="this.style.transform='translateY(-2px)'"
                            onmouseout="this.style.transform='translateY(0)'"
                        >
                            Se connecter
                        </button>
                    </form>
                    
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            Compte de démonstration :<br>
                            <strong>admin</strong> / <strong>Admin123!</strong>
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    showLoginForm() {
        // Supprimer le formulaire existant s'il y en a un
        const existingForm = document.querySelector('.login-container');
        if (existingForm) {
            existingForm.remove();
        }

        // Créer et afficher le formulaire de connexion
        const loginHtml = this.createLoginForm();
        document.body.insertAdjacentHTML('beforeend', loginHtml);

        // Ajouter les gestionnaires d'événements
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const result = await this.login(username, password);
            
            if (result.success) {
                // Supprimer le formulaire de connexion
                document.querySelector('.login-container').remove();
                
                // Recharger la page ou rediriger
                window.location.reload();
            }
        });

        // Focus sur le champ username
        document.getElementById('username').focus();
    }

    redirectToLogin() {
        this.showLoginForm();
    }

    // ===== GETTERS =====

    isAuthenticated() {
        return !!(this.token && this.user);
    }

    getCurrentUser() {
        return this.user;
    }

    getUserRole() {
        return this.user?.role || 'guest';
    }

    getUserDepartment() {
        return this.user?.department || '';
    }

    getAuthToken() {
        return this.token;
    }
}

// ===== INSTANCE GLOBALE =====

// Créer une instance globale du gestionnaire d'authentification
window.erpAuth = new ERPAuthManager();

// Vérifier l'authentification au chargement de la page
document.addEventListener('DOMContentLoaded', async () => {
    const isAuthenticated = await window.erpAuth.checkAuthStatus();
    
    if (!isAuthenticated) {
        console.log('🔒 Utilisateur non authentifié');
        window.erpAuth.showLoginForm();
    } else {
        console.log('✅ Utilisateur authentifié:', window.erpAuth.getCurrentUser());
    }
});

// Ajouter un bouton de déconnexion global
window.addEventListener('load', () => {
    if (window.erpAuth.isAuthenticated()) {
        // Ajouter un indicateur d'utilisateur connecté
        const userIndicator = document.createElement('div');
        userIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 8px 16px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        `;
        
        const user = window.erpAuth.getCurrentUser();
        userIndicator.innerHTML = `
            <span class="material-icons" style="font-size: 16px; color: #10b981;">account_circle</span>
            <span>${user.first_name} ${user.last_name}</span>
            <button onclick="window.erpAuth.logout()" style="
                background: #ef4444;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                margin-left: 8px;
            ">Déconnexion</button>
        `;
        
        document.body.appendChild(userIndicator);
    }
});

console.log('🔐 Gestionnaire d\'authentification ERP HUB initialisé');
console.log('📋 Utilisation:');
console.log('   erpAuth.login(username, password)');
console.log('   erpAuth.logout()');
console.log('   erpAuth.hasPermission(module, action)');
console.log('   erpAuth.authenticatedRequest(url, options)');
