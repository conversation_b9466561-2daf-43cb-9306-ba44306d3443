# 📊 SYSTÈME DE MONITORING AVANCÉ ERP HUB
# Monitoring en temps réel avec alertes automatiques

import time
import psutil
import logging
import json
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import <PERSON>me<PERSON>ultipart
from threading import Thread, Event
import requests
import os
from dataclasses import dataclass
from typing import Dict, List, Optional
from redis_cache import cache

@dataclass
class Alert:
    """Classe pour représenter une alerte"""
    id: str
    level: str  # 'info', 'warning', 'error', 'critical'
    title: str
    message: str
    timestamp: datetime
    source: str
    resolved: bool = False
    
class ERPMonitoring:
    """Système de monitoring pour ERP HUB"""
    
    def __init__(self):
        self.running = False
        self.stop_event = Event()
        self.alerts = []
        self.metrics = {}
        
        # Configuration
        self.check_interval = 30  # secondes
        self.alert_thresholds = {
            'cpu_usage': 80,
            'memory_usage': 85,
            'disk_usage': 90,
            'response_time': 2.0,
            'error_rate': 5.0,
            'failed_logins': 10
        }
        
        # Configuration email
        self.email_config = {
            'smtp_server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
            'smtp_port': int(os.getenv('SMTP_PORT', '587')),
            'email_user': os.getenv('EMAIL_USER', ''),
            'email_password': os.getenv('EMAIL_PASSWORD', ''),
            'alert_recipients': os.getenv('ALERT_RECIPIENTS', '').split(',')
        }
        
        # Logger
        self.logger = logging.getLogger('monitoring')
        self.logger.setLevel(logging.INFO)
        
        # Handler pour fichier de monitoring
        handler = logging.FileHandler('monitoring.log')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def start_monitoring(self):
        """Démarrer le monitoring"""
        if self.running:
            return
        
        self.running = True
        self.stop_event.clear()
        
        # Démarrer les threads de monitoring
        threads = [
            Thread(target=self._monitor_system_resources, daemon=True),
            Thread(target=self._monitor_application_health, daemon=True),
            Thread(target=self._monitor_database_performance, daemon=True),
            Thread(target=self._monitor_security_events, daemon=True),
            Thread(target=self._process_alerts, daemon=True)
        ]
        
        for thread in threads:
            thread.start()
        
        self.logger.info("🚀 Monitoring ERP HUB démarré")
        print("📊 Monitoring ERP HUB démarré")
    
    def stop_monitoring(self):
        """Arrêter le monitoring"""
        self.running = False
        self.stop_event.set()
        self.logger.info("⏹️ Monitoring ERP HUB arrêté")
    
    # ===== MONITORING DES RESSOURCES SYSTÈME =====
    
    def _monitor_system_resources(self):
        """Surveiller les ressources système"""
        while self.running and not self.stop_event.wait(self.check_interval):
            try:
                # CPU
                cpu_percent = psutil.cpu_percent(interval=1)
                self.metrics['cpu_usage'] = cpu_percent
                
                if cpu_percent > self.alert_thresholds['cpu_usage']:
                    self._create_alert(
                        'warning',
                        'CPU Usage élevé',
                        f'Utilisation CPU: {cpu_percent:.1f}%',
                        'system'
                    )
                
                # Mémoire
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                self.metrics['memory_usage'] = memory_percent
                
                if memory_percent > self.alert_thresholds['memory_usage']:
                    self._create_alert(
                        'warning',
                        'Mémoire Usage élevé',
                        f'Utilisation mémoire: {memory_percent:.1f}%',
                        'system'
                    )
                
                # Disque
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.metrics['disk_usage'] = disk_percent
                
                if disk_percent > self.alert_thresholds['disk_usage']:
                    self._create_alert(
                        'error',
                        'Espace disque faible',
                        f'Utilisation disque: {disk_percent:.1f}%',
                        'system'
                    )
                
                # Processus
                self.metrics['process_count'] = len(psutil.pids())
                
                # Load average (Linux/Mac)
                try:
                    load_avg = os.getloadavg()
                    self.metrics['load_average'] = load_avg[0]
                except:
                    pass
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring système: {e}")
    
    def _monitor_application_health(self):
        """Surveiller la santé de l'application"""
        while self.running and not self.stop_event.wait(self.check_interval):
            try:
                # Test de l'API
                start_time = time.time()
                try:
                    response = requests.get('http://localhost:5000/api/health', timeout=5)
                    response_time = time.time() - start_time
                    
                    self.metrics['api_response_time'] = response_time
                    self.metrics['api_status'] = response.status_code
                    
                    if response_time > self.alert_thresholds['response_time']:
                        self._create_alert(
                            'warning',
                            'API lente',
                            f'Temps de réponse: {response_time:.2f}s',
                            'application'
                        )
                    
                    if response.status_code != 200:
                        self._create_alert(
                            'error',
                            'API non disponible',
                            f'Status code: {response.status_code}',
                            'application'
                        )
                
                except requests.RequestException as e:
                    self._create_alert(
                        'critical',
                        'API inaccessible',
                        f'Erreur: {str(e)}',
                        'application'
                    )
                    self.metrics['api_status'] = 'down'
                
                # Test Redis
                if cache.connected:
                    try:
                        cache_stats = cache.get_cache_stats()
                        self.metrics['redis_status'] = 'up'
                        self.metrics['redis_hit_rate'] = cache_stats.get('hit_rate', 0)
                        
                        if cache_stats.get('hit_rate', 0) < 50:
                            self._create_alert(
                                'info',
                                'Cache hit rate faible',
                                f'Hit rate: {cache_stats.get("hit_rate", 0):.1f}%',
                                'cache'
                            )
                    except:
                        self.metrics['redis_status'] = 'down'
                        self._create_alert(
                            'error',
                            'Redis inaccessible',
                            'Cache Redis non disponible',
                            'cache'
                        )
                else:
                    self.metrics['redis_status'] = 'down'
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring application: {e}")
    
    def _monitor_database_performance(self):
        """Surveiller les performances de la base de données"""
        while self.running and not self.stop_event.wait(self.check_interval * 2):  # Moins fréquent
            try:
                from database_postgresql import ERPDatabasePostgreSQL
                
                db = ERPDatabasePostgreSQL()
                
                # Test de connexion
                start_time = time.time()
                try:
                    conn = db.get_connection()
                    cursor = conn.cursor()
                    cursor.execute('SELECT 1')
                    cursor.fetchone()
                    
                    db_response_time = time.time() - start_time
                    self.metrics['db_response_time'] = db_response_time
                    self.metrics['db_status'] = 'up'
                    
                    if db_response_time > 1.0:
                        self._create_alert(
                            'warning',
                            'Base de données lente',
                            f'Temps de réponse DB: {db_response_time:.2f}s',
                            'database'
                        )
                    
                    # Statistiques de la DB
                    cursor.execute("""
                        SELECT 
                            count(*) as active_connections
                        FROM pg_stat_activity 
                        WHERE state = 'active'
                    """)
                    
                    active_connections = cursor.fetchone()[0]
                    self.metrics['db_active_connections'] = active_connections
                    
                    if active_connections > 50:
                        self._create_alert(
                            'warning',
                            'Nombreuses connexions DB',
                            f'Connexions actives: {active_connections}',
                            'database'
                        )
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    self.metrics['db_status'] = 'down'
                    self._create_alert(
                        'critical',
                        'Base de données inaccessible',
                        f'Erreur: {str(e)}',
                        'database'
                    )
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring database: {e}")
    
    def _monitor_security_events(self):
        """Surveiller les événements de sécurité"""
        while self.running and not self.stop_event.wait(self.check_interval):
            try:
                # Analyser les logs de sécurité
                self._analyze_security_logs()
                
                # Vérifier les tentatives de connexion échouées
                if cache.connected:
                    failed_login_pattern = "login_attempts:*"
                    # Ici vous pourriez compter les tentatives échouées
                    # et créer des alertes si nécessaire
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring sécurité: {e}")
    
    def _analyze_security_logs(self):
        """Analyser les logs de sécurité"""
        try:
            # Lire les dernières entrées du log de sécurité
            with open('security.log', 'r') as f:
                lines = f.readlines()
                recent_lines = lines[-100:]  # 100 dernières lignes
                
                # Compter les événements suspects
                failed_logins = sum(1 for line in recent_lines if 'login_failed' in line)
                
                if failed_logins > self.alert_thresholds['failed_logins']:
                    self._create_alert(
                        'warning',
                        'Tentatives de connexion suspectes',
                        f'{failed_logins} tentatives échouées récentes',
                        'security'
                    )
        
        except FileNotFoundError:
            pass  # Fichier de log pas encore créé
        except Exception as e:
            self.logger.error(f"Erreur analyse logs sécurité: {e}")
    
    # ===== GESTION DES ALERTES =====
    
    def _create_alert(self, level: str, title: str, message: str, source: str):
        """Créer une nouvelle alerte"""
        alert_id = f"{source}_{level}_{int(time.time())}"
        
        # Éviter les doublons récents
        recent_alerts = [a for a in self.alerts if 
                        a.title == title and 
                        a.timestamp > datetime.now() - timedelta(minutes=10)]
        
        if recent_alerts:
            return  # Alerte similaire récente
        
        alert = Alert(
            id=alert_id,
            level=level,
            title=title,
            message=message,
            timestamp=datetime.now(),
            source=source
        )
        
        self.alerts.append(alert)
        self.logger.warning(f"ALERT [{level.upper()}] {title}: {message}")
        
        # Limiter le nombre d'alertes en mémoire
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-500:]
    
    def _process_alerts(self):
        """Traiter les alertes (envoi email, notifications)"""
        while self.running and not self.stop_event.wait(60):  # Vérifier chaque minute
            try:
                # Alertes non traitées
                unprocessed_alerts = [a for a in self.alerts if not a.resolved]
                
                # Grouper par niveau de criticité
                critical_alerts = [a for a in unprocessed_alerts if a.level == 'critical']
                error_alerts = [a for a in unprocessed_alerts if a.level == 'error']
                
                # Envoyer les alertes critiques immédiatement
                for alert in critical_alerts:
                    self._send_alert_email(alert)
                    alert.resolved = True
                
                # Envoyer un résumé des erreurs
                if error_alerts:
                    self._send_summary_email(error_alerts)
                    for alert in error_alerts:
                        alert.resolved = True
                
            except Exception as e:
                self.logger.error(f"Erreur traitement alertes: {e}")
    
    def _send_alert_email(self, alert: Alert):
        """Envoyer une alerte par email"""
        if not self.email_config['email_user'] or not self.email_config['alert_recipients']:
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = self.email_config['email_user']
            msg['To'] = ', '.join(self.email_config['alert_recipients'])
            msg['Subject'] = f"[ERP HUB] Alerte {alert.level.upper()}: {alert.title}"
            
            body = f"""
            Alerte ERP HUB
            
            Niveau: {alert.level.upper()}
            Titre: {alert.title}
            Message: {alert.message}
            Source: {alert.source}
            Timestamp: {alert.timestamp}
            
            Vérifiez le système immédiatement.
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['email_user'], self.email_config['email_password'])
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"Alerte envoyée par email: {alert.title}")
            
        except Exception as e:
            self.logger.error(f"Erreur envoi email: {e}")
    
    def _send_summary_email(self, alerts: List[Alert]):
        """Envoyer un résumé des alertes par email"""
        if not alerts:
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = self.email_config['email_user']
            msg['To'] = ', '.join(self.email_config['alert_recipients'])
            msg['Subject'] = f"[ERP HUB] Résumé des alertes ({len(alerts)} alertes)"
            
            body = "Résumé des alertes ERP HUB\n\n"
            
            for alert in alerts:
                body += f"- [{alert.level.upper()}] {alert.title}: {alert.message}\n"
            
            body += f"\nTotal: {len(alerts)} alertes\n"
            body += f"Timestamp: {datetime.now()}\n"
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['email_user'], self.email_config['email_password'])
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"Résumé d'alertes envoyé: {len(alerts)} alertes")
            
        except Exception as e:
            self.logger.error(f"Erreur envoi résumé: {e}")
    
    # ===== API PUBLIQUE =====
    
    def get_metrics(self) -> Dict:
        """Obtenir les métriques actuelles"""
        return {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.metrics.copy(),
            'alerts_count': len([a for a in self.alerts if not a.resolved]),
            'status': 'healthy' if self.metrics.get('api_status') == 200 else 'unhealthy'
        }
    
    def get_alerts(self, level: Optional[str] = None) -> List[Dict]:
        """Obtenir les alertes"""
        alerts = self.alerts
        
        if level:
            alerts = [a for a in alerts if a.level == level]
        
        return [
            {
                'id': a.id,
                'level': a.level,
                'title': a.title,
                'message': a.message,
                'timestamp': a.timestamp.isoformat(),
                'source': a.source,
                'resolved': a.resolved
            }
            for a in alerts[-50:]  # 50 dernières alertes
        ]
    
    def get_health_status(self) -> Dict:
        """Obtenir le statut de santé global"""
        critical_alerts = len([a for a in self.alerts if a.level == 'critical' and not a.resolved])
        error_alerts = len([a for a in self.alerts if a.level == 'error' and not a.resolved])
        
        if critical_alerts > 0:
            status = 'critical'
        elif error_alerts > 0:
            status = 'degraded'
        elif self.metrics.get('api_status') != 200:
            status = 'unhealthy'
        else:
            status = 'healthy'
        
        return {
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'metrics': self.metrics,
            'alerts': {
                'critical': critical_alerts,
                'error': error_alerts,
                'warning': len([a for a in self.alerts if a.level == 'warning' and not a.resolved]),
                'total': len([a for a in self.alerts if not a.resolved])
            }
        }

# ===== INSTANCE GLOBALE =====

monitoring = ERPMonitoring()

# ===== FONCTIONS UTILITAIRES =====

def start_monitoring():
    """Démarrer le monitoring"""
    monitoring.start_monitoring()

def stop_monitoring():
    """Arrêter le monitoring"""
    monitoring.stop_monitoring()

def get_system_health():
    """Obtenir la santé du système"""
    return monitoring.get_health_status()

if __name__ == '__main__':
    print("📊 Démarrage du monitoring ERP HUB...")
    monitoring.start_monitoring()
    
    try:
        while True:
            time.sleep(10)
            health = monitoring.get_health_status()
            print(f"Status: {health['status']} - Alertes: {health['alerts']['total']}")
    except KeyboardInterrupt:
        print("\n⏹️ Arrêt du monitoring...")
        monitoring.stop_monitoring()
