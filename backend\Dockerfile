# Dockerfile pour le backend Django - Production
FROM python:3.11-slim

# Variables d'environnement
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV DJANGO_SETTINGS_MODULE=config.settings.production

# Création d'un utilisateur non-root
RUN groupadd -r django && useradd -r -g django django

# Répertoire de travail
WORKDIR /app

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copie et installation des dépendances Python
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt gunicorn gevent

# Copie du code source
COPY . /app/

# Création des dossiers nécessaires
RUN mkdir -p /app/static /app/media /app/logs /app/staticfiles && \
    chown -R django:django /app

# Permissions
RUN chmod +x /app/manage.py

# Collecte des fichiers statiques (sera fait au runtime avec les bonnes variables)
# RUN python manage.py collectstatic --noinput

# Changement vers l'utilisateur non-root
USER django

# Port d'exposition
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1

# Commande par défaut optimisée pour la production
CMD ["gunicorn", "config.wsgi:application", \
     "--bind", "0.0.0.0:8000", \
     "--workers", "4", \
     "--worker-class", "gevent", \
     "--worker-connections", "1000", \
     "--max-requests", "1000", \
     "--max-requests-jitter", "100", \
     "--timeout", "30", \
     "--keep-alive", "2", \
     "--access-logfile", "-", \
     "--error-logfile", "-"]
