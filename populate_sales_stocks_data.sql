-- 📊 SCRIPT DE PEUPLEMENT DES DONNÉES SALES & STOCKS
-- Données de test pour finaliser les modules Sales et Stocks

-- ===== DONNÉES CLIENTS =====

INSERT INTO customers (id, customer_code, company_name, contact_person, email, phone, address, city, postal_code, country, industry, customer_type, credit_limit, payment_terms, sales_rep_id, status, created_date, modified_date) VALUES
('cust-001', 'CLI001', 'TechCorp Solutions', '<PERSON>', '<EMAIL>', '+33 1 23 45 67 89', '123 Avenue des Champs', 'Paris', '75008', 'France', 'Technologie', 'client', 150000.00, 30, 'emp-001', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cust-002', 'CLI002', 'InnovateX', '<PERSON>', '<EMAIL>', '+33 1 98 76 54 32', '456 <PERSON> de la Paix', 'Lyon', '69000', 'France', 'Innovation', 'client', 100000.00, 45, 'emp-002', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cust-003', 'CLI003', 'DataFlow Systems', 'Sophie Laurent', '<EMAIL>', '+33 1 11 22 33 44', '789 Boulevard Tech', 'Marseille', '13000', 'France', 'Data Analytics', 'prospect', 75000.00, 30, 'emp-003', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cust-004', 'CLI004', 'CloudFirst', 'Pierre Durand', '<EMAIL>', '+33 1 55 66 77 88', '321 Rue du Cloud', 'Toulouse', '31000', 'France', 'Cloud Computing', 'client', 200000.00, 30, 'emp-001', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cust-005', 'CLI005', 'SecureNet', 'Anne Moreau', '<EMAIL>', '+33 1 99 88 77 66', '654 Avenue Sécurité', 'Nice', '06000', 'France', 'Cybersécurité', 'prospect', 120000.00, 60, 'emp-002', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES OPPORTUNITÉS =====

INSERT INTO opportunities (id, customer_id, title, description, value, probability, stage, expected_close_date, actual_close_date, sales_rep_id, source, competitor, next_action, created_date, modified_date) VALUES
('opp-001', 'cust-001', 'Migration ERP Cloud', 'Migration complète vers solution ERP cloud avec formation équipe', 120000.00, 85, 'negotiation', '2024-07-15', NULL, 'emp-001', 'Référence client', 'SAP', 'Finaliser contrat et conditions', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('opp-002', 'cust-002', 'Système CRM Intégré', 'Implémentation CRM avec intégration API existante', 85000.00, 70, 'proposal', '2024-07-30', NULL, 'emp-002', 'Site web', 'Salesforce', 'Présentation solution technique', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('opp-003', 'cust-003', 'Analytics Dashboard', 'Tableau de bord analytics temps réel pour données business', 45000.00, 60, 'qualification', '2024-08-15', NULL, 'emp-003', 'Salon professionnel', 'Tableau', 'Démonstration produit', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('opp-004', 'cust-004', 'Infrastructure Cloud', 'Migration infrastructure complète vers cloud hybride', 180000.00, 90, 'negotiation', '2024-06-30', NULL, 'emp-001', 'Recommandation', 'AWS', 'Signature contrat imminent', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('opp-005', 'cust-005', 'Audit Sécurité', 'Audit complet sécurité et mise en conformité RGPD', 35000.00, 40, 'prospecting', '2024-09-01', NULL, 'emp-002', 'Appel à froid', 'Deloitte', 'Planifier rendez-vous technique', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('opp-006', 'cust-001', 'Formation Équipe', 'Formation avancée équipe IT sur nouvelles technologies', 25000.00, 95, 'closed_won', '2024-06-01', '2024-05-28', 'emp-001', 'Client existant', NULL, 'Commande confirmée', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES COMMANDES =====

INSERT INTO orders (id, order_number, customer_id, opportunity_id, order_date, delivery_date, status, total_amount, tax_amount, discount_amount, shipping_address, billing_address, payment_method, payment_status, sales_rep_id, notes, created_date, modified_date) VALUES
('ord-001', 'CMD-2024-001', 'cust-001', 'opp-006', '2024-05-28', '2024-07-15', 'confirmed', 25000.00, 5000.00, 0.00, '123 Avenue des Champs, Paris 75008', '123 Avenue des Champs, Paris 75008', 'Virement', 'paid', 'emp-001', 'Formation confirmée pour juillet', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('ord-002', 'CMD-2024-002', 'cust-002', NULL, '2024-06-05', '2024-06-20', 'shipped', 32000.00, 6400.00, 2000.00, '456 Rue de la Paix, Lyon 69000', '456 Rue de la Paix, Lyon 69000', 'Carte bancaire', 'paid', 'emp-002', 'Livraison express demandée', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('ord-003', 'CMD-2024-003', 'cust-004', NULL, '2024-06-10', '2024-07-01', 'pending', 15000.00, 3000.00, 500.00, '321 Rue du Cloud, Toulouse 31000', '321 Rue du Cloud, Toulouse 31000', 'Virement', 'pending', 'emp-001', 'En attente validation budget', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES DEVIS =====

INSERT INTO quotes (id, quote_number, client_id, client_name, quote_date, validity_date, amount_ht, amount_tva, amount_ttc, status, description, file_path, created_date, modified_date) VALUES
('quote-001', 'DEV-2024-001', 'cust-003', 'DataFlow Systems', '2024-06-01', '2024-07-01', 45000.00, 9000.00, 54000.00, 'sent', 'Tableau de bord analytics personnalisé', '/documents/quotes/DEV-2024-001.pdf', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('quote-002', 'DEV-2024-002', 'cust-005', 'SecureNet', '2024-06-05', '2024-07-05', 35000.00, 7000.00, 42000.00, 'draft', 'Audit sécurité et conformité RGPD', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('quote-003', 'DEV-2024-003', 'cust-001', 'TechCorp Solutions', '2024-05-15', '2024-06-15', 120000.00, 24000.00, 144000.00, 'accepted', 'Migration ERP Cloud complète', '/documents/quotes/DEV-2024-003.pdf', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES FOURNISSEURS =====

INSERT INTO suppliers (id, supplier_code, company_name, contact_person, email, phone, address, city, postal_code, country, industry, payment_terms, credit_rating, preferred, status, created_date, modified_date) VALUES
('sup-001', 'FOUR001', 'TechSupply Pro', 'Michel Leroy', '<EMAIL>', '+33 1 44 55 66 77', '100 Rue de la Tech', 'Paris', '75001', 'France', 'Matériel informatique', 30, 'A+', true, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('sup-002', 'FOUR002', 'Software Solutions', 'Claire Petit', '<EMAIL>', '+33 1 33 44 55 66', '200 Avenue du Code', 'Lyon', '69001', 'France', 'Logiciels', 45, 'A', true, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('sup-003', 'FOUR003', 'CloudServices Inc', 'David Wilson', '<EMAIL>', '****** 123 4567', '300 Cloud Street', 'San Francisco', '94105', 'USA', 'Services Cloud', 60, 'A+', false, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES ENTREPÔTS =====

INSERT INTO warehouses (id, warehouse_code, name, address, city, postal_code, country, manager_id, capacity, current_utilization, warehouse_type, status, created_date, modified_date) VALUES
('wh-001', 'WH001', 'Entrepôt Central Paris', '500 Zone Industrielle Nord', 'Roissy', '95700', 'France', 'emp-001', 10000.00, 65.5, 'main', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('wh-002', 'WH002', 'Dépôt Lyon', '250 Zone Logistique Sud', 'Lyon', '69007', 'France', 'emp-002', 5000.00, 45.2, 'regional', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('wh-003', 'WH003', 'Centre Distribution Marseille', '150 Port de Commerce', 'Marseille', '13016', 'France', 'emp-003', 3000.00, 78.9, 'distribution', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES PRODUITS =====

INSERT INTO products (id, product_code, name, description, category, brand, unit_of_measure, weight, dimensions, cost_price, selling_price, min_stock_level, max_stock_level, reorder_point, supplier_id, barcode, status, created_date, modified_date) VALUES
('prod-001', 'PROD001', 'Serveur Dell PowerEdge', 'Serveur haute performance pour datacenter', 'Serveurs', 'Dell', 'Unité', 25.5, '43x68x8 cm', 2500.00, 3500.00, 5, 50, 10, 'sup-001', '1234567890123', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('prod-002', 'PROD002', 'Licence Office 365', 'Suite bureautique Microsoft Office 365 Business', 'Logiciels', 'Microsoft', 'Licence', 0.0, 'Numérique', 120.00, 180.00, 100, 1000, 200, 'sup-002', '2345678901234', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('prod-003', 'PROD003', 'Switch Cisco 24 ports', 'Commutateur réseau 24 ports Gigabit', 'Réseau', 'Cisco', 'Unité', 2.8, '44x25x4 cm', 450.00, 650.00, 10, 100, 20, 'sup-001', '3456789012345', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('prod-004', 'PROD004', 'Service Cloud AWS', 'Crédit de service cloud computing AWS', 'Services', 'Amazon', 'Crédit', 0.0, 'Numérique', 800.00, 1200.00, 50, 500, 100, 'sup-003', '4567890123456', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES INVENTAIRE =====

INSERT INTO inventory (id, product_id, warehouse_id, quantity_on_hand, quantity_reserved, last_count_date, last_movement_date, location, batch_number, expiry_date, created_date, modified_date) VALUES
('inv-001', 'prod-001', 'wh-001', 25, 5, '2024-06-01', CURRENT_TIMESTAMP, 'A-01-15', 'BATCH001', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('inv-002', 'prod-002', 'wh-001', 500, 50, '2024-06-01', CURRENT_TIMESTAMP, 'B-02-10', 'LIC2024', '2025-12-31', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('inv-003', 'prod-003', 'wh-002', 45, 10, '2024-06-01', CURRENT_TIMESTAMP, 'C-01-05', 'NET001', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('inv-004', 'prod-004', 'wh-001', 200, 25, '2024-06-01', CURRENT_TIMESTAMP, 'D-03-20', 'CLOUD24', '2024-12-31', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===== DONNÉES MOUVEMENTS DE STOCK =====

INSERT INTO stock_movements (id, product_id, warehouse_id, movement_type, quantity, reference_type, reference_id, reason, cost_per_unit, total_cost, performed_by, movement_date, notes) VALUES
('mov-001', 'prod-001', 'wh-001', 'in', 30, 'purchase_order', 'po-001', 'Réception commande fournisseur', 2500.00, 75000.00, 'emp-001', CURRENT_TIMESTAMP, 'Livraison conforme'),
('mov-002', 'prod-002', 'wh-001', 'in', 600, 'purchase_order', 'po-002', 'Achat licences Office 365', 120.00, 72000.00, 'emp-002', CURRENT_TIMESTAMP, 'Licences activées'),
('mov-003', 'prod-001', 'wh-001', 'out', 5, 'order', 'ord-001', 'Expédition commande client', 2500.00, 12500.00, 'emp-001', CURRENT_TIMESTAMP, 'Livraison TechCorp'),
('mov-004', 'prod-003', 'wh-002', 'in', 50, 'purchase_order', 'po-003', 'Réception switches réseau', 450.00, 22500.00, 'emp-002', CURRENT_TIMESTAMP, 'Matériel testé OK');

-- Message de confirmation
SELECT 'Données Sales & Stocks insérées avec succès !' as message,
       (SELECT COUNT(*) FROM customers) as customers_count,
       (SELECT COUNT(*) FROM opportunities) as opportunities_count,
       (SELECT COUNT(*) FROM orders) as orders_count,
       (SELECT COUNT(*) FROM quotes) as quotes_count,
       (SELECT COUNT(*) FROM suppliers) as suppliers_count,
       (SELECT COUNT(*) FROM products) as products_count,
       (SELECT COUNT(*) FROM inventory) as inventory_count,
       (SELECT COUNT(*) FROM stock_movements) as movements_count;
