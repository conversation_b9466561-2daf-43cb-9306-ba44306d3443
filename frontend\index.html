<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP HUB - Accueil</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
            display: flex;
        }

        /* Navigation verticale */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: left;
            gap: 1rem;
            margin-bottom: 0rem;
        }

        .sidebar-logo h1 {
            font-size: 1.5rem;
            font-weight: 800;
            margin: 0;
        }

        .sidebar-subtitle {
            font-size: 0.875rem;
            opacity: 0.8;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 4px solid transparent;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: #3b82f6;
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.2);
            border-left-color: #3b82f6;
        }

        .nav-item-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-icon {
            font-size: 1.25rem;
            width: 24px;
            text-align: center;
        }

        .nav-text {
            font-weight: 500;
        }

        .nav-description {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 0.25rem;
        }

        /* Contenu principal */
        .main-wrapper {
            flex: 1;
            margin-left: 280px;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 1rem;
        }
        
        .hero {
            text-align: center;
            color: white;
            margin-bottom: 0.1rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0;
            text-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        

        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 3rem;
            width: 100%;
            padding: 0 1rem;
        }
        
        .module-card {
            background: white;
            padding: 0.5rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
        }
        
        .module-card.dashboard::before { --accent-color: #3b82f6; }
        .module-card.hr::before { --accent-color: #10b981; }
        .module-card.sales::before { --accent-color: #f59e0b; }
        .module-card.purchase::before { --accent-color: #8b5cf6; }
        .module-card.stock::before { --accent-color: #06b6d4; }
        .module-card.logistics::before { --accent-color: #ef4444; }
        .module-card.finance::before { --accent-color: #ec4899; }
        .module-card.crm::before { --accent-color: #7c3aed; }
        .module-card.bi::before { --accent-color: #f97316; }
        
        .module-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .module-icon.dashboard { color: #3b82f6; }
        .module-icon.hr { color: #10b981; }
        .module-icon.sales { color: #f59e0b; }
        .module-icon.purchase { color: #8b5cf6; }
        .module-icon.stock { color: #06b6d4; }
        .module-icon.logistics { color: #ef4444; }
        .module-icon.finance { color: #ec4899; }
        .module-icon.crm { color: #7c3aed; }
        .module-icon.bi { color: #f97316; }
        
        .module-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .module-description {
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem 2rem;
            border-radius: 1rem;
            text-align: center;
            color: white;
            margin-top: 2rem;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0 1rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        
        /* Menu mobile */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #1e293b;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        /* Responsive Design - Tablettes */
        @media (max-width: 1200px) {
            .modules-grid {
                grid-template-columns: repeat(2, 1fr);
                padding: 0 3rem;
            }
        }

        /* Responsive Design - Mobile */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar-overlay.open {
                display: block;
            }

            .main-wrapper {
                margin-left: 0;
            }

            .hero h1 {
                font-size: 2.5rem;
            }



            .modules-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
                gap: 1rem;
            }

            .container {
                padding: 1rem;
            }

            .modules-grid {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton menu mobile -->
    <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
        <span class="material-icons">menu</span>
    </button>

    <!-- Overlay pour mobile -->
    <div class="sidebar-overlay" onclick="closeMobileMenu()"></div>

    <!-- Navigation verticale -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <span style="font-size: 2rem;">🏢</span>
                <div>
                    <h1>ERP HUB</h1>
                    <p class="sidebar-subtitle">Système de Gestion Intégré</p>
                </div>
            </div>
        </div>

        <div class="sidebar-nav">
            <a href="dashboard-global-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">dashboard</span>
                    <div>
                        <div class="nav-text">Dashboard</div>
                        <div class="nav-description">Vue d'ensemble globale</div>
                    </div>
                </div>
            </a>

            <a href="finance-management.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">account_balance</span>
                    <div>
                        <div class="nav-text">Finance</div>
                        <div class="nav-description">Budget & Comptabilité</div>
                    </div>
                </div>
            </a>

            <a href="hr-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">people</span>
                    <div>
                        <div class="nav-text">Ressources Humaines</div>
                        <div class="nav-description">Employés & Paie</div>
                    </div>
                </div>
            </a>

            <a href="crm-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">contacts</span>
                    <div>
                        <div class="nav-text">CRM</div>
                        <div class="nav-description">Relation Client</div>
                    </div>
                </div>
            </a>

            <a href="sales-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">trending_up</span>
                    <div>
                        <div class="nav-text">Ventes</div>
                        <div class="nav-description">Opportunités & Commandes</div>
                    </div>
                </div>
            </a>

            <a href="purchase-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">shopping_cart</span>
                    <div>
                        <div class="nav-text">Achats</div>
                        <div class="nav-description">Fournisseurs & Commandes</div>
                    </div>
                </div>
            </a>

            <a href="stock-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">inventory_2</span>
                    <div>
                        <div class="nav-text">Stocks</div>
                        <div class="nav-description">Inventaire & Produits</div>
                    </div>
                </div>
            </a>

            <a href="logistics-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">local_shipping</span>
                    <div>
                        <div class="nav-text">Logistique</div>
                        <div class="nav-description">Expéditions & Transport</div>
                    </div>
                </div>
            </a>

            <a href="bi-management-postgresql.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">analytics</span>
                    <div>
                        <div class="nav-text">Business Intelligence</div>
                        <div class="nav-description">Rapports & Analyses</div>
                    </div>
                </div>
            </a>

            <a href="ai-assistant.html" class="nav-item">
                <div class="nav-item-content">
                    <span class="nav-icon material-icons">smart_toy</span>
                    <div>
                        <div class="nav-text">Assistant IA</div>
                        <div class="nav-description">Intelligence Artificielle</div>
                    </div>
                </div>
            </a>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="main-wrapper">
        <div class="container">
            <div class="hero">
                <h1>🏢 ERP HUB</h1>
            </div>
        
        <div class="modules-grid">
            <a href="dashboard-global-postgresql.html" class="module-card dashboard">
                <span class="module-icon dashboard material-icons">dashboard</span>
                <h3 class="module-title">Dashboard Global</h3>
                <p class="module-description">Vue d'ensemble consolidée de tous les modules avec indicateurs clés et graphiques en temps réel</p>
            </a>
            
            <a href="hr-management-postgresql.html" class="module-card hr">
                <span class="module-icon hr material-icons">people</span>
                <h3 class="module-title">Ressources Humaines</h3>
                <p class="module-description">Gestion des employés, paie, congés, évaluations et formation du personnel</p>
            </a>
            
            <a href="sales-management-postgresql.html" class="module-card sales">
                <span class="module-icon sales material-icons">trending_up</span>
                <h3 class="module-title">Gestion des Ventes</h3>
                <p class="module-description">Suivi des opportunités, devis, commandes et performance commerciale</p>
            </a>
            
            <a href="purchase-management-postgresql.html" class="module-card purchase">
                <span class="module-icon purchase material-icons">shopping_cart</span>
                <h3 class="module-title">Gestion des Achats</h3>
                <p class="module-description">Gestion des fournisseurs, commandes d'achat et approvisionnement</p>
            </a>
            
            <a href="stock-management-postgresql.html" class="module-card stock">
                <span class="module-icon stock material-icons">inventory_2</span>
                <h3 class="module-title">Gestion des Stocks</h3>
                <p class="module-description">Inventaire, mouvements de stock, alertes et optimisation des niveaux</p>
            </a>
            
            <a href="logistics-management-postgresql.html" class="module-card logistics">
                <span class="module-icon logistics material-icons">local_shipping</span>
                <h3 class="module-title">Logistique</h3>
                <p class="module-description">Gestion des expéditions, transport et suivi des livraisons</p>
            </a>
            
            <a href="finance-management.html" class="module-card finance">
                <span class="module-icon finance material-icons">account_balance</span>
                <h3 class="module-title">Finance & Budget</h3>
                <p class="module-description">Gestion budgétaire, comptabilité et analyse financière</p>
            </a>
            
            <a href="crm-management-postgresql.html" class="module-card crm">
                <span class="module-icon crm material-icons">contacts</span>
                <h3 class="module-title">CRM</h3>
                <p class="module-description">Gestion de la relation client, prospects et suivi commercial</p>
            </a>
            
            <a href="bi-management-postgresql.html" class="module-card bi">
                <span class="module-icon bi material-icons">analytics</span>
                <h3 class="module-title">Business Intelligence</h3>
                <p class="module-description">Tableaux de bord, rapports et analyse de données avancée</p>
            </a>
            </div>

            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>Backend API: Connecté</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>Base de données: PostgreSQL</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>Statut: Opérationnel</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gestion du menu mobile
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('open');
        }

        function closeMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('open');
        }

        // Vérification de la connexion API
        async function checkApiStatus() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                console.log('API Status:', data);
            } catch (error) {
                console.error('Erreur de connexion API:', error);
            }
        }

        // Marquer l'élément actuel dans la navigation
        function setActiveNavItem() {
            const currentPage = window.location.pathname.split('/').pop();
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkApiStatus();
            setActiveNavItem();
        });
    </script>
</body>
</html>
