#!/bin/bash

echo "🔍 VÉRIFICATION DE L'ÉTAT DES SERVICES"
echo "====================================="

echo "📊 Services Docker:"
docker-compose -f docker-compose.local.yml ps

echo ""
echo "🔍 Tests de connectivité rapides:"

# Test PostgreSQL
if docker exec erp_postgres_local pg_isready -U erp_user_local > /dev/null 2>&1; then
    echo "✅ PostgreSQL: Opérationnel"
else
    echo "❌ PostgreSQL: Non accessible"
fi

# Test Redis
if docker exec erp_redis_local redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: Opérationnel"
else
    echo "❌ Redis: Non accessible"
fi

# Test Backend (si disponible)
if curl -s http://localhost:8000 > /dev/null 2>&1; then
    echo "✅ Backend: Opérationnel"
else
    echo "⏳ Backend: En cours de démarrage..."
fi

# Test Frontend (si disponible)
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend: Opérationnel"
else
    echo "⏳ Frontend: En cours de démarrage..."
fi

echo ""
echo "🌐 URLs d'accès (une fois prêt):"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   Admin:    http://localhost:8000/admin"
