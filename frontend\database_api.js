// 🌐 CONNECTEUR API BASE DE DONNÉES ERP HUB
// Remplace localStorage par vraie base de données

class ERPDatabaseAPI {
    constructor(baseUrl = 'http://localhost:5000/api') {
        this.baseUrl = baseUrl;
        this.isOnline = false;
        this.checkConnection();
    }

    // ===== GESTION CONNEXION =====

    async checkConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/health`);
            this.isOnline = response.ok;
            console.log(this.isOnline ? '✅ API connectée' : '❌ API déconnectée');
            return this.isOnline;
        } catch (error) {
            this.isOnline = false;
            console.log('❌ API non disponible, mode localStorage');
            return false;
        }
    }

    // ===== OPÉRATIONS CRUD BUDGETS =====

    async createBudget(budgetData) {
        if (!this.isOnline) {
            return this.fallbackToLocalStorage('create', budgetData);
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(budgetData)
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Budget créé en base de données');
                this.showDatabaseIndicator('success', 'Créé en base de données');
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur création budget:', error);
            this.showDatabaseIndicator('error', 'Erreur création');
            return this.fallbackToLocalStorage('create', budgetData);
        }
    }

    async readBudgets() {
        if (!this.isOnline) {
            return this.fallbackToLocalStorage('read');
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets`);
            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ ${result.count} budgets chargés depuis la base`);
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur lecture budgets:', error);
            return this.fallbackToLocalStorage('read');
        }
    }

    async updateBudget(budgetId, budgetData) {
        if (!this.isOnline) {
            return this.fallbackToLocalStorage('update', budgetData, budgetId);
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets/${budgetId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(budgetData)
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Budget mis à jour en base de données');
                this.showDatabaseIndicator('success', 'Mis à jour en base');
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur mise à jour budget:', error);
            this.showDatabaseIndicator('error', 'Erreur mise à jour');
            return this.fallbackToLocalStorage('update', budgetData, budgetId);
        }
    }

    async deleteBudget(budgetId) {
        if (!this.isOnline) {
            return this.fallbackToLocalStorage('delete', null, budgetId);
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets/${budgetId}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Budget supprimé de la base de données');
                this.showDatabaseIndicator('success', 'Supprimé de la base');
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur suppression budget:', error);
            this.showDatabaseIndicator('error', 'Erreur suppression');
            return this.fallbackToLocalStorage('delete', null, budgetId);
        }
    }

    // ===== IMPORT/EXPORT =====

    async importBudgets(budgetsArray) {
        if (!this.isOnline) {
            // Import en localStorage
            budgetsArray.forEach(budget => {
                this.fallbackToLocalStorage('create', budget);
            });
            return { success: true, imported: budgetsArray.length };
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets/import`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ budgets: budgetsArray })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ ${result.imported} budgets importés en base`);
                this.showDatabaseIndicator('success', `${result.imported} importés`);
                return result;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur import budgets:', error);
            this.showDatabaseIndicator('error', 'Erreur import');
            return { success: false, error: error.message };
        }
    }

    async exportBudgets() {
        if (!this.isOnline) {
            return this.fallbackToLocalStorage('read');
        }

        try {
            const response = await fetch(`${this.baseUrl}/budgets/export`);
            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ ${result.count} budgets exportés`);
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur export budgets:', error);
            return this.fallbackToLocalStorage('read');
        }
    }

    // ===== HISTORIQUE =====

    async saveActionHistory(action, data, userId = 'default') {
        if (!this.isOnline) {
            return true; // Pas de sauvegarde historique en mode offline
        }

        try {
            const response = await fetch(`${this.baseUrl}/history`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action,
                    data: data,
                    user_id: userId
                })
            });

            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('❌ Erreur sauvegarde historique:', error);
            return false;
        }
    }

    // ===== FALLBACK LOCALSTORAGE =====

    fallbackToLocalStorage(operation, data = null, id = null) {
        console.log(`🔄 Fallback localStorage pour: ${operation}`);
        
        try {
            const existingData = JSON.parse(localStorage.getItem('erp_budget_data') || '[]');
            
            switch (operation) {
                case 'create':
                    existingData.push(data);
                    localStorage.setItem('erp_budget_data', JSON.stringify(existingData));
                    this.showDatabaseIndicator('warning', 'Sauvé localement');
                    return true;
                
                case 'read':
                    return existingData;
                
                case 'update':
                    const updateIndex = existingData.findIndex(b => b.id === id);
                    if (updateIndex !== -1) {
                        existingData[updateIndex] = data;
                        localStorage.setItem('erp_budget_data', JSON.stringify(existingData));
                        this.showDatabaseIndicator('warning', 'Mis à jour localement');
                        return true;
                    }
                    return false;
                
                case 'delete':
                    const filteredData = existingData.filter(b => b.id !== id);
                    localStorage.setItem('erp_budget_data', JSON.stringify(filteredData));
                    this.showDatabaseIndicator('warning', 'Supprimé localement');
                    return true;
                
                default:
                    return false;
            }
        } catch (error) {
            console.error('❌ Erreur localStorage:', error);
            return false;
        }
    }

    // ===== INDICATEURS VISUELS =====

    showDatabaseIndicator(status, message) {
        const indicator = document.querySelector('.save-indicator');
        if (!indicator) return;

        indicator.classList.remove('saved', 'saving', 'error', 'warning');
        
        switch (status) {
            case 'success':
                indicator.classList.add('saved');
                indicator.innerHTML = `<span class="material-icons">cloud_done</span> ${message}`;
                break;
            case 'error':
                indicator.classList.add('error');
                indicator.innerHTML = `<span class="material-icons">cloud_off</span> ${message}`;
                break;
            case 'warning':
                indicator.classList.add('warning');
                indicator.innerHTML = `<span class="material-icons">cloud_queue</span> ${message}`;
                break;
        }

        // Revenir à l'état normal après 3 secondes
        setTimeout(() => {
            indicator.classList.remove('saved', 'error', 'warning');
            const statusText = this.isOnline ? 'Base de données connectée' : 'Mode hors ligne';
            const icon = this.isOnline ? 'cloud_done' : 'cloud_off';
            indicator.innerHTML = `<span class="material-icons">${icon}</span> ${statusText}`;
        }, 3000);
    }

    // ===== SYNCHRONISATION =====

    async syncLocalToDatabase() {
        if (!this.isOnline) {
            console.log('❌ Impossible de synchroniser : API déconnectée');
            return false;
        }

        try {
            const localData = JSON.parse(localStorage.getItem('erp_budget_data') || '[]');
            
            if (localData.length === 0) {
                console.log('ℹ️ Aucune donnée locale à synchroniser');
                return true;
            }

            console.log(`🔄 Synchronisation de ${localData.length} budgets...`);
            
            const result = await this.importBudgets(localData);
            
            if (result.success) {
                // Vider le localStorage après synchronisation réussie
                localStorage.removeItem('erp_budget_data');
                console.log('✅ Synchronisation terminée, localStorage vidé');
                return true;
            } else {
                console.log('❌ Échec de la synchronisation');
                return false;
            }
        } catch (error) {
            console.error('❌ Erreur synchronisation:', error);
            return false;
        }
    }
}

// ===== INSTANCE GLOBALE =====

// Créer une instance globale de l'API
window.erpAPI = new ERPDatabaseAPI();

// Vérifier la connexion toutes les 30 secondes
setInterval(() => {
    window.erpAPI.checkConnection();
}, 30000);

console.log('🌐 API ERP HUB initialisée');
console.log('📋 Utilisation:');
console.log('   erpAPI.createBudget(data)');
console.log('   erpAPI.readBudgets()');
console.log('   erpAPI.updateBudget(id, data)');
console.log('   erpAPI.deleteBudget(id)');
console.log('   erpAPI.syncLocalToDatabase()');
