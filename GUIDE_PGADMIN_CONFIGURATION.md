# 📊 Guide de Configuration pgAdmin pour ERP HUB

## 🎯 Vue d'ensemble

Votre base de données PostgreSQL ERP HUB contient maintenant des données réelles que vous pouvez consulter et modifier via pgAdmin, une interface graphique professionnelle pour PostgreSQL.

## 📈 Données actuelles dans votre base :
- ✅ **3 budgets** (Marketing, RH, Ventes)
- ✅ **3 employés** (<PERSON>, <PERSON>, <PERSON>)
- ✅ **3 contacts** (TechCorp, InnoSoft, DataSys)
- ✅ **3 comptes bancaires** (BNP, Crédit Agricole, Société Générale)

## 🔧 Installation et Configuration pgAdmin

### Étape 1 : Téléchargement
1. Allez sur : https://www.pgadmin.org/download/
2. Choisissez votre version Windows
3. Téléchargez et installez pgAdmin 4

### Étape 2 : Configuration de la connexion
1. **Ouvrez pgAdmin**
2. **Clic droit sur "Servers"** dans le panneau de gauche
3. **Sélectionnez "Register" → "Server"**

### Étape 3 : Paramètres de connexion

**Onglet "General" :**
- **Name** : `ERP HUB PostgreSQL`
- **Server group** : `Servers` (par défaut)
- **Comments** : `Base de données ERP HUB - Système de gestion d'entreprise`

**Onglet "Connection" :**
- **Host name/address** : `localhost`
- **Port** : `5432`
- **Maintenance database** : `erp_hub`
- **Username** : `erp_admin`
- **Password** : `erp_secure_2024`
- **Save password** : ✅ (cochez pour éviter de retaper)

**Onglet "Advanced" (optionnel) :**
- **DB restriction** : `erp_hub` (pour ne voir que votre base)

### Étape 4 : Test de connexion
1. Cliquez sur **"Save"**
2. Si la connexion réussit, vous verrez votre serveur dans la liste
3. Développez : `ERP HUB PostgreSQL` → `Databases` → `erp_hub` → `Schemas` → `public` → `Tables`

## 📋 Navigation dans vos données

### Tables disponibles :
1. **budgets** - Gestion financière et budgétaire
2. **employees** - Ressources humaines
3. **contacts** - CRM et relations clients
4. **accounts** - Comptes bancaires
5. **movements** - Mouvements de trésorerie
6. **leaves** - Gestion des congés
7. **kpis** - Indicateurs de performance
8. **action_history** - Historique des actions

### Consulter les données :
1. **Clic droit sur une table** → `View/Edit Data` → `All Rows`
2. Les données s'affichent dans un tableau
3. **Double-clic sur une cellule** pour la modifier
4. **F6** pour sauvegarder les modifications

## ✏️ Modification des données

### Modifier un budget existant :
1. Allez dans la table `budgets`
2. Trouvez le budget à modifier (ex: `test_001`)
3. Double-cliquez sur la cellule à modifier
4. Changez la valeur (ex: forecast de 50000 à 60000)
5. Appuyez sur **Entrée** puis **F6** pour sauvegarder

### Ajouter un nouvel employé :
1. Allez dans la table `employees`
2. Clic droit → `View/Edit Data` → `All Rows`
3. Cliquez sur la dernière ligne vide
4. Remplissez les champs :
   - `id` : `emp_004`
   - `first_name` : `Votre prénom`
   - `last_name` : `Votre nom`
   - `email` : `<EMAIL>`
   - `position` : `Votre poste`
   - `department` : `Votre département`
   - `salary` : `Votre salaire`
5. **F6** pour sauvegarder

### Supprimer une ligne :
1. Sélectionnez la ligne entière (clic sur le numéro de ligne)
2. **Clic droit** → `Delete Row`
3. **F6** pour confirmer la suppression

## 🔍 Requêtes SQL personnalisées

### Ouvrir l'éditeur SQL :
1. **Clic droit sur votre base `erp_hub`**
2. **Sélectionnez "Query Tool"**
3. Tapez vos requêtes SQL

### Exemples de requêtes utiles :

**Voir tous les budgets avec calcul du pourcentage réalisé :**
```sql
SELECT 
    id,
    category_name,
    forecast,
    realized,
    ROUND((realized / forecast * 100), 2) as pourcentage_realise
FROM budgets 
ORDER BY pourcentage_realise DESC;
```

**Employés par département :**
```sql
SELECT 
    department,
    COUNT(*) as nombre_employes,
    AVG(salary) as salaire_moyen
FROM employees 
GROUP BY department;
```

**Contacts par type :**
```sql
SELECT 
    contact_type,
    COUNT(*) as nombre,
    STRING_AGG(company_name, ', ') as entreprises
FROM contacts 
GROUP BY contact_type;
```

## 🚀 Conseils d'utilisation

### Bonnes pratiques :
- ✅ **Toujours sauvegarder** avec F6 après modification
- ✅ **Tester sur une copie** avant modification importante
- ✅ **Utiliser des transactions** pour les modifications multiples
- ✅ **Faire des sauvegardes régulières** de votre base

### Raccourcis utiles :
- **F5** : Actualiser les données
- **F6** : Sauvegarder les modifications
- **Ctrl+A** : Sélectionner tout
- **Ctrl+C/V** : Copier/Coller
- **Ctrl+Z** : Annuler (dans l'éditeur SQL)

## 🔄 Synchronisation avec l'application web

Vos modifications dans pgAdmin sont **immédiatement visibles** dans :
- L'application web ERP HUB (http://localhost:5173)
- L'API REST (http://localhost:5000)
- Tous les modules de l'ERP

## 🆘 Dépannage

### Problème de connexion :
1. Vérifiez que PostgreSQL fonctionne : `docker ps`
2. Testez la connexion : `docker exec -it erp_postgres psql -U erp_admin -d erp_hub`
3. Redémarrez le container si nécessaire

### Données non visibles :
1. Actualisez avec F5
2. Vérifiez que vous êtes dans la bonne base `erp_hub`
3. Vérifiez les filtres de vue

### Erreur de permission :
- Utilisez toujours l'utilisateur `erp_admin`
- Vérifiez le mot de passe `erp_secure_2024`

## 📞 Support

Pour toute question ou problème :
1. Consultez les logs du backend
2. Vérifiez l'état de PostgreSQL
3. Testez l'API REST directement

Votre ERP HUB est maintenant entièrement opérationnel avec une interface de gestion de données professionnelle ! 🎉
