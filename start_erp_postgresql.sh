#!/bin/bash
# 🚀 SCRIPT DE DÉMARRAGE ERP HUB POSTGRESQL
# Lancement automatique de tous les services

echo "🚀 Démarrage ERP HUB avec PostgreSQL..."
echo "================================================"

# Vérifier que Docker est installé
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier que les fichiers nécessaires existent
if [ ! -f "docker-compose-postgresql.yml" ]; then
    echo "❌ Fichier docker-compose-postgresql.yml non trouvé."
    exit 1
fi

# Créer les dossiers nécessaires
echo "📁 Création des dossiers..."
mkdir -p logs
mkdir -p backups
mkdir -p ssl
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/datasources

# Arrêter les services existants
echo "🛑 Arrêt des services existants..."
docker-compose -f docker-compose-postgresql.yml down

# Nettoyer les containers orphelins
echo "🧹 Nettoyage des containers orphelins..."
docker system prune -f

# Construire les images
echo "🔨 Construction des images Docker..."
docker-compose -f docker-compose-postgresql.yml build

# Démarrer les services
echo "🚀 Démarrage des services..."
docker-compose -f docker-compose-postgresql.yml up -d

# Attendre que PostgreSQL soit prêt
echo "⏳ Attente de PostgreSQL..."
sleep 10

# Vérifier l'état des services
echo "📊 Vérification de l'état des services..."
docker-compose -f docker-compose-postgresql.yml ps

# Initialiser la base de données si nécessaire
echo "🗄️ Initialisation de la base de données..."
if command -v python3 &> /dev/null; then
    python3 backend/postgresql_setup.py
elif command -v python &> /dev/null; then
    python backend/postgresql_setup.py
else
    echo "⚠️ Python non trouvé. Initialisation manuelle nécessaire."
fi

# Afficher les informations de connexion
echo ""
echo "✅ ERP HUB PostgreSQL démarré avec succès !"
echo "================================================"
echo "🌐 Frontend : http://localhost"
echo "🔌 API : http://localhost:5000"
echo "🐘 PostgreSQL : localhost:5432"
echo "📊 Grafana : http://localhost:3000"
echo "🔍 Prometheus : http://localhost:9090"
echo "💾 Redis : localhost:6379"
echo ""
echo "🔒 Comptes par défaut :"
echo "   Admin : admin / Admin123!"
echo "   Grafana : admin / admin_grafana_2024"
echo ""
echo "📋 Commandes utiles :"
echo "   Arrêter : docker-compose -f docker-compose-postgresql.yml down"
echo "   Logs : docker-compose -f docker-compose-postgresql.yml logs -f"
echo "   Redémarrer : docker-compose -f docker-compose-postgresql.yml restart"
echo ""
echo "🎉 Votre ERP HUB est prêt à l'emploi !"
