# 🐘 CONFIGURATION POSTGRESQL POUR ERP HUB
# Base de données professionnelle avec authentification et sécurité

import psycopg2
from psycopg2.extras import RealDictCursor
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
import hashlib
import secrets
import bcrypt

class ERPPostgreSQLDatabase:
    """Gestionnaire PostgreSQL pour ERP HUB avec authentification"""
    
    def __init__(self, config=None):
        self.config = config or {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', '5432'),
            'database': os.getenv('DB_NAME', 'erp_hub'),
            'user': os.getenv('DB_USER', 'erp_admin'),
            'password': os.getenv('DB_PASSWORD', 'erp_secure_2024')
        }
        self.init_database()
    
    def get_connection(self):
        """Obtenir une connexion à la base PostgreSQL"""
        try:
            conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password'],
                cursor_factory=RealDictCursor
            )
            return conn
        except psycopg2.Error as e:
            print(f"❌ Erreur connexion PostgreSQL : {e}")
            return None
    
    def init_database(self):
        """Initialiser la base PostgreSQL avec toutes les tables"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            cursor = conn.cursor()
            
            # Table des utilisateurs avec authentification
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    first_name VARCHAR(50),
                    last_name VARCHAR(50),
                    role VARCHAR(20) DEFAULT 'user',
                    department VARCHAR(50),
                    is_active BOOLEAN DEFAULT true,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des sessions utilisateur
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                    token VARCHAR(255) UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    ip_address INET,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des budgets avec contraintes et index
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS budgets (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    category_name VARCHAR(100) NOT NULL,
                    category_type VARCHAR(20) NOT NULL CHECK (category_type IN ('revenue', 'expense', 'investment', 'financial', 'exceptional')),
                    cost_center VARCHAR(50),
                    cost_center_name VARCHAR(100),
                    analytic_code VARCHAR(50),
                    analytic_code_name VARCHAR(100),
                    responsible VARCHAR(100),
                    department VARCHAR(50),
                    notes TEXT,
                    forecast DECIMAL(15,2) DEFAULT 0,
                    realized DECIMAL(15,2) DEFAULT 0,
                    monthly_data JSONB,
                    created_by UUID REFERENCES users(id),
                    modified_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    version INTEGER DEFAULT 1
                )
            ''')
            
            # Table des mouvements de trésorerie
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS movements (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    account_id UUID NOT NULL,
                    date DATE NOT NULL,
                    description TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    type VARCHAR(20) NOT NULL CHECK (type IN ('debit', 'credit')),
                    category VARCHAR(50),
                    reference VARCHAR(50),
                    created_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des comptes bancaires
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(100) NOT NULL,
                    bank VARCHAR(100) NOT NULL,
                    type VARCHAR(20) NOT NULL,
                    balance DECIMAL(15,2) DEFAULT 0,
                    alert_threshold DECIMAL(15,2) DEFAULT 0,
                    iban VARCHAR(34),
                    currency VARCHAR(3) DEFAULT 'EUR',
                    is_active BOOLEAN DEFAULT true,
                    created_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table de l'historique des actions avec audit
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS action_history (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID REFERENCES users(id),
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id UUID,
                    old_data JSONB,
                    new_data JSONB,
                    ip_address INET,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des permissions
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS permissions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                    module VARCHAR(50) NOT NULL,
                    action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'read', 'update', 'delete', 'export', 'import')),
                    granted BOOLEAN DEFAULT true,
                    granted_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Index pour optimiser les performances
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_category_type ON budgets(category_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_cost_center ON budgets(cost_center)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_created_at ON budgets(created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_date ON movements(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_account ON movements(account_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_user ON action_history(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_created ON action_history(created_at)')
            
            # Trigger pour mise à jour automatique du timestamp
            cursor.execute('''
                CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql'
            ''')
            
            # Appliquer le trigger aux tables nécessaires
            for table in ['budgets', 'movements', 'accounts', 'users']:
                cursor.execute(f'''
                    DROP TRIGGER IF EXISTS update_{table}_updated_at ON {table};
                    CREATE TRIGGER update_{table}_updated_at
                        BEFORE UPDATE ON {table}
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column()
                ''')
            
            conn.commit()
            print("✅ Base PostgreSQL initialisée avec succès")
            
            # Créer un utilisateur admin par défaut
            self.create_default_admin()
            
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Erreur initialisation base : {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()
    
    def create_default_admin(self):
        """Créer un utilisateur administrateur par défaut"""
        try:
            admin_data = {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'Admin123!',
                'first_name': 'Administrateur',
                'last_name': 'ERP HUB',
                'role': 'admin',
                'department': 'IT'
            }
            
            if not self.get_user_by_username('admin'):
                user_id = self.create_user(admin_data)
                if user_id:
                    print("✅ Utilisateur admin créé : admin / Admin123!")
                    # Donner toutes les permissions à l'admin
                    self.grant_all_permissions(user_id)
            else:
                print("ℹ️ Utilisateur admin existe déjà")
                
        except Exception as e:
            print(f"❌ Erreur création admin : {e}")
    
    # ===== GESTION UTILISATEURS =====
    
    def hash_password(self, password: str) -> str:
        """Hasher un mot de passe avec bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Vérifier un mot de passe"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def create_user(self, user_data: Dict) -> Optional[str]:
        """Créer un nouvel utilisateur"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            cursor = conn.cursor()
            
            hashed_password = self.hash_password(user_data['password'])
            
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, first_name, last_name, role, department)
                VALUES (%(username)s, %(email)s, %(password_hash)s, %(first_name)s, %(last_name)s, %(role)s, %(department)s)
                RETURNING id
            ''', {
                'username': user_data['username'],
                'email': user_data['email'],
                'password_hash': hashed_password,
                'first_name': user_data.get('first_name', ''),
                'last_name': user_data.get('last_name', ''),
                'role': user_data.get('role', 'user'),
                'department': user_data.get('department', '')
            })
            
            user_id = cursor.fetchone()['id']
            conn.commit()
            
            print(f"✅ Utilisateur créé : {user_data['username']}")
            return str(user_id)
            
        except psycopg2.Error as e:
            print(f"❌ Erreur création utilisateur : {e}")
            conn.rollback()
            return None
        finally:
            cursor.close()
            conn.close()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authentifier un utilisateur"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, email, password_hash, first_name, last_name, role, department, is_active
                FROM users 
                WHERE username = %s AND is_active = true
            ''', (username,))
            
            user = cursor.fetchone()
            
            if user and self.verify_password(password, user['password_hash']):
                # Mettre à jour la dernière connexion
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = %s
                ''', (user['id'],))
                conn.commit()
                
                # Retourner les infos utilisateur (sans le hash du mot de passe)
                user_info = dict(user)
                del user_info['password_hash']
                return user_info
            
            return None
            
        except psycopg2.Error as e:
            print(f"❌ Erreur authentification : {e}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """Récupérer un utilisateur par nom d'utilisateur"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, email, first_name, last_name, role, department, is_active, created_at
                FROM users 
                WHERE username = %s
            ''', (username,))
            
            user = cursor.fetchone()
            return dict(user) if user else None
            
        except psycopg2.Error as e:
            print(f"❌ Erreur récupération utilisateur : {e}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    def grant_all_permissions(self, user_id: str):
        """Donner toutes les permissions à un utilisateur"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            cursor = conn.cursor()
            
            modules = ['budgets', 'movements', 'accounts', 'users', 'reports']
            actions = ['create', 'read', 'update', 'delete', 'export', 'import']
            
            for module in modules:
                for action in actions:
                    cursor.execute('''
                        INSERT INTO permissions (user_id, module, action, granted)
                        VALUES (%s, %s, %s, true)
                        ON CONFLICT DO NOTHING
                    ''', (user_id, module, action))
            
            conn.commit()
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Erreur permissions : {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

# ===== EXEMPLE D'UTILISATION =====

if __name__ == "__main__":
    # Initialiser la base PostgreSQL
    db = ERPPostgreSQLDatabase()
    
    print("🐘 Base PostgreSQL ERP HUB initialisée")
    print("👤 Utilisateur admin créé : admin / Admin123!")
    print("🔒 Authentification et permissions activées")
    print("📊 Tables créées avec index et contraintes")
    print("🚀 Prêt pour la production !")
