# 🐳 DOCKER COMPOSE POSTGRESQL SIMPLE
# Configuration minimale pour démarrer rapidement

version: '3.8'

services:
  # Base de données PostgreSQL uniquement
  postgres:
    image: postgres:15-alpine
    container_name: erp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: erp_hub
      POSTGRES_USER: erp_admin
      POSTGRES_PASSWORD: erp_secure_2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_admin -d erp_hub"]
      interval: 30s
      timeout: 10s
      retries: 3

# Volumes persistants
volumes:
  postgres_data:
    driver: local
