# Configuration de test local pour ERP HUB
# Version sécurisée pour test local

# Django
SECRET_KEY=local-test-secret-key-for-development-only-2024
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
DJANGO_SETTINGS_MODULE=config.settings.production

# Base de données PostgreSQL
POSTGRES_DB=erp_hub_local
POSTGRES_USER=erp_user_local
POSTGRES_PASSWORD=local_test_password_123
DATABASE_URL=*****************************************************************/erp_hub_local

# Redis
REDIS_PASSWORD=local_redis_password_123
REDIS_URL=redis://:local_redis_password_123@redis:6379/0

# JWT
JWT_SECRET_KEY=local-jwt-secret-key-for-testing-2024
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://0.0.0.0:3000

# Email (SMTP) - Configuration locale
EMAIL_HOST=localhost
EMAIL_PORT=1025
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
EMAIL_USE_TLS=False
DEFAULT_FROM_EMAIL=ERP HUB Local <test@localhost>

# OpenAI (pour l'IA) - Optionnel pour test local
OPENAI_API_KEY=

# Frontend
VITE_API_URL=http://localhost:8000/api
VITE_APP_NAME=ERP HUB - Test Local
VITE_APP_VERSION=1.0.0-local
VITE_ENVIRONMENT=local-test

# Monitoring
GRAFANA_PASSWORD=your-grafana-admin-password

# Sentry (monitoring d'erreurs - optionnel)
SENTRY_DSN=your-sentry-dsn-url

# AWS S3 (stockage fichiers - optionnel)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=erp-hub-media
AWS_S3_REGION_NAME=eu-west-1

# Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=erp-hub-backups

# SSL (si certificats personnalisés)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Logs
LOG_LEVEL=INFO
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5
