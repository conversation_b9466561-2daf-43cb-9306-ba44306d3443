# 🔧 SCRIPT DE CORRECTION DES LIENS DASHBOARD
# Corrige tous les liens vers le dashboard dans les pages ERP

import os
import re
from pathlib import Path

def fix_dashboard_links():
    """Corrige tous les liens dashboard dans les pages HTML"""
    
    print("🔧 CORRECTION DES LIENS DASHBOARD")
    print("=" * 50)
    
    # Dossier frontend
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ Dossier frontend non trouvé")
        return
    
    # Fichiers à corriger
    html_files = [
        "hr-management-postgresql.html",
        "sales-management-postgresql.html",
        "purchase-management-postgresql.html",
        "stock-management-postgresql.html",
        "logistics-management-postgresql.html",
        "crm-management-postgresql.html",
        "bi-management-postgresql.html"
    ]
    
    # Patterns à corriger
    patterns_to_fix = [
        (r'href="dashboard-demo\.html"', 'href="dashboard-global-postgresql.html"'),
        (r'href="dashboard\.html"', 'href="dashboard-global-postgresql.html"'),
        (r'href="index\.html"', 'href="dashboard-global-postgresql.html"'),
    ]
    
    fixed_files = 0
    total_fixes = 0
    
    for html_file in html_files:
        file_path = frontend_dir / html_file
        
        if not file_path.exists():
            print(f"⚠️ Fichier non trouvé: {html_file}")
            continue
        
        print(f"🔍 Vérification: {html_file}")
        
        # Lire le contenu
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # Appliquer les corrections
            for pattern, replacement in patterns_to_fix:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
                    print(f"  ✅ Corrigé {len(matches)} occurrence(s) de {pattern}")
            
            # Sauvegarder si des modifications ont été faites
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                total_fixes += file_fixes
                print(f"  💾 Fichier sauvegardé avec {file_fixes} correction(s)")
            else:
                print(f"  ✅ Aucune correction nécessaire")
                
        except Exception as e:
            print(f"  ❌ Erreur lors du traitement: {e}")
    
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES CORRECTIONS")
    print("=" * 50)
    print(f"📄 Fichiers traités: {len(html_files)}")
    print(f"🔧 Fichiers corrigés: {fixed_files}")
    print(f"✅ Total corrections: {total_fixes}")
    
    if total_fixes > 0:
        print("\n🎉 Tous les liens dashboard ont été corrigés !")
        print("💡 Les boutons Dashboard pointent maintenant vers dashboard-global-postgresql.html")
    else:
        print("\n✅ Tous les liens étaient déjà corrects !")

def verify_dashboard_links():
    """Vérifie que tous les liens dashboard sont corrects"""
    
    print("\n🔍 VÉRIFICATION DES LIENS DASHBOARD")
    print("=" * 50)
    
    frontend_dir = Path("frontend")
    html_files = [
        "hr-management-postgresql.html",
        "sales-management-postgresql.html", 
        "purchase-management-postgresql.html",
        "stock-management-postgresql.html",
        "logistics-management-postgresql.html",
        "crm-management-postgresql.html",
        "bi-management-postgresql.html"
    ]
    
    correct_links = 0
    total_files = 0
    
    for html_file in html_files:
        file_path = frontend_dir / html_file
        
        if not file_path.exists():
            continue
            
        total_files += 1
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Vérifier le lien correct
            if 'href="dashboard-global-postgresql.html"' in content:
                print(f"✅ {html_file}: Lien correct")
                correct_links += 1
            else:
                print(f"❌ {html_file}: Lien incorrect ou manquant")
                
                # Chercher les liens incorrects
                incorrect_patterns = [
                    'href="dashboard-demo.html"',
                    'href="dashboard.html"',
                    'href="index.html"'
                ]
                
                for pattern in incorrect_patterns:
                    if pattern in content:
                        print(f"  🔍 Trouvé: {pattern}")
                        
        except Exception as e:
            print(f"❌ {html_file}: Erreur - {e}")
    
    print(f"\n📊 Résultat: {correct_links}/{total_files} fichiers avec liens corrects")
    
    return correct_links == total_files

if __name__ == "__main__":
    # Vérifier d'abord
    all_correct = verify_dashboard_links()
    
    if not all_correct:
        print("\n🔧 Correction des liens nécessaire...")
        fix_dashboard_links()
        
        # Vérifier à nouveau
        print("\n🔍 Vérification post-correction...")
        verify_dashboard_links()
    
    print("\n🎯 INSTRUCTIONS D'UTILISATION")
    print("=" * 50)
    print("1. Les boutons Dashboard dans toutes les pages pointent maintenant vers:")
    print("   dashboard-global-postgresql.html")
    print("2. Actualisez votre navigateur (F5) pour voir les changements")
    print("3. Testez la navigation entre les pages")
    
    print("\n🎉 Correction terminée !")
