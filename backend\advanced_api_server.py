# 🚀 API REST AVANCÉE ERP HUB AVEC POSTGRESQL
# Serveur Flask avec authentification JWT, permissions et sécurité

from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity, get_jwt
from postgresql_setup import ERPPostgreSQLDatabase
import json
import os
from datetime import datetime, timedelta
from functools import wraps
import secrets
import logging
from werkzeug.security import generate_password_hash

# Configuration de l'application
app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', secrets.token_hex(32))
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=8)

# Extensions
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:8080'])
jwt = JWTManager(app)

# Base de données
db = ERPPostgreSQLDatabase()

# Configuration des logs
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ===== DÉCORATEURS DE SÉCURITÉ =====

def require_permission(module, action):
    """Décorateur pour vérifier les permissions utilisateur"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            user_id = get_jwt_identity()
            
            # Vérifier si l'utilisateur a la permission
            if not db.check_user_permission(user_id, module, action):
                return jsonify({
                    'success': False,
                    'error': f'Permission refusée : {action} sur {module}'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_action(action, table_name):
    """Décorateur pour logger les actions utilisateur"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = get_jwt_identity() if hasattr(g, 'jwt_identity') else None
            ip_address = request.remote_addr
            user_agent = request.headers.get('User-Agent', '')
            
            # Exécuter la fonction
            result = f(*args, **kwargs)
            
            # Logger l'action si succès
            if isinstance(result, tuple) and result[1] in [200, 201]:
                db.log_user_action(user_id, action, table_name, None, None, ip_address, user_agent)
            
            return result
        return decorated_function
    return decorator

# ===== ENDPOINTS AUTHENTIFICATION =====

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Connexion utilisateur avec JWT"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': 'Nom d\'utilisateur et mot de passe requis'
            }), 400
        
        # Authentifier l'utilisateur
        user = db.authenticate_user(username, password)
        
        if user:
            # Créer le token JWT
            access_token = create_access_token(
                identity=str(user['id']),
                additional_claims={
                    'username': user['username'],
                    'role': user['role'],
                    'department': user['department']
                }
            )
            
            # Logger la connexion
            logger.info(f"Connexion réussie : {username} ({request.remote_addr})")
            
            return jsonify({
                'success': True,
                'message': 'Connexion réussie',
                'access_token': access_token,
                'user': {
                    'id': str(user['id']),
                    'username': user['username'],
                    'email': user['email'],
                    'first_name': user['first_name'],
                    'last_name': user['last_name'],
                    'role': user['role'],
                    'department': user['department']
                }
            }), 200
        else:
            logger.warning(f"Tentative de connexion échouée : {username} ({request.remote_addr})")
            return jsonify({
                'success': False,
                'error': 'Nom d\'utilisateur ou mot de passe incorrect'
            }), 401
            
    except Exception as e:
        logger.error(f"Erreur lors de la connexion : {e}")
        return jsonify({
            'success': False,
            'error': 'Erreur interne du serveur'
        }), 500

@app.route('/api/auth/register', methods=['POST'])
@jwt_required()
@require_permission('users', 'create')
def register():
    """Inscription d'un nouvel utilisateur (admin uniquement)"""
    try:
        data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['username', 'email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Créer l'utilisateur
        user_id = db.create_user(data)
        
        if user_id:
            logger.info(f"Nouvel utilisateur créé : {data['username']}")
            return jsonify({
                'success': True,
                'message': 'Utilisateur créé avec succès',
                'user_id': user_id
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la création de l\'utilisateur'
            }), 500
            
    except Exception as e:
        logger.error(f"Erreur lors de l'inscription : {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Récupérer le profil de l'utilisateur connecté"""
    try:
        user_id = get_jwt_identity()
        user = db.get_user_by_id(user_id)
        
        if user:
            return jsonify({
                'success': True,
                'user': user
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Utilisateur non trouvé'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS BUDGETS SÉCURISÉS =====

@app.route('/api/budgets', methods=['GET'])
@jwt_required()
@require_permission('budgets', 'read')
@log_action('read', 'budgets')
def get_budgets():
    """Récupérer tous les budgets avec filtres"""
    try:
        # Paramètres de filtrage
        cost_center = request.args.get('cost_center')
        analytic_code = request.args.get('analytic_code')
        category_type = request.args.get('category_type')
        department = request.args.get('department')
        
        budgets = db.read_budgets_filtered({
            'cost_center': cost_center,
            'analytic_code': analytic_code,
            'category_type': category_type,
            'department': department
        })
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
        
    except Exception as e:
        logger.error(f"Erreur récupération budgets : {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
@jwt_required()
@require_permission('budgets', 'create')
@log_action('create', 'budgets')
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        user_id = get_jwt_identity()
        
        # Ajouter l'utilisateur créateur
        budget_data['created_by'] = user_id
        budget_data['modified_by'] = user_id
        
        # Validation des champs obligatoires
        required_fields = ['category_name', 'category_type']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        budget_id = db.create_budget_advanced(budget_data)
        
        if budget_id:
            logger.info(f"Budget créé : {budget_id} par {user_id}")
            return jsonify({
                'success': True,
                'message': 'Budget créé avec succès',
                'budget_id': budget_id
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la création du budget'
            }), 500
            
    except Exception as e:
        logger.error(f"Erreur création budget : {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
@jwt_required()
@require_permission('budgets', 'update')
@log_action('update', 'budgets')
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        user_id = get_jwt_identity()
        
        # Ajouter l'utilisateur modificateur
        budget_data['modified_by'] = user_id
        
        success = db.update_budget_advanced(budget_id, budget_data)
        
        if success:
            logger.info(f"Budget mis à jour : {budget_id} par {user_id}")
            return jsonify({
                'success': True,
                'message': 'Budget mis à jour avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de mise à jour'
            }), 404
            
    except Exception as e:
        logger.error(f"Erreur mise à jour budget : {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
@jwt_required()
@require_permission('budgets', 'delete')
@log_action('delete', 'budgets')
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        user_id = get_jwt_identity()
        success = db.delete_budget_advanced(budget_id, user_id)
        
        if success:
            logger.info(f"Budget supprimé : {budget_id} par {user_id}")
            return jsonify({
                'success': True,
                'message': 'Budget supprimé avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de suppression'
            }), 404
            
    except Exception as e:
        logger.error(f"Erreur suppression budget : {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS RAPPORTS ET ANALYTICS =====

@app.route('/api/reports/budget-summary', methods=['GET'])
@jwt_required()
@require_permission('budgets', 'read')
def get_budget_summary():
    """Récupérer un résumé des budgets"""
    try:
        summary = db.get_budget_summary()
        return jsonify({
            'success': True,
            'data': summary
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reports/monthly-evolution', methods=['GET'])
@jwt_required()
@require_permission('budgets', 'read')
def get_monthly_evolution():
    """Récupérer l'évolution mensuelle des budgets"""
    try:
        year = request.args.get('year', datetime.now().year)
        evolution = db.get_monthly_evolution(year)
        
        return jsonify({
            'success': True,
            'data': evolution
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS ADMINISTRATION =====

@app.route('/api/admin/users', methods=['GET'])
@jwt_required()
@require_permission('users', 'read')
def get_users():
    """Récupérer la liste des utilisateurs (admin uniquement)"""
    try:
        users = db.get_all_users()
        return jsonify({
            'success': True,
            'data': users
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/stats', methods=['GET'])
@jwt_required()
@require_permission('users', 'read')
def get_admin_stats():
    """Récupérer les statistiques d'administration"""
    try:
        stats = db.get_admin_statistics()
        return jsonify({
            'success': True,
            'data': stats
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== GESTION DES ERREURS =====

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint non trouvé'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Erreur interne du serveur'
    }), 500

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'success': False,
        'error': 'Token expiré, veuillez vous reconnecter'
    }), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({
        'success': False,
        'error': 'Token invalide'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({
        'success': False,
        'error': 'Token d\'authentification requis'
    }), 401

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB AVANCÉ...")
    print("🐘 PostgreSQL avec authentification JWT")
    print("🔒 Permissions et audit activés")
    print("📊 Rapports et analytics disponibles")
    
    # Afficher les statistiques au démarrage
    try:
        stats = db.get_database_stats()
        print(f"📈 Statistiques : {stats}")
    except:
        print("⚠️ Impossible de récupérer les statistiques")
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("🔑 Connexion admin : admin / Admin123!")
    
    # Lancer le serveur
    app.run(debug=False, host='0.0.0.0', port=5000)
