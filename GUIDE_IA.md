# 🤖 Guide d'utilisation de l'IA - ERP HUB

## 🚀 Vue d'ensemble

L'ERP HUB intègre maintenant un module d'Intelligence Artificielle complet qui vous aide à :
- 📊 **Analyser vos données** automatiquement
- 🔮 **Prédire les tendances** budgétaires et KPIs
- 🤖 **Converser** avec vos données via un assistant intelligent
- 🔍 **Détecter les anomalies** dans vos processus
- 💡 **Obtenir des insights** personnalisés

## 🎯 Fonctionnalités IA disponibles

### 1. 🤖 Assistant Conversationnel
**URL**: `http://localhost:5173/ai-assistant.html`

**Capacités**:
- Répondre à vos questions sur les données ERP
- Analyser vos budgets, KPIs, employés, contacts
- Fournir des recommandations personnalisées
- Mode de secours intelligent (sans OpenAI)

**Exemples de questions**:
- "Analyse mes budgets"
- "Quel est l'état de mes KPIs ?"
- "Montre-moi les anomalies détectées"
- "Prédis mes ventes du mois prochain"

### 2. 📊 Prédictions Budgétaires
**Endpoint**: `POST /api/ai/predict-budgets`

**Fonctionnalités**:
- Analyse des tendances de réalisation
- Prédictions pour le mois suivant
- Recommandations d'optimisation
- Détection des budgets à risque

### 3. 📈 Analyse des KPIs
**Endpoint**: `POST /api/ai/analyze-kpis`

**Fonctionnalités**:
- Calcul automatique des performances
- Identification des KPIs critiques
- Recommandations d'amélioration
- Analyse par catégorie

### 4. 🔍 Détection d'Anomalies
**Endpoint**: `POST /api/ai/detect-anomalies`

**Types d'anomalies détectées**:
- Dépassements budgétaires (>150%)
- Sous-réalisations importantes (<30%)
- Patterns inhabituels dans les données
- Écarts significatifs par rapport aux prévisions

### 5. 💡 Insights Globaux
**Endpoint**: `GET /api/ai/insights`

**Contenu**:
- Résumé des analyses IA
- Anomalies détectées
- Recommandations prioritaires
- Métriques de performance globales

## 🛠️ Configuration

### Installation des dépendances IA
```bash
cd backend
pip install openai scikit-learn pandas numpy
```

### Configuration OpenAI (Optionnel)
1. Créez un compte sur [OpenAI](https://platform.openai.com/)
2. Obtenez votre clé API
3. Créez un fichier `.env` dans le dossier `backend`:
```bash
OPENAI_API_KEY=your_api_key_here
```

### Mode de secours
Si OpenAI n'est pas configuré, l'IA fonctionne en **mode de secours** avec :
- Réponses préprogrammées intelligentes
- Analyses statistiques locales
- Prédictions basées sur des algorithmes simples

## 📱 Interface utilisateur

### Accès à l'Assistant IA
1. **Depuis le Dashboard**: Cliquez sur le bouton "🤖 Assistant IA"
2. **URL directe**: `http://localhost:5173/ai-assistant.html`

### Fonctionnalités de l'interface
- **Chat en temps réel** avec l'IA
- **Questions rapides** prédéfinies
- **Panel d'insights** avec analyses automatiques
- **Statut de connexion** en temps réel
- **Historique des conversations**

## 🔧 API Endpoints IA

### 1. Chat Assistant
```bash
POST /api/ai/chat
Content-Type: application/json

{
  "message": "Analyse mes budgets"
}
```

### 2. Prédictions Budgétaires
```bash
POST /api/ai/predict-budgets
```

### 3. Analyse KPIs
```bash
POST /api/ai/analyze-kpis
```

### 4. Insights Globaux
```bash
GET /api/ai/insights
```

## 📊 Exemples de réponses IA

### Prédictions Budgétaires
```json
{
  "success": true,
  "summary": {
    "realization_rate": 27.24,
    "total_forecast": 670000.0,
    "total_realized": 182500.0,
    "overall_trend": "negative"
  },
  "predictions": [
    {
      "budget_id": "test_003",
      "category": "Ventes",
      "current_rate": 25.0,
      "trend": "attention",
      "next_month_forecast": 475000.0,
      "recommendation": "Attention! Révision urgente nécessaire."
    }
  ],
  "insights": [
    "⚠️ Performance budgétaire nécessitant une attention immédiate.",
    "💰 Taux de réalisation des revenus: 25.0%"
  ]
}
```

### Détection d'Anomalies
```json
{
  "success": true,
  "anomalies_count": 2,
  "anomalies": [
    {
      "type": "budget_underrun",
      "severity": "medium",
      "description": "Sous-réalisation importante: 125000.0 vs 500000.0",
      "recommendation": "Analyser les causes de la sous-performance"
    }
  ]
}
```

## 🎯 Cas d'usage recommandés

### 1. Analyse quotidienne
- Consultez les insights IA chaque matin
- Vérifiez les anomalies détectées
- Suivez les recommandations prioritaires

### 2. Planification mensuelle
- Utilisez les prédictions budgétaires
- Analysez les tendances KPIs
- Ajustez vos stratégies selon les insights IA

### 3. Prise de décision
- Posez des questions spécifiques à l'assistant
- Obtenez des analyses en temps réel
- Validez vos hypothèses avec les données IA

## 🔒 Sécurité et Confidentialité

- **Données locales**: Toutes les analyses sont effectuées localement
- **Pas de stockage externe**: Vos données ne quittent pas votre serveur
- **OpenAI optionnel**: Fonctionne sans services externes
- **Chiffrement**: Communications sécurisées via HTTPS

## 🚀 Évolutions futures

### Phase 2 (prochaines semaines)
- **Machine Learning avancé** avec modèles personnalisés
- **Prédictions multi-variables** plus précises
- **Alertes automatiques** par email/SMS
- **Rapports IA** automatisés

### Phase 3 (prochains mois)
- **IA prédictive avancée** pour tous les modules
- **Optimisation automatique** des processus
- **Intégration vocale** (commandes vocales)
- **IA collaborative** multi-utilisateurs

## 📞 Support

Pour toute question sur l'IA :
1. Consultez ce guide
2. Testez l'assistant IA intégré
3. Vérifiez les logs du serveur backend
4. Contactez l'équipe de développement

---

**🎉 Félicitations ! Votre ERP HUB est maintenant équipé d'une Intelligence Artificielle avancée !**
