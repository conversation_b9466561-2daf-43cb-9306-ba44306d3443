<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Finance - Gestion Financière | ERP HUB</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #f97316 30%, #ea580c 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #f97316;
            color: white;
        }
        
        .btn-primary:hover {
            background: #ea580c;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        
        .btn-info:hover {
            background: #2563eb;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #f97316;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .amount-positive {
            color: #10b981;
            font-weight: 600;
        }
        
        .amount-negative {
            color: #ef4444;
            font-weight: 600;
        }
        
        .amount-neutral {
            color: #6b7280;
            font-weight: 600;
        }
        
        .balance-critical {
            color: #ef4444;
            font-weight: 700;
        }
        
        .balance-warning {
            color: #f59e0b;
            font-weight: 600;
        }
        
        .balance-good {
            color: #10b981;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close-btn:hover {
            color: #374151;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #f97316;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }

        /* Styles pour les onglets */
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #f97316;
            background: #fff7ed;
        }

        .nav-tab.active {
            color: #f97316;
            border-bottom-color: #f97316;
            background: #fff7ed;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        /* Styles pour les contenus d'onglets */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Styles spécifiques au module budget */
        .budget-category-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s;
        }

        .budget-category-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #f97316;
        }

        .budget-category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .budget-category-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
        }

        .budget-progress {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .budget-progress-bar {
            height: 100%;
            transition: width 0.3s;
        }

        .budget-progress-bar.good {
            background: #10b981;
        }

        .budget-progress-bar.warning {
            background: #f59e0b;
        }

        .budget-progress-bar.danger {
            background: #ef4444;
        }

        .budget-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .budget-stat {
            text-align: center;
        }

        .budget-stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .budget-stat-label {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .budget-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        /* Classes pour les montants colorés */
        .amount-positive {
            color: #10b981 !important;
            font-weight: 600;
        }

        .amount-negative {
            color: #ef4444 !important;
            font-weight: 600;
        }

        .amount-warning {
            color: #f59e0b !important;
            font-weight: 600;
        }

        /* Styles pour les boutons de petite taille */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Styles pour les panneaux de filtres (style Power BI) */
        .filter-row {
            display: flex;
            flex-direction: row;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-panel {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            min-width: 280px;
        }

        .filter-header {
            background: #f9fafb;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            transition: background-color 0.2s;
        }

        .filter-header:hover {
            background: #f3f4f6;
        }

        .filter-arrow {
            margin-left: auto;
            transition: transform 0.2s;
        }

        .filter-arrow.rotated {
            transform: rotate(180deg);
        }

        .filter-content {
            max-height: 300px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .filter-content.collapsed {
            max-height: 0;
        }

        .filter-search {
            padding: 0.75rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .filter-search input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        .filter-items {
            max-height: 200px;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.25rem;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 0.875rem;
        }

        .filter-item:hover {
            background: #f9fafb;
        }

        .filter-item input[type="checkbox"] {
            margin: 0;
            width: 16px;
            height: 16px;
        }

        .checkmark {
            width: 16px;
            height: 16px;
            border: 2px solid #d1d5db;
            border-radius: 3px;
            position: relative;
            background: white;
            transition: all 0.2s;
        }

        .filter-item input[type="checkbox"]:checked + .checkmark {
            background: #f97316;
            border-color: #f97316;
        }

        .filter-item input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .filter-text {
            flex: 1;
            user-select: none;
        }

        .filter-count {
            background: #f97316;
            color: white;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: auto;
        }

        /* Styles pour les cases à cocher personnalisées */
        .filter-item input[type="checkbox"] {
            display: none;
        }

        /* Styles pour l'interface tableur */
        .spreadsheet-toolbar {
            background: #f8fafc;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .editable-cell {
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .editable-cell:hover {
            background-color: #f3f4f6;
        }

        .editable-cell.editing {
            background-color: #dbeafe;
            border: 2px solid #3b82f6;
        }

        .editable-cell.valid {
            background-color: #dcfce7;
        }

        .editable-cell.invalid {
            background-color: #fecaca;
        }

        .editable-cell.warning {
            background-color: #fef3c7;
        }

        .cell-editor {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: none;
            background: white;
            padding: 0.5rem;
            font-size: inherit;
            font-family: inherit;
            z-index: 10;
        }

        .cell-editor:focus {
            outline: none;
        }

        .row-selector {
            width: 20px;
            text-align: center;
        }

        .row-selected {
            background-color: #e0f2fe !important;
        }

        .sticky-column {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            border-right: 2px solid #e5e7eb;
        }

        .sticky-header {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }

        /* Styles pour l'import */
        .import-step {
            margin: 1rem 0;
        }

        .mapping-field {
            margin-bottom: 1rem;
        }

        .mapping-field label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #374151;
        }

        .file-column {
            background: #f9fafb;
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.25rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .file-column:hover {
            background: #f3f4f6;
        }

        .file-column.mapped {
            background: #dcfce7;
            border-color: #10b981;
        }

        .validation-error {
            background: #fecaca;
            color: #dc2626;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }

        .validation-warning {
            background: #fef3c7;
            color: #d97706;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }

        .validation-success {
            background: #dcfce7;
            color: #059669;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0;
        }

        /* Animation de rotation pour l'indicateur de sauvegarde */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Styles pour les raccourcis clavier */
        .keyboard-shortcut {
            background: #e5e7eb;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-family: monospace;
            margin: 0 0.125rem;
        }

        /* ===== STYLES ÉDITION INLINE COMPLÈTE PHASE 2 ===== */

        /* Barre d'outils tableur améliorée */
        .spreadsheet-toolbar {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0 0.75rem;
            border-right: 1px solid #cbd5e1;
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .toolbar-btn {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            padding: 0.5rem 0.875rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .toolbar-btn:hover {
            background: #f8fafc;
            border-color: #94a3b8;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toolbar-btn:active {
            background: #e2e8f0;
            transform: translateY(0);
        }

        .toolbar-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .toolbar-btn.disabled:hover {
            background: white;
            border-color: #d1d5db;
            transform: none;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .toolbar-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #2563eb;
        }

        .toolbar-btn.primary:hover {
            background: #2563eb;
            border-color: #1d4ed8;
        }

        .toolbar-btn.success {
            background: #10b981;
            color: white;
            border-color: #059669;
        }

        .toolbar-btn.success:hover {
            background: #059669;
            border-color: #047857;
        }

        .toolbar-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #dc2626;
        }

        .toolbar-btn.danger:hover {
            background: #dc2626;
            border-color: #b91c1c;
        }

        /* Indicateurs de statut */
        .selection-info {
            font-size: 0.875rem;
            color: #475569;
            font-weight: 600;
            background: #f1f5f9;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            border: 1px solid #cbd5e1;
        }

        .save-indicator {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            border: 1px solid;
            transition: all 0.3s ease;
        }

        .save-indicator.saved {
            color: #059669;
            background: #f0fdf4;
            border-color: #bbf7d0;
        }

        .save-indicator.saving {
            color: #d97706;
            background: #fffbeb;
            border-color: #fed7aa;
        }

        .save-indicator.error {
            color: #dc2626;
            background: #fef2f2;
            border-color: #fecaca;
        }

        .save-indicator .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* ===== STYLES ÉDITION CELLULES COMPLÈTE ===== */

        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            min-height: 2.5rem;
            padding: 0.375rem 0.75rem;
            background: white;
            border-radius: 0.25rem;
        }

        .editable-cell:hover {
            background-color: #f0f9ff !important;
            border-color: #bfdbfe;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .editable-cell.editing {
            background-color: #dbeafe !important;
            border: 2px solid #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            z-index: 100;
        }

        .editable-cell.active {
            background-color: #fef3c7 !important;
            border-color: #f59e0b;
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
        }

        .editable-cell.valid {
            border-left: 4px solid #10b981;
            background-color: #f0fdf4 !important;
        }

        .editable-cell.invalid {
            border-left: 4px solid #ef4444;
            background-color: #fef2f2 !important;
        }

        .editable-cell.warning {
            border-left: 4px solid #f59e0b;
            background-color: #fffbeb !important;
        }

        .editable-cell.required {
            border-left: 4px solid #8b5cf6;
        }

        /* Éditeurs de cellules spécialisés */
        .cell-editor {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid #3b82f6;
            background: white;
            font-size: inherit;
            font-family: inherit;
            padding: 0.375rem 0.75rem;
            z-index: 1000;
            border-radius: 0.375rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            outline: none;
        }

        .cell-editor:focus {
            outline: none;
            border-color: #2563eb;
        }

        .cell-editor.select-editor {
            padding: 0.25rem 0.5rem;
            cursor: pointer;
        }

        .cell-editor.number-editor {
            text-align: right;
            font-variant-numeric: tabular-nums;
        }

        .cell-editor.textarea-editor {
            resize: none;
            min-height: 4rem;
            line-height: 1.4;
        }

        .cell-editor.date-editor {
            font-family: monospace;
        }

        /* Listes déroulantes inline */
        .inline-select {
            width: 100%;
            border: none;
            background: transparent;
            font-size: inherit;
            font-family: inherit;
            padding: 0.375rem 0.75rem;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .inline-select:focus {
            outline: none;
            background: white;
            border: 2px solid #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .inline-select option {
            padding: 0.5rem;
            background: white;
            color: #374151;
        }

        .inline-select option:hover {
            background: #f3f4f6;
        }

        /* Messages de validation temps réel */
        .validation-message {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-size: 0.8125rem;
            z-index: 1001;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            margin-top: 0.25rem;
        }

        .validation-message.error {
            border-color: #ef4444;
            color: #dc2626;
            background: #fef2f2;
        }

        .validation-message.warning {
            border-color: #f59e0b;
            color: #d97706;
            background: #fffbeb;
        }

        .validation-message.success {
            border-color: #10b981;
            color: #059669;
            background: #f0fdf4;
        }

        .validation-message.info {
            border-color: #3b82f6;
            color: #2563eb;
            background: #eff6ff;
        }

        .validation-message::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 1rem;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid currentColor;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>
    <header class="header">
        <div class="logo">💰 Agent Finance - ERP HUB</div>
        <div class="nav-buttons">
            <button class="btn btn-primary" onclick="openModal('movementModal')">
                <span class="material-icons" style="font-size: 1rem;">add</span>
                Nouveau Mouvement
            </button>
            <button class="btn btn-warning" onclick="exportSelectedRows()">
                <span class="material-icons" style="font-size: 1rem;">download</span>
                Exporter
            </button>
            <button class="btn btn-success" onclick="generateFinanceReport()">
                <span class="material-icons" style="font-size: 1rem;">assessment</span>
                Générer Rapport
            </button>
            <a href="dashboard-demo.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Financière</h1>
            <p class="page-subtitle">Trésorerie, budgets et analyses financières</p>
        </div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('treasury')">
                <span class="material-icons">account_balance</span>
                Trésorerie
            </button>
            <button class="nav-tab" onclick="showTab('budget')">
                <span class="material-icons">pie_chart</span>
                Gestion Budgétaire
            </button>
            <button class="nav-tab" onclick="showTab('analysis')">
                <span class="material-icons">analytics</span>
                Analyse Financière
            </button>
        </nav>

        <!-- Onglet Trésorerie -->
        <div id="treasury" class="tab-content active">
            <!-- Statistiques -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalTreasury">0€</div>
                    <div class="stat-label">Trésorerie Totale</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="monthlyVariation">0€</div>
                    <div class="stat-label">Variation Mensuelle</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="availableBudget">0€</div>
                    <div class="stat-label">Budget Disponible</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="monthlyROI">0%</div>
                    <div class="stat-label">ROI Mensuel</div>
                </div>
            </div>

        <!-- Comptes bancaires -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Comptes Bancaires</h2>
                <button class="btn btn-primary" onclick="refreshAccounts()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div id="alertContainer"></div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Compte</th>
                                <th>Banque</th>
                                <th>Type</th>
                                <th>Solde</th>
                                <th>Seuil Alerte</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="accountsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Mouvements de trésorerie -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">Mouvements de Trésorerie</h2>
                <button class="btn btn-primary" onclick="refreshMovements()">
                    <span class="material-icons" style="font-size: 1rem;">refresh</span>
                    Actualiser
                </button>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date Transaction</th>
                                <th>Date Valeur</th>
                                <th>Date Échéance</th>
                                <th>Libellé</th>
                                <th>Compte</th>
                                <th>Type</th>
                                <th>Montant</th>
                                <th>Référence</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        </div>

        <!-- Onglet Gestion Budgétaire -->
        <div id="budget" class="tab-content">
            <!-- Statistiques budgétaires -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalBudgetAllocated">0€</div>
                    <div class="stat-label">Budget Total Alloué</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalBudgetSpent">0€</div>
                    <div class="stat-label">Total Dépensé</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalBudgetRemaining">0€</div>
                    <div class="stat-label">Budget Restant</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="budgetUtilizationRate">0%</div>
                    <div class="stat-label">Taux d'Utilisation</div>
                </div>
            </div>

            <!-- Conteneur en ligne -->
<div class="filter-row">
    
    <!-- Filtre Centres de Coûts -->
    <div class="filter-panel">
        <div class="filter-header" onclick="toggleFilterPanel('costCenterPanel')">
            <span class="material-icons">business</span>
            <span>Centres de Coûts</span>
            <span class="material-icons filter-arrow">expand_more</span>
        </div>
        <div id="costCenterPanel" class="filter-content">
            <div class="filter-search">
                <input type="text" id="costCenterSearch" placeholder="Rechercher..." onkeyup="filterSearchItems('costCenter')">
            </div>
            <div class="filter-items" id="costCenterItems">
                <!-- Items générés dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Filtre Codes Analytiques -->
    <div class="filter-panel">
        <div class="filter-header" onclick="toggleFilterPanel('analyticCodePanel')">
            <span class="material-icons">analytics</span>
            <span>Codes Analytiques</span>
            <span class="material-icons filter-arrow">expand_more</span>
        </div>
        <div id="analyticCodePanel" class="filter-content">
            <div class="filter-search">
                <input type="text" id="analyticCodeSearch" placeholder="Rechercher..." onkeyup="filterSearchItems('analyticCode')">
            </div>
            <div class="filter-items" id="analyticCodeItems">
                <!-- Items générés dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Filtre Types de Budget -->
    <div class="filter-panel">
        <div class="filter-header" onclick="toggleFilterPanel('budgetTypePanel')">
            <span class="material-icons">category</span>
            <span>Types de Budget</span>
            <span class="material-icons filter-arrow">expand_more</span>
        </div>
        <div id="budgetTypePanel" class="filter-content">
            <div class="filter-items" id="budgetTypeItems">
                <label class="filter-item">
                    <input type="checkbox" value="revenue" checked onchange="updateFilterCount('budgetType')">
                    <span class="checkmark"></span>
                    <span class="filter-text">💰 Revenus</span>
                </label>
                <label class="filter-item">
                    <input type="checkbox" value="expense" checked onchange="updateFilterCount('budgetType')">
                    <span class="checkmark"></span>
                    <span class="filter-text">🏢 Charges d'exploitation</span>
                </label>
                <label class="filter-item">
                    <input type="checkbox" value="financial" checked onchange="updateFilterCount('budgetType')">
                    <span class="checkmark"></span>
                    <span class="filter-text">🏦 Charges financières</span>
                </label>
                <label class="filter-item">
                    <input type="checkbox" value="investment" checked onchange="updateFilterCount('budgetType')">
                    <span class="checkmark"></span>
                    <span class="filter-text">🔧 Investissements</span>
                </label>
                <label class="filter-item">
                    <input type="checkbox" value="exceptional" checked onchange="updateFilterCount('budgetType')">
                    <span class="checkmark"></span>
                    <span class="filter-text">⚠️ Charges exceptionnelles</span>
                </label>
            </div>
        </div>
    </div>

</div>




            <!-- Analyse Financière Multi-Années Améliorée -->
            <div class="content-section" id="advancedFinancialAnalysis">
                <div class="section-header">
                    <h2 class="section-title">📈 Analyse Financière Multi-Années</h2>
                    <div style="display: flex; gap: 0.5rem; align-items: center;">
                        <button class="btn btn-primary" onclick="exportChartData()">
                            <span class="material-icons" style="font-size: 1rem;">download</span>
                            Exporter
                        </button>
                        <button class="btn btn-secondary" onclick="resetChartView()">
                            <span class="material-icons" style="font-size: 1rem;">refresh</span>
                            Réinitialiser
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <!-- Contrôles de sélection avancés -->
                    <div class="advanced-controls" style="background: #f9fafb; padding: 1.5rem; border-radius: 0.75rem; margin-bottom: 1.5rem;">
                        <!-- Sélection des années -->
                        <div class="control-row" style="display: flex; gap: 2rem; align-items: center; margin-bottom: 1rem; flex-wrap: wrap;">
                            <div class="control-group">
                                <label class="control-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; display: block;">📅 Années à comparer :</label>
                                <div style="display: flex; gap: 1rem; align-items: center;">
                                    <div>
                                        <label style="font-size: 0.875rem; color: #6b7280;">Année 1 :</label>
                                        <select id="year1Selector" class="form-select" style="width: 120px;" onchange="updateFinancialChart()">
                                            <option value="2024" selected>2024</option>
                                            <option value="2023">2023</option>
                                            <option value="2022">2022</option>
                                            <option value="2021">2021</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label style="font-size: 0.875rem; color: #6b7280;">Année 2 :</label>
                                        <select id="year2Selector" class="form-select" style="width: 120px;" onchange="updateFinancialChart()">
                                            <option value="2024">2024</option>
                                            <option value="2023" selected>2023</option>
                                            <option value="2022">2022</option>
                                            <option value="2021">2021</option>
                                        </select>
                                    </div>
                                    <div class="ratio-display" style="background: white; padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                                        <span style="font-size: 0.875rem; color: #6b7280;">Ratio :</span>
                                        <span id="yearRatio" style="font-weight: 600; color: #3b82f6;">+0.0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sélection des indicateurs -->
                        <div class="control-row" style="display: flex; gap: 2rem; align-items: flex-start; margin-bottom: 1rem; flex-wrap: wrap;">
                            <div class="control-group" style="flex: 1; min-width: 300px;">
                                <label class="control-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; display: block;">📊 Indicateurs à afficher :</label>
                                <div class="indicators-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.75rem;">
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showRevenue" checked onchange="updateFinancialChart()">
                                        <span style="color: #10b981;">💰</span>
                                        <span style="font-size: 0.875rem;">Chiffre d'Affaires</span>
                                    </label>
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showExpenses" checked onchange="updateFinancialChart()">
                                        <span style="color: #ef4444;">📉</span>
                                        <span style="font-size: 0.875rem;">Charges d'Exploitation</span>
                                    </label>
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showInvestments" onchange="updateFinancialChart()">
                                        <span style="color: #8b5cf6;">🏗️</span>
                                        <span style="font-size: 0.875rem;">Investissements</span>
                                    </label>
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showMargins" onchange="updateFinancialChart()">
                                        <span style="color: #f59e0b;">📈</span>
                                        <span style="font-size: 0.875rem;">Marges Bénéficiaires</span>
                                    </label>
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showCostCenters" onchange="updateFinancialChart()">
                                        <span style="color: #6366f1;">🏢</span>
                                        <span style="font-size: 0.875rem;">Coûts par Centre</span>
                                    </label>
                                    <label class="indicator-item" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; cursor: pointer;">
                                        <input type="checkbox" id="showRevenueByCategory" onchange="updateFinancialChart()">
                                        <span style="color: #14b8a6;">📊</span>
                                        <span style="font-size: 0.875rem;">Revenus par Catégorie</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Options d'affichage -->
                        <div class="control-row" style="display: flex; gap: 2rem; align-items: center; flex-wrap: wrap;">
                            <div class="control-group">
                                <label class="control-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; display: block;">⚙️ Options d'affichage :</label>
                                <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                                    <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem;">
                                        <input type="checkbox" id="showTrendLines" onchange="updateFinancialChart()">
                                        <span>Lignes de tendance</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem;">
                                        <input type="checkbox" id="showDataPoints" checked onchange="updateFinancialChart()">
                                        <span>Points de données</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem;">
                                        <input type="checkbox" id="showPercentages" onchange="updateFinancialChart()">
                                        <span>Affichage en %</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Graphique principal -->
                    <div class="chart-container" style="background: white; border-radius: 0.75rem; padding: 1.5rem; border: 1px solid #e5e7eb; margin-bottom: 1.5rem;">
                        <div id="financialChart" style="height: 500px;">
                            <canvas id="financialChartCanvas" width="100%" height="450"></canvas>
                        </div>
                    </div>

                    <!-- Tableau de ratios et statistiques -->
                    <div class="ratios-summary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <div class="ratio-card" style="background: white; padding: 1rem; border-radius: 0.75rem; border: 1px solid #e5e7eb;">
                            <h4 style="color: #374151; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: #10b981;">📊</span>
                                Ratios Annuels
                            </h4>
                            <div id="annualRatios">
                                <!-- Ratios calculés dynamiquement -->
                            </div>
                        </div>
                        <div class="trend-card" style="background: white; padding: 1rem; border-radius: 0.75rem; border: 1px solid #e5e7eb;">
                            <h4 style="color: #374151; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: #3b82f6;">📈</span>
                                Tendances
                            </h4>
                            <div id="trendSummary">
                                <!-- Analyses de tendances -->
                            </div>
                        </div>
                        <div class="insights-card" style="background: white; padding: 1rem; border-radius: 0.75rem; border: 1px solid #e5e7eb;">
                            <h4 style="color: #374151; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: #f59e0b;">💡</span>
                                Insights
                            </h4>
                            <div id="financialInsights">
                                <!-- Insights automatiques -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique de répartition budgétaire -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Répartition Budgétaire par Période</h2>
                    <div style="display: flex; gap: 0.5rem;">
                        <button class="btn btn-secondary" onclick="toggleChartType('bar')" id="barChartBtn">
                            <span class="material-icons" style="font-size: 1rem;">bar_chart</span>
                            Barres
                        </button>
                        <button class="btn btn-secondary" onclick="toggleChartType('pie')" id="pieChartBtn">
                            <span class="material-icons" style="font-size: 1rem;">pie_chart</span>
                            Secteurs
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <div id="budgetChart" style="height: 400px; background: white; border-radius: 0.75rem; padding: 1rem; border: 1px solid #e5e7eb;">
                        <canvas id="budgetChartCanvas" width="100%" height="350"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Analyse Financière -->
        <div id="analysis" class="tab-content">
            <!-- Filtres pour l'analyse -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🔍 Filtres d'Analyse</h2>
                    <div style="display: flex; gap: 0.5rem;">
                        <button class="btn btn-secondary" onclick="copyFiltersFromBudget()">
                            <span class="material-icons" style="font-size: 1rem;">content_copy</span>
                            Copier filtres Budget
                        </button>
                        <button class="btn btn-secondary" onclick="clearAnalysisFilters()">
                            <span class="material-icons" style="font-size: 1rem;">clear_all</span>
                            Réinitialiser
                        </button>
                        <button class="btn btn-primary" onclick="applyAnalysisFilters()">
                            <span class="material-icons" style="font-size: 1rem;">filter_list</span>
                            Appliquer à l'analyse
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">

                        <!-- Filtre Centres de Coûts pour Analyse -->
                        <div class="filter-panel">
                            <div class="filter-header" onclick="toggleFilterPanel('analysisCostCenterPanel')">
                                <span class="material-icons">business</span>
                                <span>Centres de Coûts</span>
                                <span class="material-icons filter-arrow">expand_more</span>
                            </div>
                            <div id="analysisCostCenterPanel" class="filter-content">
                                <div class="filter-search">
                                    <input type="text" id="analysisCostCenterSearch" placeholder="Rechercher..." onkeyup="filterSearchItems('analysisCostCenter')">
                                </div>
                                <div class="filter-items" id="analysisCostCenterItems">
                                    <!-- Items générés dynamiquement -->
                                </div>
                            </div>
                        </div>

                        <!-- Filtre Codes Analytiques pour Analyse -->
                        <div class="filter-panel">
                            <div class="filter-header" onclick="toggleFilterPanel('analysisAnalyticCodePanel')">
                                <span class="material-icons">analytics</span>
                                <span>Codes Analytiques</span>
                                <span class="material-icons filter-arrow">expand_more</span>
                            </div>
                            <div id="analysisAnalyticCodePanel" class="filter-content">
                                <div class="filter-search">
                                    <input type="text" id="analysisAnalyticCodeSearch" placeholder="Rechercher..." onkeyup="filterSearchItems('analysisAnalyticCode')">
                                </div>
                                <div class="filter-items" id="analysisAnalyticCodeItems">
                                    <!-- Items générés dynamiquement -->
                                </div>
                            </div>
                        </div>

                        <!-- Filtre Types de Budget pour Analyse -->
                        <div class="filter-panel">
                            <div class="filter-header" onclick="toggleFilterPanel('analysisBudgetTypePanel')">
                                <span class="material-icons">category</span>
                                <span>Types de Budget</span>
                                <span class="material-icons filter-arrow">expand_more</span>
                            </div>
                            <div id="analysisBudgetTypePanel" class="filter-content">
                                <div class="filter-items" id="analysisBudgetTypeItems">
                                    <label class="filter-item">
                                        <input type="checkbox" value="revenue" checked onchange="updateFilterCount('analysisBudgetType')">
                                        <span class="checkmark"></span>
                                        <span class="filter-text">💰 Revenus</span>
                                    </label>
                                    <label class="filter-item">
                                        <input type="checkbox" value="expense" checked onchange="updateFilterCount('analysisBudgetType')">
                                        <span class="checkmark"></span>
                                        <span class="filter-text">🏢 Charges d'exploitation</span>
                                    </label>
                                    <label class="filter-item">
                                        <input type="checkbox" value="financial" checked onchange="updateFilterCount('analysisBudgetType')">
                                        <span class="checkmark"></span>
                                        <span class="filter-text">🏦 Charges financières</span>
                                    </label>
                                    <label class="filter-item">
                                        <input type="checkbox" value="investment" checked onchange="updateFilterCount('analysisBudgetType')">
                                        <span class="checkmark"></span>
                                        <span class="filter-text">🔧 Investissements</span>
                                    </label>
                                    <label class="filter-item">
                                        <input type="checkbox" value="exceptional" checked onchange="updateFilterCount('analysisBudgetType')">
                                        <span class="checkmark"></span>
                                        <span class="filter-text">⚠️ Charges exceptionnelles</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Tableau Croisé Dynamique -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📊 Tableau Croisé Dynamique (TCD)</h2>
                    <div style="display: flex; gap: 0.5rem;">
                        <button class="btn btn-primary" onclick="generatePivotTable()">
                            <span class="material-icons" style="font-size: 1rem;">pivot_table_chart</span>
                            Générer TCD
                        </button>
                        <button class="btn btn-info" onclick="refreshPivotTable()">
                            <span class="material-icons" style="font-size: 1rem;">refresh</span>
                            Actualiser
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    <!-- Contrôles du TCD -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem; padding: 1rem; background: #f9fafb; border-radius: 0.5rem;">
                        <div class="form-group">
                            <label class="form-label">Lignes (Grouper par) :</label>
                            <select id="pivotRows" class="form-select" onchange="updatePivotTable()">
                                <option value="categoryType">Type de Catégorie</option>
                                <option value="costCenter">Centre de Coûts</option>
                                <option value="analyticCode">Code Analytique</option>
                                <option value="department">Département</option>
                                <option value="responsible">Responsable</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Colonnes (Répartir par) :</label>
                            <select id="pivotColumns" class="form-select" onchange="updatePivotTable()">
                                <option value="month">Mois</option>
                                <option value="quarter">Trimestre</option>
                                <option value="categoryType">Type de Catégorie</option>
                                <option value="costCenter">Centre de Coûts</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Valeurs (Mesures) :</label>
                            <select id="pivotValues" class="form-select" onchange="updatePivotTable()">
                                <option value="forecast">Budget Prévisionnel</option>
                                <option value="realized">Montant Réalisé</option>
                                <option value="variance">Écart (Réalisé - Prévisionnel)</option>
                                <option value="percentage">Pourcentage de Réalisation</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Fonction d'agrégation :</label>
                            <select id="pivotAggregation" class="form-select" onchange="updatePivotTable()">
                                <option value="sum">Somme</option>
                                <option value="average">Moyenne</option>
                                <option value="count">Nombre</option>
                                <option value="min">Minimum</option>
                                <option value="max">Maximum</option>
                            </select>
                        </div>
                    </div>

                    <!-- Tableau TCD -->
                    <div class="table-container" style="max-height: 500px; overflow: auto;">
                        <table class="data-table" id="pivotTable">
                            <thead id="pivotTableHead">
                                <!-- En-têtes générés dynamiquement -->
                            </thead>
                            <tbody id="pivotTableBody">
                                <!-- Données générées dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analyse Financière Avancée -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📈 Analyse Financière Avancée</h2>
                    <button class="btn btn-primary" onclick="generateFinanceReport()">
                        <span class="material-icons" style="font-size: 1rem;">assessment</span>
                        Générer Rapport
                    </button>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <!-- Ratios Financiers -->
                        <div style="background: white; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                            <h3 style="color: #374151; margin-bottom: 1rem;">💹 Ratios Financiers</h3>
                            <div id="financialRatios">
                                <!-- Ratios calculés dynamiquement -->
                            </div>
                        </div>

                        <!-- Tendances -->
                        <div style="background: white; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                            <h3 style="color: #374151; margin-bottom: 1rem;">📊 Tendances</h3>
                            <div id="trendAnalysis">
                                <!-- Analyses de tendances -->
                            </div>
                        </div>

                        <!-- Alertes -->
                        <div style="background: white; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                            <h3 style="color: #374151; margin-bottom: 1rem;">⚠️ Alertes</h3>
                            <div id="budgetAlerts">
                                <!-- Alertes budgétaires -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Mouvement Bancaire -->
    <div id="movementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="movementModalTitle">Nouveau Mouvement Bancaire</h3>
                <button class="close-btn" onclick="closeModal('movementModal')">&times;</button>
            </div>
            <form id="movementForm">
                <div id="movementModalAlertContainer"></div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="movementDate">Date *</label>
                        <input type="date" id="movementDate" name="movementDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementAccount">Compte *</label>
                        <select id="movementAccount" name="movementAccount" class="form-select" required>
                            <option value="">Sélectionner un compte</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementType">Type *</label>
                        <select id="movementType" name="movementType" class="form-select" required>
                            <option value="">Sélectionner un type</option>
                            <option value="income">Recette (+)</option>
                            <option value="expense">Dépense (-)</option>
                            <option value="transfer">Virement</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementAmount">Montant (€) *</label>
                        <input type="number" id="movementAmount" name="movementAmount" class="form-input" required min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementLabel">Libellé *</label>
                        <input type="text" id="movementLabel" name="movementLabel" class="form-input" required placeholder="Description du mouvement">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementReference">Référence</label>
                        <input type="text" id="movementReference" name="movementReference" class="form-input" placeholder="N° facture, chèque, etc.">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="movementCategory">Catégorie</label>
                        <select id="movementCategory" name="movementCategory" class="form-select">
                            <option value="">Sélectionner une catégorie</option>
                            <option value="sales">Ventes</option>
                            <option value="purchases">Achats</option>
                            <option value="salaries">Salaires</option>
                            <option value="taxes">Taxes et charges</option>
                            <option value="investments">Investissements</option>
                            <option value="loans">Emprunts</option>
                            <option value="other">Autres</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="movementNotes">Notes</label>
                    <textarea id="movementNotes" name="movementNotes" class="form-textarea" placeholder="Notes complémentaires..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('movementModal')">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-success" id="saveMovementBtn">
                        <span class="material-icons" style="font-size: 1rem;">save</span>
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>



    <!-- Modal pour l'import Excel/CSV -->
    <div id="importModal" class="modal">
        <div class="modal-content" style="max-width: 90vw; width: 1200px;">
            <div class="modal-header">
                <h3>📊 Import Excel/CSV - Données Budgétaires</h3>
                <span class="close" onclick="closeModal('importModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Étape 1: Sélection du fichier -->
                <div id="importStep1" class="import-step">
                    <h4>📁 Étape 1 : Sélection du fichier</h4>
                    <div style="border: 2px dashed #d1d5db; border-radius: 0.5rem; padding: 2rem; text-align: center; margin: 1rem 0;">
                        <input type="file" id="importFileInput" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileSelect(event)">
                        <button class="btn btn-primary" onclick="document.getElementById('importFileInput').click()">
                            <span class="material-icons">upload_file</span>
                            Choisir un fichier Excel/CSV
                        </button>
                        <p style="margin-top: 1rem; color: #6b7280;">Formats supportés : .xlsx, .xls, .csv (UTF-8)</p>
                        <div id="fileInfo" style="display: none; margin-top: 1rem; padding: 1rem; background: #f3f4f6; border-radius: 0.25rem;">
                            <p><strong>Fichier sélectionné :</strong> <span id="fileName"></span></p>
                            <p><strong>Taille :</strong> <span id="fileSize"></span></p>
                            <p><strong>Type :</strong> <span id="fileType"></span></p>
                        </div>
                    </div>
                </div>

                <!-- Étape 2: Mapping des colonnes -->
                <div id="importStep2" class="import-step" style="display: none;">
                    <h4>🔗 Étape 2 : Mapping des colonnes</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 1rem 0;">
                        <div>
                            <h5>Colonnes du fichier :</h5>
                            <div id="fileColumns" style="max-height: 300px; overflow-y: auto; border: 1px solid #e5e7eb; border-radius: 0.25rem; padding: 1rem;">
                                <!-- Colonnes détectées -->
                            </div>
                        </div>
                        <div>
                            <h5>Champs ERP à mapper :</h5>
                            <div id="erpFields" style="max-height: 300px; overflow-y: auto;">
                                <div class="mapping-field">
                                    <label>Catégorie :</label>
                                    <select id="map_category" class="form-select">
                                        <option value="">-- Sélectionner --</option>
                                    </select>
                                </div>
                                <div class="mapping-field">
                                    <label>Centre de Coûts :</label>
                                    <select id="map_costCenter" class="form-select">
                                        <option value="">-- Sélectionner --</option>
                                    </select>
                                </div>
                                <div class="mapping-field">
                                    <label>Code Analytique :</label>
                                    <select id="map_analyticCode" class="form-select">
                                        <option value="">-- Sélectionner --</option>
                                    </select>
                                </div>
                                <div class="mapping-field">
                                    <label>Responsable :</label>
                                    <select id="map_responsible" class="form-select">
                                        <option value="">-- Sélectionner --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin: 1rem 0;">
                        <button class="btn btn-secondary" onclick="autoMapColumns()">
                            <span class="material-icons">auto_fix_high</span>
                            Mapping automatique
                        </button>
                    </div>
                </div>

                <!-- Étape 3: Prévisualisation -->
                <div id="importStep3" class="import-step" style="display: none;">
                    <h4>👁️ Étape 3 : Prévisualisation et validation</h4>
                    <div id="previewContainer" style="max-height: 400px; overflow: auto; border: 1px solid #e5e7eb; border-radius: 0.25rem; margin: 1rem 0;">
                        <table id="previewTable" class="data-table">
                            <thead id="previewHeader"></thead>
                            <tbody id="previewBody"></tbody>
                        </table>
                    </div>
                    <div id="validationResults" style="margin: 1rem 0;">
                        <!-- Résultats de validation -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('importModal')">Annuler</button>
                <button type="button" class="btn btn-secondary" id="prevStepBtn" onclick="previousImportStep()" style="display: none;">Précédent</button>
                <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextImportStep()" disabled>Suivant</button>
                <button type="button" class="btn btn-success" id="importBtn" onclick="executeImport()" style="display: none;">
                    <span class="material-icons">upload</span>
                    Importer les données
                </button>
            </div>
        </div>
    </div>

    <script>
        let accounts = [];
        let movements = [];
        let budgets = [];
        let budgetCategories = [];
        let editingMovementId = null;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }
        let editingBudgetId = null;
        let isLoading = false;
        let currentTab = 'treasury';
        let currentPeriodFilter = 'month';
        let currentChartType = 'bar';
        let filteredBudgetData = [];

        // Variables pour l'interface tableur
        let editingCell = null;
        let selectedRows = new Set();
        let actionHistory = [];
        let historyIndex = -1;
        let maxHistorySize = 20;
        let autoSaveTimeout = null;
        let currentImportStep = 1;
        let importData = null;
        let columnMapping = {};
        let validationResults = [];

        // Fonction utilitaire pour formater les dates
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        }

        // Données de démonstration réalistes - Comptes bancaires
        const demoAccounts = [
            {
                id: 'ACC-001',
                name: 'Compte Principal',
                bank: 'BNP Paribas',
                type: 'Courant',
                balance: 125000.00,
                alertThreshold: 10000.00,
                iban: 'FR76 3000 6000 0112 3456 7890 189'
            },
            {
                id: 'ACC-002',
                name: 'Compte Épargne',
                bank: 'Crédit Agricole',
                type: 'Épargne',
                balance: 85000.00,
                alertThreshold: 50000.00,
                iban: 'FR76 1751 2000 0003 0123 4567 890'
            },
            {
                id: 'ACC-003',
                name: 'Ligne de Crédit',
                bank: 'Société Générale',
                type: 'Crédit',
                balance: -15000.00,
                alertThreshold: -50000.00,
                iban: 'FR76 3000 3000 4000 0123 4567 890'
            },
            {
                id: 'ACC-004',
                name: 'Compte Devises USD',
                bank: 'HSBC',
                type: 'Devises',
                balance: 45000.00,
                alertThreshold: 5000.00,
                iban: 'FR76 3005 6000 0112 3456 7890 189'
            }
        ];

        // Données de démonstration réalistes - Mouvements bancaires
        const demoMovements = [
            {
                id: 'MOV-001',
                date: '2024-01-25',
                label: 'Virement client Global Manufacturing',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'income',
                amount: 54000.00,
                reference: 'VIR-CLI-001',
                category: 'sales',
                notes: 'Règlement facture FACT-CLI-001',
                valueDate: '2024-01-26',
                dueDate: null,
                createdDate: '2024-01-25T14:30:00'
            },
            {
                id: 'MOV-002',
                date: '2024-01-20',
                label: 'Paiement salaires janvier',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 25000.00,
                reference: 'PAIE-2024-01',
                category: 'salaries',
                notes: 'Salaires nets équipe - 10 employés'
            },
            {
                id: 'MOV-003',
                date: '2024-01-18',
                label: 'Paiement fournisseur TechSupply Co',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 9600.00,
                reference: 'FACT-2024-001',
                category: 'purchases',
                notes: 'Ordinateurs portables Dell'
            },
            {
                id: 'MOV-004',
                date: '2024-01-15',
                label: 'Charges sociales URSSAF',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 15500.00,
                reference: 'URSSAF-2024-01',
                category: 'taxes',
                notes: 'Charges sociales janvier 2024'
            },
            {
                id: 'MOV-005',
                date: '2024-01-30',
                label: 'Virement client StartupTech',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'income',
                amount: 9600.00,
                reference: 'VIR-CLI-002',
                category: 'sales',
                notes: 'Règlement facture services'
            },
            {
                id: 'MOV-006',
                date: '2024-01-12',
                label: 'Placement épargne entreprise',
                accountId: 'ACC-002',
                accountName: 'Compte Épargne',
                type: 'income',
                amount: 50000.00,
                reference: 'VIR-INT-001',
                category: 'investments',
                notes: 'Transfert excédent de trésorerie'
            },
            {
                id: 'MOV-007',
                date: '2024-01-28',
                label: 'Remboursement emprunt équipement',
                accountId: 'ACC-003',
                accountName: 'Ligne de Crédit',
                type: 'expense',
                amount: 5000.00,
                reference: 'PRET-2024-01',
                category: 'loans',
                notes: 'Mensualité janvier - matériel informatique'
            },
            {
                id: 'MOV-008',
                date: '2024-01-22',
                label: 'Frais bancaires',
                accountId: 'ACC-001',
                accountName: 'Compte Principal',
                type: 'expense',
                amount: 125.00,
                reference: 'FRAIS-2024-01',
                category: 'other',
                notes: 'Commission de tenue de compte'
            }
        ];

        // Tables de référence - Centres de coûts
        const costCenters = [
            { id: 'CC-001', code: 'ADM', name: 'Administration Générale', description: 'Direction, RH, Comptabilité' },
            { id: 'CC-002', code: 'COM', name: 'Commercial', description: 'Ventes, Marketing, Relation Client' },
            { id: 'CC-003', code: 'PROD', name: 'Production', description: 'Fabrication, Logistique, Qualité' },
            { id: 'CC-004', code: 'IT', name: 'Informatique', description: 'Développement, Infrastructure, Support' },
            { id: 'CC-005', code: 'RD', name: 'Recherche & Développement', description: 'Innovation, Études, Prototypes' },
            { id: 'CC-006', code: 'FIN', name: 'Finance', description: 'Trésorerie, Contrôle de gestion, Audit' },
            { id: 'CC-007', code: 'LOG', name: 'Logistique', description: 'Achats, Stock, Transport' },
            { id: 'CC-008', code: 'MAINT', name: 'Maintenance', description: 'Entretien, Réparations, Sécurité' }
        ];

        // Tables de référence - Codes analytiques
        const analyticCodes = [
            { id: 'AN-001', code: 'PROJ-A', name: 'Projet Alpha', type: 'Projet', description: 'Développement nouveau produit' },
            { id: 'AN-002', code: 'PROJ-B', name: 'Projet Beta', type: 'Projet', description: 'Amélioration processus' },
            { id: 'AN-003', code: 'FORM-2024', name: 'Formation 2024', type: 'Programme', description: 'Plan de formation annuel' },
            { id: 'AN-004', code: 'DIGIT', name: 'Digitalisation', type: 'Transformation', description: 'Transformation numérique' },
            { id: 'AN-005', code: 'QUAL', name: 'Qualité', type: 'Processus', description: 'Amélioration qualité' },
            { id: 'AN-006', code: 'ECO', name: 'Écologie', type: 'RSE', description: 'Initiatives environnementales' },
            { id: 'AN-007', code: 'INNOV', name: 'Innovation', type: 'R&D', description: 'Recherche et innovation' },
            { id: 'AN-008', code: 'CLIENT', name: 'Satisfaction Client', type: 'Service', description: 'Amélioration service client' },
            { id: 'AN-009', code: 'SECU', name: 'Sécurité', type: 'Sécurité', description: 'Sécurité informatique et physique' },
            { id: 'AN-010', code: 'EXPORT', name: 'Export International', type: 'Commercial', description: 'Développement international' }
        ];

        // Données de démonstration réalistes - Budgets par catégorie avec données mensuelles
        const demoBudgetCategories = [
            {
                id: 'BUD-001',
                category: 'sales_revenue',
                categoryName: 'Ventes',
                categoryType: 'revenue',
                period: '2024',
                forecast: 850000.00,
                realized: 245000.00,
                department: 'Sales',
                responsible: 'Marie Dubois',
                costCenter: 'CC-002',
                costCenterName: 'Commercial',
                analyticCode: 'AN-010',
                analyticCodeName: 'Export International',
                notes: 'Objectif de croissance 15% par rapport à 2023',
                createdDate: '2024-01-15T09:00:00',
                modifiedDate: '2024-01-25T14:30:00',
                monthlyData: {
                    'janvier': { forecast: 70833, realized: 65000 },
                    'février': { forecast: 70833, realized: 72000 },
                    'mars': { forecast: 70833, realized: 68000 },
                    'avril': { forecast: 70833, realized: 40000 },
                    'mai': { forecast: 70833, realized: 0 },
                    'juin': { forecast: 70833, realized: 0 },
                    'juillet': { forecast: 70833, realized: 0 },
                    'août': { forecast: 70833, realized: 0 },
                    'septembre': { forecast: 70833, realized: 0 },
                    'octobre': { forecast: 70833, realized: 0 },
                    'novembre': { forecast: 70833, realized: 0 },
                    'décembre': { forecast: 70833, realized: 0 }
                }
            },
            {
                id: 'BUD-002',
                category: 'services_revenue',
                categoryName: 'Services',
                categoryType: 'revenue',
                period: '2024',
                forecast: 320000.00,
                realized: 89000.00,
                department: 'Sales',
                responsible: 'Pierre Martin',
                costCenter: 'CC-002',
                costCenterName: 'Commercial',
                analyticCode: 'AN-008',
                analyticCodeName: 'Satisfaction Client',
                notes: 'Services de conseil et maintenance',
                createdDate: '2024-01-15T09:15:00',
                modifiedDate: '2024-01-20T11:45:00',
                monthlyData: {
                    'janvier': { forecast: 26667, realized: 28000 },
                    'février': { forecast: 26667, realized: 25000 },
                    'mars': { forecast: 26667, realized: 22000 },
                    'avril': { forecast: 26667, realized: 14000 },
                    'mai': { forecast: 26667, realized: 0 },
                    'juin': { forecast: 26667, realized: 0 },
                    'juillet': { forecast: 26667, realized: 0 },
                    'août': { forecast: 26667, realized: 0 },
                    'septembre': { forecast: 26667, realized: 0 },
                    'octobre': { forecast: 26667, realized: 0 },
                    'novembre': { forecast: 26667, realized: 0 },
                    'décembre': { forecast: 26667, realized: 0 }
                }
            },
            {
                id: 'BUD-003',
                category: 'salaries_expense',
                categoryName: 'Salaires',
                categoryType: 'expense',
                period: '2024',
                forecast: 480000.00,
                realized: 125000.00,
                department: 'HR',
                responsible: 'Sophie Leroy',
                notes: 'Salaires bruts + charges patronales',
                createdDate: '2024-01-10T08:30:00',
                modifiedDate: '2024-01-30T16:20:00'
            },
            {
                id: 'BUD-004',
                category: 'rent_expense',
                categoryName: 'Loyers',
                categoryType: 'expense',
                period: '2024',
                forecast: 84000.00,
                realized: 21000.00,
                department: 'General',
                responsible: 'Jean Dupont',
                notes: 'Loyer bureaux + charges locatives',
                createdDate: '2024-01-05T10:00:00',
                modifiedDate: '2024-01-05T10:00:00'
            },
            {
                id: 'BUD-005',
                category: 'marketing_expense',
                categoryName: 'Marketing',
                categoryType: 'expense',
                period: '2024',
                forecast: 65000.00,
                realized: 18500.00,
                department: 'Marketing',
                responsible: 'Claire Moreau',
                notes: 'Publicité digitale et événements',
                createdDate: '2024-01-12T14:00:00',
                modifiedDate: '2024-01-28T09:15:00'
            },
            {
                id: 'BUD-006',
                category: 'equipment_investment',
                categoryName: 'Équipements',
                categoryType: 'investment',
                period: '2024',
                forecast: 120000.00,
                realized: 35000.00,
                department: 'IT',
                responsible: 'Thomas Bernard',
                notes: 'Renouvellement parc informatique',
                createdDate: '2024-01-08T11:30:00',
                modifiedDate: '2024-01-22T15:45:00'
            },
            {
                id: 'BUD-007',
                category: 'utilities_expense',
                categoryName: 'Utilities',
                categoryType: 'expense',
                period: '2024',
                forecast: 36000.00,
                realized: 9200.00,
                department: 'General',
                responsible: 'Jean Dupont',
                notes: 'Électricité, eau, gaz, internet',
                createdDate: '2024-01-05T10:15:00',
                modifiedDate: '2024-01-15T14:30:00'
            },
            {
                id: 'BUD-008',
                category: 'supplies_expense',
                categoryName: 'Fournitures',
                categoryType: 'expense',
                period: '2024',
                forecast: 24000.00,
                realized: 6800.00,
                department: 'General',
                responsible: 'Anne Petit',
                notes: 'Fournitures bureau et consommables',
                createdDate: '2024-01-10T09:45:00',
                modifiedDate: '2024-01-25T11:20:00'
            },
            {
                id: 'BUD-009',
                category: 'interest_expense',
                categoryName: 'Intérêts',
                categoryType: 'financial',
                period: '2024',
                forecast: 18000.00,
                realized: 4500.00,
                department: 'Finance',
                responsible: 'Michel Rousseau',
                notes: 'Intérêts emprunts bancaires',
                createdDate: '2024-01-03T16:00:00',
                modifiedDate: '2024-01-20T10:30:00'
            },
            {
                id: 'BUD-010',
                category: 'training_investment',
                categoryName: 'Formation',
                categoryType: 'investment',
                period: '2024',
                forecast: 45000.00,
                realized: 12000.00,
                department: 'HR',
                responsible: 'Sophie Leroy',
                notes: 'Formation continue des équipes',
                createdDate: '2024-01-12T13:15:00',
                modifiedDate: '2024-01-26T16:45:00'
            },
            {
                id: 'BUD-011',
                category: 'insurance_expense',
                categoryName: 'Assurances',
                categoryType: 'financial',
                period: '2024',
                forecast: 28000.00,
                realized: 7000.00,
                department: 'Finance',
                responsible: 'Michel Rousseau',
                notes: 'Assurances professionnelles et RC',
                createdDate: '2024-01-05T14:20:00',
                modifiedDate: '2024-01-15T09:10:00'
            },
            {
                id: 'BUD-012',
                category: 'taxes_expense',
                categoryName: 'Impôts',
                categoryType: 'exceptional',
                period: '2024',
                forecast: 95000.00,
                realized: 23000.00,
                department: 'Finance',
                responsible: 'Michel Rousseau',
                notes: 'IS, CFE, taxes diverses',
                createdDate: '2024-01-02T08:00:00',
                modifiedDate: '2024-01-30T17:30:00'
            }
        ];

        // Fonctions utilitaires
        function showAlert(message, type = 'error', container = 'alertContainer') {
            const alertContainer = document.getElementById(container);
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // ===== ÉDITION INLINE COMPLÈTE - PHASE 2 =====

        // Variables pour l'édition inline complète
        let currentEditingCell = null;
        let activeCellPosition = { row: -1, col: -1 };
        let isNavigatingWithKeyboard = false;

        // Démarrer l'édition inline d'une cellule
        function startInlineEdit(cell, budgetId, field, rowIndex, colIndex) {
            // Sauvegarder l'édition précédente
            if (currentEditingCell && currentEditingCell !== cell) {
                saveInlineEdit();
            }

            // Mettre à jour la position active
            activeCellPosition = { row: rowIndex, col: colIndex };

            // Marquer la cellule comme active
            clearActiveCells();
            cell.classList.add('editing');

            // Créer l'éditeur spécialisé
            createInlineEditor(cell, budgetId, field);

            currentEditingCell = cell;
        }

        // Créer un éditeur inline spécialisé selon le type de champ
        function createInlineEditor(cell, budgetId, field) {
            const budget = budgetCategories.find(b => b.id === budgetId);
            if (!budget) return;

            let editor;
            const currentValue = getCurrentFieldValue(budget, field);

            // Déterminer le type d'éditeur selon le champ
            if (field === 'categoryName') {
                editor = createTextEditor(currentValue);
            } else if (field === 'costCenter') {
                editor = createSelectEditor(currentValue, getCostCenterOptions());
            } else if (field === 'analyticCode') {
                editor = createSelectEditor(currentValue, getAnalyticCodeOptions());
            } else if (field === 'department') {
                editor = createSelectEditor(currentValue, getDepartmentOptions());
            } else if (field === 'responsible') {
                editor = createTextEditor(currentValue);
            } else if (field.includes('forecast') || field.includes('realized')) {
                editor = createNumberEditor(currentValue);
            } else if (field === 'notes') {
                editor = createTextareaEditor(currentValue);
            } else {
                editor = createTextEditor(currentValue);
            }

            // Ajouter l'éditeur à la cellule
            cell.innerHTML = '';
            cell.appendChild(editor);

            // Focus et sélection
            editor.focus();
            if (editor.select) editor.select();

            // Gestionnaires d'événements
            setupEditorEvents(editor, cell, budgetId, field);
        }

        // Créer un éditeur de texte
        function createTextEditor(value) {
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'cell-editor';
            input.value = value || '';
            return input;
        }

        // Créer un éditeur numérique
        function createNumberEditor(value) {
            const input = document.createElement('input');
            input.type = 'number';
            input.className = 'cell-editor number-editor';
            input.value = value || 0;
            input.step = '0.01';
            input.min = '0';
            return input;
        }

        // Créer un éditeur de zone de texte
        function createTextareaEditor(value) {
            const textarea = document.createElement('textarea');
            textarea.className = 'cell-editor textarea-editor';
            textarea.value = value || '';
            return textarea;
        }

        // Créer un éditeur de liste déroulante
        function createSelectEditor(value, options) {
            const select = document.createElement('select');
            select.className = 'cell-editor select-editor';

            // Option vide
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '-- Sélectionner --';
            select.appendChild(emptyOption);

            // Ajouter les options
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                if (option.value === value) {
                    optionElement.selected = true;
                }
                select.appendChild(optionElement);
            });

            return select;
        }

        // Obtenir la valeur actuelle d'un champ
        function getCurrentFieldValue(budget, field) {
            if (field === 'categoryName') return budget.categoryName || '';
            if (field === 'costCenter') return budget.costCenter || '';
            if (field === 'analyticCode') return budget.analyticCode || '';
            if (field === 'department') return budget.department || '';
            if (field === 'responsible') return budget.responsible || '';
            if (field === 'notes') return budget.notes || '';

            // Gérer les champs mensuels
            if (field.includes('_')) {
                const [monthIndex, type] = field.split('_');
                const monthKey = getMonthKey(parseInt(monthIndex));
                if (budget.monthlyData && budget.monthlyData[monthKey]) {
                    return budget.monthlyData[monthKey][type] || 0;
                }
            }

            return '';
        }

        // Obtenir les options pour les centres de coûts
        function getCostCenterOptions() {
            return costCenters.map(center => ({
                value: center.id,
                text: `${center.code} - ${center.name}`
            }));
        }

        // Obtenir les options pour les codes analytiques
        function getAnalyticCodeOptions() {
            return analyticCodes.map(code => ({
                value: code.id,
                text: `${code.code} - ${code.name}`
            }));
        }

        // Obtenir les options pour les départements
        function getDepartmentOptions() {
            return [
                { value: 'IT', text: 'Informatique' },
                { value: 'HR', text: 'Ressources Humaines' },
                { value: 'Sales', text: 'Commercial' },
                { value: 'Marketing', text: 'Marketing' },
                { value: 'Operations', text: 'Opérations' },
                { value: 'Finance', text: 'Finance' },
                { value: 'General', text: 'Général' }
            ];
        }

        // Configurer les événements de l'éditeur
        function setupEditorEvents(editor, cell, budgetId, field) {
            // Gestionnaire de validation temps réel
            editor.addEventListener('input', () => {
                validateFieldValue(editor, field);
            });

            // Gestionnaire de touches
            editor.addEventListener('keydown', (e) => {
                handleEditorKeydown(e, editor, cell, budgetId, field);
            });

            // Gestionnaire de perte de focus
            editor.addEventListener('blur', () => {
                setTimeout(() => {
                    if (currentEditingCell === cell) {
                        saveInlineEdit();
                    }
                }, 100);
            });

            // Gestionnaire de changement pour les selects
            if (editor.tagName === 'SELECT') {
                editor.addEventListener('change', () => {
                    saveInlineEdit();
                });
            }
        }

        // Gérer les touches dans l'éditeur
        function handleEditorKeydown(e, editor, cell, budgetId, field) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    saveInlineEdit();
                    if (e.shiftKey) {
                        navigateToCell('up');
                    } else {
                        navigateToCell('down');
                    }
                    break;

                case 'Tab':
                    e.preventDefault();
                    saveInlineEdit();
                    if (e.shiftKey) {
                        navigateToCell('left');
                    } else {
                        navigateToCell('right');
                    }
                    break;

                case 'Escape':
                    e.preventDefault();
                    cancelInlineEdit();
                    break;

                case 'F2':
                    e.preventDefault();
                    // Déjà en édition, ne rien faire
                    break;
            }
        }

        // Valider la valeur d'un champ en temps réel
        function validateFieldValue(editor, field) {
            const value = editor.value;
            let isValid = true;
            let message = '';
            let type = 'success';

            // Validation selon le type de champ
            if (field.includes('forecast') || field.includes('realized')) {
                const numValue = parseFloat(value);
                if (isNaN(numValue) || numValue < 0) {
                    isValid = false;
                    message = 'Veuillez saisir un montant valide (≥ 0)';
                    type = 'error';
                } else if (numValue > 1000000) {
                    isValid = true;
                    message = 'Montant élevé - Vérifiez la saisie';
                    type = 'warning';
                }
            } else if (field === 'categoryName') {
                if (!value.trim()) {
                    isValid = false;
                    message = 'Le nom de catégorie est obligatoire';
                    type = 'error';
                } else if (value.length < 3) {
                    isValid = true;
                    message = 'Nom très court - Recommandé: 3+ caractères';
                    type = 'warning';
                }
            } else if (field === 'responsible') {
                if (value.length > 0 && value.length < 2) {
                    isValid = true;
                    message = 'Nom très court';
                    type = 'warning';
                }
            }

            // Appliquer les styles de validation
            editor.classList.remove('valid', 'invalid', 'warning');
            if (isValid) {
                editor.classList.add(type === 'warning' ? 'warning' : 'valid');
            } else {
                editor.classList.add('invalid');
            }

            // Afficher/masquer le message de validation
            showValidationMessage(editor, message, type);

            return isValid;
        }

        // Afficher un message de validation
        function showValidationMessage(editor, message, type) {
            // Supprimer le message existant
            const existingMessage = editor.parentNode.querySelector('.validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // Afficher le nouveau message si nécessaire
            if (message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `validation-message ${type}`;
                messageDiv.textContent = message;
                editor.parentNode.appendChild(messageDiv);

                // Supprimer automatiquement après 3 secondes
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 3000);
            }
        }

        // Sauvegarder l'édition inline
        function saveInlineEdit() {
            if (!currentEditingCell) return;

            const editor = currentEditingCell.querySelector('.cell-editor');
            if (!editor) return;

            // Valider avant de sauvegarder
            const field = currentEditingCell.dataset.field || '';
            if (!validateFieldValue(editor, field)) {
                // Ne pas sauvegarder si la validation échoue
                return;
            }

            const budgetId = currentEditingCell.dataset.budgetId;
            const newValue = editor.value;

            // Sauvegarder dans la base de données
            updateBudgetField(budgetId, field, newValue);

            // Nettoyer l'édition
            cleanupInlineEdit();

            // Actualiser l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();

            // Auto-save
            triggerAutoSave();
        }

        // Annuler l'édition inline
        function cancelInlineEdit() {
            if (!currentEditingCell) return;
            cleanupInlineEdit();
            renderBudgetCategoriesTable();
        }

        // Nettoyer l'édition inline
        function cleanupInlineEdit() {
            if (currentEditingCell) {
                currentEditingCell.classList.remove('editing');
                currentEditingCell = null;
            }
            clearActiveCells();
        }

        // Effacer toutes les cellules actives
        function clearActiveCells() {
            document.querySelectorAll('.editable-cell.editing, .editable-cell.active').forEach(cell => {
                cell.classList.remove('editing', 'active');
            });
        }

        // Navigation entre cellules avec le clavier
        function navigateToCell(direction) {
            const table = document.querySelector('#budgetCategoriesTable tbody');
            if (!table) return;

            const rows = table.querySelectorAll('tr:not(.new-row)');
            const currentRow = activeCellPosition.row;
            const currentCol = activeCellPosition.col;

            let newRow = currentRow;
            let newCol = currentCol;

            switch (direction) {
                case 'up':
                    newRow = Math.max(0, currentRow - 1);
                    break;
                case 'down':
                    newRow = Math.min(rows.length - 1, currentRow + 1);
                    break;
                case 'left':
                    newCol = Math.max(2, currentCol - 1); // Commencer après les colonnes fixes
                    break;
                case 'right':
                    newCol = Math.min(30, currentCol + 1); // Maximum 31 colonnes
                    break;
            }

            // Trouver la cellule cible
            if (rows[newRow]) {
                const cells = rows[newRow].querySelectorAll('.editable-cell');
                const targetCell = cells[newCol - 2]; // Ajuster pour les colonnes fixes

                if (targetCell) {
                    const budgetId = targetCell.dataset.budgetId;
                    const field = targetCell.dataset.field;
                    startInlineEdit(targetCell, budgetId, field, newRow, newCol);
                }
            }
        }

        // Mettre à jour un champ de budget
        function updateBudgetField(budgetId, field, newValue) {
            const budget = budgetCategories.find(b => b.id === budgetId);
            if (!budget) return;

            // Sauvegarder l'ancienne valeur pour l'historique
            const oldBudget = JSON.parse(JSON.stringify(budget));

            // Appliquer la nouvelle valeur selon le type de champ
            if (field === 'categoryName') {
                budget.categoryName = newValue;
            } else if (field === 'costCenter') {
                budget.costCenter = newValue;
                const center = costCenters.find(c => c.id === newValue);
                budget.costCenterName = center ? center.name : '';
            } else if (field === 'analyticCode') {
                budget.analyticCode = newValue;
                const code = analyticCodes.find(c => c.id === newValue);
                budget.analyticCodeName = code ? code.name : '';
            } else if (field === 'department') {
                budget.department = newValue;
            } else if (field === 'responsible') {
                budget.responsible = newValue;
            } else if (field === 'notes') {
                budget.notes = newValue;
            } else if (field.includes('_')) {
                // Gérer les champs mensuels (format: "0_forecast" ou "0_realized")
                const [monthIndex, type] = field.split('_');
                const monthKey = getMonthKey(parseInt(monthIndex));

                if (!budget.monthlyData) {
                    budget.monthlyData = generateEmptyMonthlyData();
                }

                if (!budget.monthlyData[monthKey]) {
                    budget.monthlyData[monthKey] = { forecast: 0, realized: 0 };
                }

                budget.monthlyData[monthKey][type] = parseFloat(newValue) || 0;

                // Recalculer les totaux
                budget.forecast = Object.values(budget.monthlyData).reduce((sum, month) => sum + (month.forecast || 0), 0);
                budget.realized = Object.values(budget.monthlyData).reduce((sum, month) => sum + (month.realized || 0), 0);
            }

            // Mettre à jour la date de modification
            budget.modifiedDate = new Date().toISOString();

            // Sauvegarder dans l'historique
            saveToHistory('edit', {
                id: budget.id,
                oldValue: oldBudget,
                newValue: JSON.parse(JSON.stringify(budget))
            });

            // Sauvegarder en base de données (simulation)
            saveBudgetToDatabase(budget);
        }

        // Obtenir la clé du mois selon l'index
        function getMonthKey(monthIndex) {
            const months = [
                'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
            ];
            return months[monthIndex] || 'janvier';
        }

        // Sauvegarder un budget en base de données PostgreSQL
        async function saveBudgetToDatabase(budget) {
            try {
                showDatabaseSaveIndicator('saving');

                // Déterminer si c'est une création ou une mise à jour
                const existingBudget = budgetCategories.find(b => b.id === budget.id);
                const isUpdate = !!existingBudget;

                let response;
                if (isUpdate) {
                    // Mise à jour
                    response = await fetch(`http://localhost:5000/api/budgets/${budget.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(budget)
                    });
                } else {
                    // Création
                    response = await fetch('http://localhost:5000/api/budgets', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(budget)
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        console.log(`✅ Budget ${isUpdate ? 'mis à jour' : 'créé'} en PostgreSQL:`, budget.id);
                        showDatabaseSaveIndicator('success');

                        // Mettre à jour les données locales
                        if (isUpdate) {
                            const index = budgetCategories.findIndex(b => b.id === budget.id);
                            if (index !== -1) {
                                budgetCategories[index] = budget;
                            }
                        } else {
                            budgetCategories.push(budget);
                        }
                    } else {
                        throw new Error(result.error || 'Erreur lors de la sauvegarde');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

            } catch (error) {
                console.error('❌ Erreur lors de la sauvegarde PostgreSQL:', error);
                showDatabaseSaveIndicator('error');

                // Fallback vers localStorage
                try {
                    const existingData = JSON.parse(localStorage.getItem('erp_budget_data') || '[]');
                    const index = existingData.findIndex(b => b.id === budget.id);

                    if (index !== -1) {
                        existingData[index] = budget;
                    } else {
                        existingData.push(budget);
                    }

                    localStorage.setItem('erp_budget_data', JSON.stringify(existingData));
                    console.log('💾 Sauvegarde en localStorage comme fallback');

                } catch (localError) {
                    console.error('❌ Erreur fallback localStorage:', localError);
                }
            }
        }

        // Supprimer un budget de la base de données PostgreSQL
        async function deleteBudgetFromDatabase(budgetId) {
            try {
                showDatabaseSaveIndicator('saving');

                // Supprimer via l'API PostgreSQL
                const response = await fetch(`http://localhost:5000/api/budgets/${budgetId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        console.log(`✅ Budget supprimé de PostgreSQL: ${budgetId}`);
                        showDatabaseSaveIndicator('success');

                        // Supprimer des données locales
                        const index = budgetCategories.findIndex(b => b.id === budgetId);
                        if (index !== -1) {
                            budgetCategories.splice(index, 1);
                        }
                    } else {
                        throw new Error(result.error || 'Erreur lors de la suppression');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

            } catch (error) {
                console.error('❌ Erreur lors de la suppression PostgreSQL:', error);
                showDatabaseSaveIndicator('error');

                // Fallback vers localStorage
                try {
                    const existingData = JSON.parse(localStorage.getItem('erp_budget_data') || '[]');
                    const filteredData = existingData.filter(b => b.id !== budgetId);
                    localStorage.setItem('erp_budget_data', JSON.stringify(filteredData));
                    console.log('💾 Suppression en localStorage comme fallback');

                } catch (localError) {
                    console.error('❌ Erreur fallback localStorage:', localError);
                }
            }
        }

        // Afficher un indicateur de sauvegarde base de données
        function showDatabaseSaveIndicator(status) {
            const saveIndicator = document.getElementById('saveIndicator');
            const savingIndicator = document.getElementById('savingIndicator');

            // Masquer tous les indicateurs
            if (saveIndicator) saveIndicator.style.display = 'none';
            if (savingIndicator) savingIndicator.style.display = 'none';

            if (status === 'success') {
                if (saveIndicator) {
                    saveIndicator.style.display = 'flex';
                    setTimeout(() => {
                        saveIndicator.style.display = 'none';
                    }, 3000);
                }
            } else if (status === 'saving') {
                if (savingIndicator) {
                    savingIndicator.style.display = 'flex';
                }
            } else if (status === 'error') {
                // Afficher une alerte d'erreur
                showAlert('Erreur lors de la sauvegarde en base de données', 'error');
            }
        }

        function loadCostCentersInSelect() {
            const select = document.getElementById('budgetCostCenter');
            select.innerHTML = '<option value="">Sélectionner un centre de coûts</option>';

            costCenters.forEach(center => {
                const option = document.createElement('option');
                option.value = center.id;
                option.textContent = `${center.code} - ${center.name}`;
                select.appendChild(option);
            });
        }

        function loadAnalyticCodesInSelect() {
            const select = document.getElementById('budgetAnalyticCode');
            select.innerHTML = '<option value="">Sélectionner un code analytique</option>';

            analyticCodes.forEach(code => {
                const option = document.createElement('option');
                option.value = code.id;
                option.textContent = `${code.code} - ${code.name}`;
                select.appendChild(option);
            });
        }

        function loadFiltersInSelects() {
            loadFilterItems();
        }

        // Charger les éléments de filtre avec cases à cocher
        function loadFilterItems() {
            // Charger les centres de coûts
            const costCenterItems = document.getElementById('costCenterItems');
            costCenterItems.innerHTML = '';
            costCenters.forEach(center => {
                const item = document.createElement('label');
                item.className = 'filter-item';
                item.innerHTML = `
                    <input type="checkbox" value="${center.id}" checked onchange="updateFilterCount('costCenter')">
                    <span class="checkmark"></span>
                    <span class="filter-text">${center.code} - ${center.name}</span>
                `;
                costCenterItems.appendChild(item);
            });

            // Charger les codes analytiques
            const analyticCodeItems = document.getElementById('analyticCodeItems');
            analyticCodeItems.innerHTML = '';
            analyticCodes.forEach(code => {
                const item = document.createElement('label');
                item.className = 'filter-item';
                item.innerHTML = `
                    <input type="checkbox" value="${code.id}" checked onchange="updateFilterCount('analyticCode')">
                    <span class="checkmark"></span>
                    <span class="filter-text">${code.code} - ${code.name}</span>
                `;
                analyticCodeItems.appendChild(item);
            });

            updateFilterCounts();
        }

        // Basculer l'affichage d'un panneau de filtre
        function toggleFilterPanel(panelId) {
            const panel = document.getElementById(panelId);
            const arrow = panel.previousElementSibling.querySelector('.filter-arrow');

            if (panel.classList.contains('collapsed')) {
                panel.classList.remove('collapsed');
                arrow.classList.remove('rotated');
            } else {
                panel.classList.add('collapsed');
                arrow.classList.add('rotated');
            }
        }

        // Filtrer les éléments lors de la recherche
        function filterSearchItems(filterType) {
            const searchInput = document.getElementById(filterType + 'Search');
            const items = document.getElementById(filterType + 'Items');
            const searchTerm = searchInput.value.toLowerCase();

            Array.from(items.children).forEach(item => {
                const text = item.querySelector('.filter-text').textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Mettre à jour le compteur d'un filtre
        function updateFilterCount(filterType) {
            const items = document.getElementById(filterType + 'Items');
            const checkboxes = items.querySelectorAll('input[type="checkbox"]');
            const checkedCount = items.querySelectorAll('input[type="checkbox"]:checked').length;
            const totalCount = checkboxes.length;

            // Mettre à jour le titre du panneau avec le compteur
            const header = items.closest('.filter-panel').querySelector('.filter-header span:nth-child(2)');
            const baseName = header.textContent.split(' (')[0];
            header.textContent = `${baseName} (${checkedCount}/${totalCount})`;
        }

        // Mettre à jour tous les compteurs
        function updateFilterCounts() {
            updateFilterCount('costCenter');
            updateFilterCount('analyticCode');
            updateFilterCount('budgetType');
        }

        // Sélectionner tous les filtres
        function selectAllFilters() {
            const checkboxes = document.querySelectorAll('.filter-items input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateFilterCounts();
        }

        // Désélectionner tous les filtres
        function clearAllFilters() {
            const checkboxes = document.querySelectorAll('.filter-items input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateFilterCounts();
        }

        // Appliquer les filtres sélectionnés
        function applyFilters() {
            updateBudgetView();
            showAlert('Filtres appliqués avec succès', 'success');
        }

        // Fonctions pour les filtres d'analyse
        function loadAnalysisFilterItems() {
            // Charger les centres de coûts pour l'analyse
            const analysisCostCenterItems = document.getElementById('analysisCostCenterItems');
            if (analysisCostCenterItems) {
                analysisCostCenterItems.innerHTML = '';
                costCenters.forEach(center => {
                    const item = document.createElement('label');
                    item.className = 'filter-item';
                    item.innerHTML = `
                        <input type="checkbox" value="${center.id}" checked onchange="updateFilterCount('analysisCostCenter')">
                        <span class="checkmark"></span>
                        <span class="filter-text">${center.code} - ${center.name}</span>
                    `;
                    analysisCostCenterItems.appendChild(item);
                });
            }

            // Charger les codes analytiques pour l'analyse
            const analysisAnalyticCodeItems = document.getElementById('analysisAnalyticCodeItems');
            if (analysisAnalyticCodeItems) {
                analysisAnalyticCodeItems.innerHTML = '';
                analyticCodes.forEach(code => {
                    const item = document.createElement('label');
                    item.className = 'filter-item';
                    item.innerHTML = `
                        <input type="checkbox" value="${code.id}" checked onchange="updateFilterCount('analysisAnalyticCode')">
                        <span class="checkmark"></span>
                        <span class="filter-text">${code.code} - ${code.name}</span>
                    `;
                    analysisAnalyticCodeItems.appendChild(item);
                });
            }

            updateAnalysisFilterCounts();
        }

        function updateAnalysisFilterCounts() {
            updateFilterCount('analysisCostCenter');
            updateFilterCount('analysisAnalyticCode');
            updateFilterCount('analysisBudgetType');
        }

        function copyFiltersFromBudget() {
            // Copier les sélections des filtres budget vers les filtres analyse
            const budgetCostCenters = getSelectedFilterValues('costCenter');
            const budgetAnalyticCodes = getSelectedFilterValues('analyticCode');
            const budgetTypes = getSelectedFilterValues('budgetType');

            // Appliquer aux filtres d'analyse
            setAnalysisFilterValues('analysisCostCenter', budgetCostCenters);
            setAnalysisFilterValues('analysisAnalyticCode', budgetAnalyticCodes);
            setAnalysisFilterValues('analysisBudgetType', budgetTypes);

            updateAnalysisFilterCounts();
            showAlert('Filtres copiés depuis la gestion budgétaire', 'success');
        }

        function setAnalysisFilterValues(filterType, selectedValues) {
            const items = document.getElementById(filterType + 'Items');
            if (items) {
                const checkboxes = items.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectedValues.includes(checkbox.value);
                });
            }
        }

        function clearAnalysisFilters() {
            const analysisCheckboxes = document.querySelectorAll('#analysis .filter-items input[type="checkbox"]');
            analysisCheckboxes.forEach(checkbox => {
                checkbox.checked = true; // Tout sélectionner par défaut
            });
            updateAnalysisFilterCounts();
            showAlert('Filtres d\'analyse réinitialisés', 'success');
        }

        function applyAnalysisFilters() {
            updatePivotTable();
            showAlert('Filtres appliqués au TCD', 'success');
        }

        // ===== FONCTIONS INTERFACE TABLEUR =====

        // Ouvrir la modal d'import
        function openImportModal() {
            currentImportStep = 1;
            document.getElementById('importModal').style.display = 'block';
            resetImportSteps();
        }

        // Réinitialiser les étapes d'import
        function resetImportSteps() {
            document.getElementById('importStep1').style.display = 'block';
            document.getElementById('importStep2').style.display = 'none';
            document.getElementById('importStep3').style.display = 'none';
            document.getElementById('prevStepBtn').style.display = 'none';
            document.getElementById('nextStepBtn').disabled = true;
            document.getElementById('importBtn').style.display = 'none';

            // Réinitialiser les données
            importData = null;
            columnMapping = {};
            validationResults = [];
        }

        // Gérer la sélection de fichier
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Afficher les informations du fichier
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = file.type || 'Inconnu';
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('nextStepBtn').disabled = false;

            // Lire le fichier selon son type
            const reader = new FileReader();

            if (file.name.endsWith('.csv')) {
                reader.onload = function(e) {
                    parseCSVData(e.target.result);
                };
                reader.readAsText(file, 'UTF-8');
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                reader.onload = function(e) {
                    parseExcelData(e.target.result);
                };
                reader.readAsArrayBuffer(file);
            }
        }

        // Formater la taille du fichier
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Parser les données CSV
        function parseCSVData(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            if (lines.length === 0) return;

            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            const rows = lines.slice(1).map(line => {
                return line.split(',').map(cell => cell.trim().replace(/"/g, ''));
            });

            importData = {
                headers: headers,
                rows: rows
            };

            showAlert('Fichier CSV analysé avec succès', 'success');
        }

        // Parser les données Excel (simulation - nécessiterait une vraie librairie comme SheetJS)
        function parseExcelData(arrayBuffer) {
            // Simulation du parsing Excel
            // Dans un vrai projet, utiliser SheetJS ou une librairie similaire

            // Données d'exemple pour la démonstration
            const simulatedData = {
                headers: ['Catégorie', 'Centre de Coûts', 'Code Analytique', 'Responsable', 'Jan Prév', 'Fév Prév', 'Mar Prév'],
                rows: [
                    ['Ventes', 'COM', 'EXPORT', 'Marie Dubois', '70000', '72000', '68000'],
                    ['Services', 'COM', 'CLIENT', 'Pierre Martin', '26000', '25000', '22000'],
                    ['Salaires', 'ADM', 'FORM-2024', 'Sophie Leroy', '40000', '40000', '40000']
                ]
            };

            importData = simulatedData;
            showAlert('Fichier Excel analysé avec succès (simulation)', 'success');
        }

        // Étape suivante de l'import
        function nextImportStep() {
            if (currentImportStep === 1) {
                if (!importData) {
                    showAlert('Veuillez sélectionner un fichier', 'error');
                    return;
                }
                showMappingStep();
            } else if (currentImportStep === 2) {
                if (validateMapping()) {
                    showPreviewStep();
                }
            }
        }

        // Étape précédente de l'import
        function previousImportStep() {
            if (currentImportStep === 2) {
                currentImportStep = 1;
                document.getElementById('importStep1').style.display = 'block';
                document.getElementById('importStep2').style.display = 'none';
                document.getElementById('prevStepBtn').style.display = 'none';
            } else if (currentImportStep === 3) {
                currentImportStep = 2;
                document.getElementById('importStep2').style.display = 'block';
                document.getElementById('importStep3').style.display = 'none';
                document.getElementById('importBtn').style.display = 'none';
                document.getElementById('nextStepBtn').style.display = 'inline-block';
            }
        }

        // Afficher l'étape de mapping
        function showMappingStep() {
            currentImportStep = 2;
            document.getElementById('importStep1').style.display = 'none';
            document.getElementById('importStep2').style.display = 'block';
            document.getElementById('prevStepBtn').style.display = 'inline-block';

            // Afficher les colonnes du fichier
            displayFileColumns();

            // Charger les options de mapping
            loadMappingOptions();
        }

        // Afficher les colonnes du fichier
        function displayFileColumns() {
            const container = document.getElementById('fileColumns');
            container.innerHTML = '';

            if (!importData || !importData.headers) return;

            importData.headers.forEach((header, index) => {
                const columnDiv = document.createElement('div');
                columnDiv.className = 'file-column';
                columnDiv.textContent = `${index + 1}. ${header}`;
                columnDiv.onclick = () => selectFileColumn(index, header);
                container.appendChild(columnDiv);
            });
        }

        // Charger les options de mapping
        function loadMappingOptions() {
            // Charger les colonnes du fichier dans les selects
            const selects = ['map_category', 'map_costCenter', 'map_analyticCode', 'map_responsible'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">-- Sélectionner --</option>';

                importData.headers.forEach((header, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = header;
                    select.appendChild(option);
                });
            });
        }

        // Mapping automatique des colonnes
        function autoMapColumns() {
            const mappings = {
                'map_category': ['catégorie', 'category', 'type', 'libellé'],
                'map_costCenter': ['centre', 'cost center', 'centre de coûts', 'cc'],
                'map_analyticCode': ['code', 'analytique', 'analytic', 'projet'],
                'map_responsible': ['responsable', 'responsible', 'manager', 'resp']
            };

            Object.keys(mappings).forEach(selectId => {
                const select = document.getElementById(selectId);
                const keywords = mappings[selectId];

                for (let i = 0; i < importData.headers.length; i++) {
                    const header = importData.headers[i].toLowerCase();
                    if (keywords.some(keyword => header.includes(keyword))) {
                        select.value = i;
                        break;
                    }
                }
            });

            showAlert('Mapping automatique appliqué', 'success');
        }

        // Valider le mapping
        function validateMapping() {
            const requiredMappings = ['map_category'];
            let isValid = true;

            requiredMappings.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (!select.value) {
                    showAlert('Le mapping de la catégorie est obligatoire', 'error');
                    isValid = false;
                }
            });

            if (isValid) {
                // Sauvegarder le mapping
                columnMapping = {
                    category: document.getElementById('map_category').value,
                    costCenter: document.getElementById('map_costCenter').value,
                    analyticCode: document.getElementById('map_analyticCode').value,
                    responsible: document.getElementById('map_responsible').value
                };
            }

            return isValid;
        }

        // Afficher l'étape de prévisualisation
        function showPreviewStep() {
            currentImportStep = 3;
            document.getElementById('importStep2').style.display = 'none';
            document.getElementById('importStep3').style.display = 'block';
            document.getElementById('nextStepBtn').style.display = 'none';
            document.getElementById('importBtn').style.display = 'inline-block';

            generatePreview();
            validateImportData();
        }

        // Générer la prévisualisation
        function generatePreview() {
            const table = document.getElementById('previewTable');
            const header = document.getElementById('previewHeader');
            const body = document.getElementById('previewBody');

            // Créer l'en-tête
            header.innerHTML = '';
            const headerRow = document.createElement('tr');
            ['Ligne', 'Catégorie', 'Centre de Coûts', 'Code Analytique', 'Responsable', 'Statut'].forEach(title => {
                const th = document.createElement('th');
                th.textContent = title;
                headerRow.appendChild(th);
            });
            header.appendChild(headerRow);

            // Créer le corps
            body.innerHTML = '';
            importData.rows.slice(0, 10).forEach((row, index) => { // Limiter à 10 lignes pour la prévisualisation
                const tr = document.createElement('tr');

                // Numéro de ligne
                const lineCell = document.createElement('td');
                lineCell.textContent = index + 1;
                tr.appendChild(lineCell);

                // Données mappées
                const mappedData = {
                    category: columnMapping.category !== '' ? row[columnMapping.category] : '',
                    costCenter: columnMapping.costCenter !== '' ? row[columnMapping.costCenter] : '',
                    analyticCode: columnMapping.analyticCode !== '' ? row[columnMapping.analyticCode] : '',
                    responsible: columnMapping.responsible !== '' ? row[columnMapping.responsible] : ''
                };

                Object.values(mappedData).forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value || '-';
                    tr.appendChild(td);
                });

                // Statut de validation
                const statusCell = document.createElement('td');
                const isValid = validateRowData(mappedData);
                statusCell.innerHTML = isValid ?
                    '<span style="color: #059669;">✓ Valide</span>' :
                    '<span style="color: #dc2626;">✗ Erreur</span>';
                tr.appendChild(statusCell);

                body.appendChild(tr);
            });
        }

        // Valider une ligne de données
        function validateRowData(data) {
            return data.category && data.category.trim() !== '';
        }

        // Valider toutes les données d'import
        function validateImportData() {
            const container = document.getElementById('validationResults');
            container.innerHTML = '';

            let validRows = 0;
            let errorRows = 0;
            const errors = [];

            importData.rows.forEach((row, index) => {
                const mappedData = {
                    category: columnMapping.category !== '' ? row[columnMapping.category] : '',
                    costCenter: columnMapping.costCenter !== '' ? row[columnMapping.costCenter] : '',
                    analyticCode: columnMapping.analyticCode !== '' ? row[columnMapping.analyticCode] : '',
                    responsible: columnMapping.responsible !== '' ? row[columnMapping.responsible] : ''
                };

                if (validateRowData(mappedData)) {
                    validRows++;
                } else {
                    errorRows++;
                    errors.push(`Ligne ${index + 1}: Catégorie manquante`);
                }
            });

            // Afficher le résumé
            const summary = document.createElement('div');
            summary.className = 'validation-success';
            summary.innerHTML = `
                <h5>Résumé de validation :</h5>
                <p>✓ Lignes valides : ${validRows}</p>
                <p>✗ Lignes avec erreurs : ${errorRows}</p>
                <p>📊 Total : ${importData.rows.length} lignes</p>
            `;
            container.appendChild(summary);

            // Afficher les erreurs
            if (errors.length > 0) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'validation-error';
                errorDiv.innerHTML = '<h5>Erreurs détectées :</h5>' + errors.slice(0, 5).map(error => `<p>${error}</p>`).join('');
                if (errors.length > 5) {
                    errorDiv.innerHTML += `<p>... et ${errors.length - 5} autres erreurs</p>`;
                }
                container.appendChild(errorDiv);
            }
        }

        // Exécuter l'import
        function executeImport() {
            if (!importData || !columnMapping) {
                showAlert('Données d\'import non valides', 'error');
                return;
            }

            let importedCount = 0;
            const newBudgets = [];

            importData.rows.forEach((row, index) => {
                const mappedData = {
                    category: columnMapping.category !== '' ? row[columnMapping.category] : '',
                    costCenter: columnMapping.costCenter !== '' ? row[columnMapping.costCenter] : '',
                    analyticCode: columnMapping.analyticCode !== '' ? row[columnMapping.analyticCode] : '',
                    responsible: columnMapping.responsible !== '' ? row[columnMapping.responsible] : ''
                };

                if (validateRowData(mappedData)) {
                    // Créer un nouveau budget
                    const newBudget = {
                        id: 'BUD-IMP-' + Date.now() + '-' + index,
                        category: mappedData.category.toLowerCase().replace(/\s+/g, '_'),
                        categoryName: mappedData.category,
                        categoryType: 'expense', // Par défaut
                        period: '2024',
                        forecast: 0,
                        realized: 0,
                        department: 'Import',
                        responsible: mappedData.responsible || 'Non défini',
                        costCenter: findCostCenterByName(mappedData.costCenter),
                        analyticCode: findAnalyticCodeByName(mappedData.analyticCode),
                        notes: 'Importé depuis Excel/CSV',
                        createdDate: new Date().toISOString(),
                        modifiedDate: new Date().toISOString(),
                        monthlyData: generateEmptyMonthlyData()
                    };

                    newBudgets.push(newBudget);
                    importedCount++;
                }
            });

            // Ajouter les nouveaux budgets
            budgetCategories.push(...newBudgets);

            // Fermer la modal et actualiser
            closeModal('importModal');
            renderBudgetCategoriesTable();
            updateBudgetStats();

            showAlert(`Import réussi : ${importedCount} lignes importées`, 'success');
        }

        // Fonctions utilitaires pour l'import
        function findCostCenterByName(name) {
            if (!name) return 'CC-001'; // Par défaut
            const found = costCenters.find(cc =>
                cc.name.toLowerCase().includes(name.toLowerCase()) ||
                cc.code.toLowerCase() === name.toLowerCase()
            );
            return found ? found.id : 'CC-001';
        }

        function findAnalyticCodeByName(name) {
            if (!name) return 'AN-001'; // Par défaut
            const found = analyticCodes.find(ac =>
                ac.name.toLowerCase().includes(name.toLowerCase()) ||
                ac.code.toLowerCase() === name.toLowerCase()
            );
            return found ? found.id : 'AN-001';
        }

        function generateEmptyMonthlyData() {
            const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                           'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
            const data = {};
            months.forEach(month => {
                data[month] = { forecast: 0, realized: 0 };
            });
            return data;
        }

        // ===== FONCTIONS ÉDITION INLINE ET TABLEUR =====

        // Ajouter une nouvelle ligne de budget
        function addNewBudgetRow() {
            const newBudget = {
                id: 'BUD-NEW-' + Date.now(),
                category: 'nouvelle_categorie',
                categoryName: 'Nouvelle Catégorie',
                categoryType: 'expense',
                period: '2024',
                forecast: 0,
                realized: 0,
                department: 'Nouveau',
                responsible: 'À définir',
                costCenter: 'CC-001',
                costCenterName: 'Administration Générale',
                analyticCode: 'AN-001',
                analyticCodeName: 'Projet Alpha',
                notes: 'Nouvelle ligne créée',
                createdDate: new Date().toISOString(),
                modifiedDate: new Date().toISOString(),
                monthlyData: generateEmptyMonthlyData()
            };

            // Ajouter à la liste et sauvegarder dans l'historique
            saveToHistory('add', newBudget);
            budgetCategories.push(newBudget);

            // Actualiser l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();

            // Auto-save
            triggerAutoSave();

            showAlert('Nouvelle ligne ajoutée', 'success');
        }

        // Sélectionner/désélectionner toutes les lignes
        function toggleSelectAllRows() {
            const selectAllCheckbox = document.getElementById('selectAllRows');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');

            selectedRows.clear();

            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    selectedRows.add(checkbox.value);
                }
            });

            updateSelectedRowsDisplay();
        }

        // Mettre à jour l'affichage des lignes sélectionnées
        function updateSelectedRowsDisplay() {
            const count = selectedRows.size;
            document.getElementById('selectedRowsCount').textContent = `${count} ligne(s) sélectionnée(s)`;

            // Afficher/masquer les boutons d'action en lot
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            const exportBtn = document.getElementById('exportSelectedBtn');

            if (count > 0) {
                deleteBtn.style.display = 'inline-block';
                exportBtn.style.display = 'inline-block';
            } else {
                deleteBtn.style.display = 'none';
                exportBtn.style.display = 'none';
            }
        }

        // Supprimer les lignes sélectionnées
        function deleteSelectedRows() {
            if (selectedRows.size === 0) {
                showAlert('Aucune ligne sélectionnée', 'error');
                return;
            }

            if (!confirm(`Êtes-vous sûr de vouloir supprimer ${selectedRows.size} ligne(s) ?`)) {
                return;
            }

            // Sauvegarder dans l'historique
            const deletedBudgets = budgetCategories.filter(budget => selectedRows.has(budget.id));
            saveToHistory('delete', deletedBudgets);

            // Supprimer les lignes
            budgetCategories = budgetCategories.filter(budget => !selectedRows.has(budget.id));

            // Réinitialiser la sélection
            selectedRows.clear();
            document.getElementById('selectAllRows').checked = false;

            // Actualiser l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();
            updateSelectedRowsDisplay();

            // Auto-save
            triggerAutoSave();

            showAlert(`${deletedBudgets.length} ligne(s) supprimée(s)`, 'success');
        }

        // Exporter les lignes sélectionnées
        function exportSelectedRows() {
            if (selectedRows.size === 0) {
                showAlert('Aucune ligne sélectionnée', 'error');
                return;
            }

            const selectedBudgets = budgetCategories.filter(budget => selectedRows.has(budget.id));

            // Créer le CSV des lignes sélectionnées
            const csvContent = generateCSVContent(selectedBudgets);
            downloadFile(csvContent, `budgets_selection_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');

            showAlert(`${selectedBudgets.length} ligne(s) exportée(s)`, 'success');
        }

        // ===== SYSTÈME D'HISTORIQUE =====

        // Sauvegarder une action dans l'historique
        function saveToHistory(action, data) {
            // Supprimer les actions après l'index actuel (si on a fait des undo)
            actionHistory = actionHistory.slice(0, historyIndex + 1);

            // Ajouter la nouvelle action
            actionHistory.push({
                action: action,
                data: JSON.parse(JSON.stringify(data)), // Deep copy
                timestamp: Date.now()
            });

            // Limiter la taille de l'historique
            if (actionHistory.length > maxHistorySize) {
                actionHistory.shift();
            } else {
                historyIndex++;
            }

            updateHistoryButtons();
        }

        // Annuler la dernière action (Ctrl+Z)
        function undoLastAction() {
            if (historyIndex < 0) {
                showAlert('Aucune action à annuler', 'error');
                return;
            }

            const lastAction = actionHistory[historyIndex];

            switch (lastAction.action) {
                case 'add':
                    // Supprimer l'élément ajouté
                    budgetCategories = budgetCategories.filter(budget => budget.id !== lastAction.data.id);
                    break;

                case 'delete':
                    // Restaurer les éléments supprimés
                    if (Array.isArray(lastAction.data)) {
                        budgetCategories.push(...lastAction.data);
                    } else {
                        budgetCategories.push(lastAction.data);
                    }
                    break;

                case 'edit':
                    // Restaurer l'ancienne valeur
                    const budgetIndex = budgetCategories.findIndex(b => b.id === lastAction.data.id);
                    if (budgetIndex !== -1) {
                        budgetCategories[budgetIndex] = lastAction.data.oldValue;
                    }
                    break;
            }

            historyIndex--;
            updateHistoryButtons();
            renderBudgetCategoriesTable();
            updateBudgetStats();
            triggerAutoSave();

            showAlert('Action annulée', 'success');
        }

        // Rétablir la dernière action annulée (Ctrl+Y)
        function redoLastAction() {
            if (historyIndex >= actionHistory.length - 1) {
                showAlert('Aucune action à rétablir', 'error');
                return;
            }

            historyIndex++;
            const actionToRedo = actionHistory[historyIndex];

            switch (actionToRedo.action) {
                case 'add':
                    // Ré-ajouter l'élément
                    budgetCategories.push(actionToRedo.data);
                    break;

                case 'delete':
                    // Re-supprimer les éléments
                    if (Array.isArray(actionToRedo.data)) {
                        const idsToDelete = actionToRedo.data.map(item => item.id);
                        budgetCategories = budgetCategories.filter(budget => !idsToDelete.includes(budget.id));
                    } else {
                        budgetCategories = budgetCategories.filter(budget => budget.id !== actionToRedo.data.id);
                    }
                    break;

                case 'edit':
                    // Ré-appliquer la modification
                    const budgetIndex = budgetCategories.findIndex(b => b.id === actionToRedo.data.id);
                    if (budgetIndex !== -1) {
                        budgetCategories[budgetIndex] = actionToRedo.data.newValue;
                    }
                    break;
            }

            updateHistoryButtons();
            renderBudgetCategoriesTable();
            updateBudgetStats();
            triggerAutoSave();

            showAlert('Action rétablie', 'success');
        }

        // Mettre à jour l'état des boutons d'historique
        function updateHistoryButtons() {
            const undoBtn = document.getElementById('undoBtn');
            const redoBtn = document.getElementById('redoBtn');

            undoBtn.disabled = historyIndex < 0;
            redoBtn.disabled = historyIndex >= actionHistory.length - 1;
        }

        // ===== AUTO-SAVE =====

        // Déclencher l'auto-save
        function triggerAutoSave() {
            // Annuler le timer précédent
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }

            // Afficher l'indicateur de sauvegarde
            showSavingIndicator();

            // Programmer la sauvegarde après 1 seconde
            autoSaveTimeout = setTimeout(() => {
                performAutoSave();
            }, 1000);
        }

        // Effectuer la sauvegarde automatique
        function performAutoSave() {
            // Simulation de sauvegarde (dans un vrai projet, envoyer au serveur)
            localStorage.setItem('erp_budget_data', JSON.stringify(budgetCategories));
            localStorage.setItem('erp_budget_history', JSON.stringify(actionHistory));

            // Afficher l'indicateur de succès
            showSavedIndicator();
        }

        // Afficher l'indicateur "Sauvegarde en cours"
        function showSavingIndicator() {
            document.getElementById('saveIndicator').style.display = 'none';
            document.getElementById('savingIndicator').style.display = 'flex';
        }

        // Afficher l'indicateur "Sauvegardé"
        function showSavedIndicator() {
            document.getElementById('savingIndicator').style.display = 'none';
            document.getElementById('saveIndicator').style.display = 'flex';

            // Masquer après 3 secondes
            setTimeout(() => {
                document.getElementById('saveIndicator').style.display = 'none';
            }, 3000);
        }

        // ===== GESTION RACCOURCIS CLAVIER GLOBAUX =====

        // Gestionnaire global des raccourcis clavier
        document.addEventListener('keydown', (e) => {
            // Ignorer si on est en train d'éditer une cellule
            if (currentEditingCell) return;

            // Ignorer si on est dans un champ de saisie
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                return;
            }

            switch (e.key) {
                case 'F2':
                    e.preventDefault();
                    // Entrer en mode édition sur la cellule sélectionnée
                    const activeCell = document.querySelector('.editable-cell.active');
                    if (activeCell) {
                        const budgetId = activeCell.dataset.budgetId;
                        const field = activeCell.dataset.field;
                        startInlineEdit(activeCell, budgetId, field, activeCellPosition.row, activeCellPosition.col);
                    }
                    break;

                case 'Delete':
                    e.preventDefault();
                    // Supprimer les lignes sélectionnées
                    if (selectedRows.size > 0) {
                        deleteSelectedRows();
                    }
                    break;

                case 'Escape':
                    e.preventDefault();
                    // Annuler l'édition ou désélectionner
                    if (currentEditingCell) {
                        cancelInlineEdit();
                    } else {
                        selectedRows.clear();
                        updateSelectedRowsDisplay();
                        renderBudgetCategoriesTable();
                    }
                    break;
            }
        });

        function loadAccountsInSelect() {
            const select = document.getElementById('movementAccount');
            select.innerHTML = '<option value="">Sélectionner un compte</option>';

            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = `${account.name} - ${account.bank}`;
                select.appendChild(option);
            });
        }

        // Calcul des statistiques financières
        function updateStats() {
            // Trésorerie totale
            const totalTreasury = accounts.reduce((sum, acc) => sum + acc.balance, 0);

            // Variation mensuelle (mouvements du mois en cours)
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyMovements = movements.filter(m => {
                const moveDate = new Date(m.date);
                return moveDate.getMonth() === currentMonth && moveDate.getFullYear() === currentYear;
            });

            const monthlyIncome = monthlyMovements
                .filter(m => m.type === 'income')
                .reduce((sum, m) => sum + m.amount, 0);
            const monthlyExpenses = monthlyMovements
                .filter(m => m.type === 'expense')
                .reduce((sum, m) => sum + m.amount, 0);
            const monthlyVariation = monthlyIncome - monthlyExpenses;

            // Budget disponible
            const totalBudget = budgets.reduce((sum, b) => sum + b.budgetAmount, 0);
            const totalSpent = budgets.reduce((sum, b) => sum + b.spentAmount, 0);
            const availableBudget = totalBudget - totalSpent;

            // ROI mensuel (simplifié)
            const monthlyROI = totalTreasury > 0 ? ((monthlyVariation / totalTreasury) * 100) : 0;

            // Mise à jour de l'affichage
            document.getElementById('totalTreasury').textContent = totalTreasury.toLocaleString() + '€';
            document.getElementById('monthlyVariation').textContent = monthlyVariation.toLocaleString() + '€';
            document.getElementById('availableBudget').textContent = availableBudget.toLocaleString() + '€';
            document.getElementById('monthlyROI').textContent = monthlyROI.toFixed(1) + '%';

            // Coloration des valeurs
            const variationElement = document.getElementById('monthlyVariation');
            variationElement.className = 'stat-value ' + (monthlyVariation >= 0 ? 'amount-positive' : 'amount-negative');

            const roiElement = document.getElementById('monthlyROI');
            roiElement.className = 'stat-value ' + (monthlyROI >= 0 ? 'amount-positive' : 'amount-negative');
        }

        // Déterminer le statut d'un compte selon son solde
        function getAccountStatus(account) {
            if (account.type === 'Crédit') {
                // Pour les comptes de crédit, la logique est inversée
                if (account.balance <= account.alertThreshold) {
                    return { class: 'balance-good', text: 'Normal', badge: 'badge-success' };
                } else {
                    return { class: 'balance-warning', text: 'Attention', badge: 'badge-warning' };
                }
            } else {
                // Pour les autres comptes
                if (account.balance <= account.alertThreshold) {
                    return { class: 'balance-critical', text: 'Critique', badge: 'badge-danger' };
                } else if (account.balance <= account.alertThreshold * 2) {
                    return { class: 'balance-warning', text: 'Attention', badge: 'badge-warning' };
                } else {
                    return { class: 'balance-good', text: 'Normal', badge: 'badge-success' };
                }
            }
        }

        // Rendu du tableau des comptes
        function renderAccountsTable() {
            const tbody = document.getElementById('accountsTableBody');

            if (accounts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun compte trouvé. Contactez l'administrateur.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = accounts.map(account => {
                const status = getAccountStatus(account);
                return `
                    <tr>
                        <td>${account.name}<br><small style="color: #6b7280;">${account.iban}</small></td>
                        <td>${account.bank}</td>
                        <td>${account.type}</td>
                        <td><span class="${status.class}">${account.balance.toLocaleString()}€</span></td>
                        <td>${account.alertThreshold.toLocaleString()}€</td>
                        <td><span class="badge ${status.badge}">${status.text}</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="viewAccountDetails('${account.id}')" title="Détails">
                                <span class="material-icons" style="font-size: 1rem;">visibility</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Rendu du tableau des mouvements
        function renderMovementsTable() {
            const tbody = document.getElementById('movementsTableBody');

            if (movements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun mouvement trouvé. Cliquez sur "Nouveau Mouvement" pour commencer.
                        </td>
                    </tr>
                `;
                return;
            }

            // Trier par date décroissante
            const sortedMovements = [...movements].sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = sortedMovements.map(movement => {
                const typeDisplay = getMovementTypeDisplay(movement.type);
                const amountClass = movement.type === 'income' ? 'amount-positive' : 'amount-negative';
                const amountSign = movement.type === 'income' ? '+' : '-';

                return `
                    <tr>
                        <td>${new Date(movement.date).toLocaleDateString()}</td>
                        <td>${movement.label}</td>
                        <td>${movement.accountName}</td>
                        <td>${typeDisplay}</td>
                        <td><span class="${amountClass}">${amountSign}${movement.amount.toLocaleString()}€</span></td>
                        <td>${movement.reference || '-'}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 0.25rem 0.5rem; margin-right: 0.5rem;" onclick="editMovement('${movement.id}')" title="Modifier">
                                <span class="material-icons" style="font-size: 1rem;">edit</span>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem;" onclick="deleteMovement('${movement.id}')" title="Supprimer">
                                <span class="material-icons" style="font-size: 1rem;">delete</span>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getMovementTypeDisplay(type) {
            const types = {
                'income': '<span class="badge badge-success">Recette</span>',
                'expense': '<span class="badge badge-danger">Dépense</span>',
                'transfer': '<span class="badge badge-info">Virement</span>'
            };
            return types[type] || '<span class="badge badge-warning">Inconnu</span>';
        }

        // Chargement des données depuis l'API PostgreSQL
        async function loadFinanceData() {
            try {
                console.log('🔄 Chargement des données depuis l\'API PostgreSQL...');

                // Charger les budgets depuis l'API PostgreSQL
                const budgetResponse = await fetch('http://localhost:5000/api/budgets');
                if (budgetResponse.ok) {
                    const budgetData = await budgetResponse.json();
                    if (budgetData.success) {
                        budgetCategories = budgetData.data || [];
                        console.log(`✅ ${budgetCategories.length} budgets chargés depuis PostgreSQL`);
                    } else {
                        throw new Error('Erreur lors du chargement des budgets: ' + budgetData.error);
                    }
                } else {
                    throw new Error('Erreur HTTP lors du chargement des budgets: ' + budgetResponse.status);
                }

                // Pour l'instant, utiliser les données de démo pour les comptes et mouvements
                // (ces tables seront ajoutées plus tard à l'API)
                accounts = [...demoAccounts];
                movements = [...demoMovements];
                budgets = [...demoBudgets];

                console.log('✅ Données financières chargées avec succès');
                showAlert('Données chargées depuis PostgreSQL', 'success');

            } catch (error) {
                console.error('❌ Erreur lors du chargement depuis PostgreSQL:', error);
                console.log('🔄 Chargement des données de démonstration en fallback');

                // Fallback vers les données de démonstration
                accounts = [...demoAccounts];
                movements = [...demoMovements];
                budgets = [...demoBudgets];
                budgetCategories = [...demoBudgetCategories];

                showAlert('Données de démonstration chargées (PostgreSQL indisponible)', 'warning');
            }

            renderAccountsTable();
            renderMovementsTable();
            updateStats();

            // Charger les données budgétaires si on est sur l'onglet budget
            if (currentTab === 'budget') {
                loadBudgetData();
            }

            // Charger les filtres d'analyse
            loadAnalysisFilterItems();

            // Initialiser l'interface tableur
            initializeSpreadsheetInterface();
        }

        // ===== INITIALISATION INTERFACE TABLEUR =====

        // Initialiser l'interface tableur
        function initializeSpreadsheetInterface() {
            // Charger les données sauvegardées
            loadSavedData();

            // Ajouter les gestionnaires d'événements clavier
            addKeyboardEventListeners();

            // Initialiser l'état des boutons
            updateHistoryButtons();
            updateSelectedRowsDisplay();
        }

        // Charger les données sauvegardées
        function loadSavedData() {
            try {
                const savedData = localStorage.getItem('erp_budget_data');
                const savedHistory = localStorage.getItem('erp_budget_history');

                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    // Fusionner avec les données de démo existantes
                    const existingIds = budgetCategories.map(b => b.id);
                    const newData = parsedData.filter(b => !existingIds.includes(b.id));
                    budgetCategories.push(...newData);
                }

                if (savedHistory) {
                    actionHistory = JSON.parse(savedHistory);
                    historyIndex = actionHistory.length - 1;
                }
            } catch (error) {
                console.warn('Erreur lors du chargement des données sauvegardées:', error);
            }
        }

        // Ajouter les gestionnaires d'événements clavier
        function addKeyboardEventListeners() {
            document.addEventListener('keydown', function(event) {
                // Ctrl+Z : Annuler
                if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
                    event.preventDefault();
                    undoLastAction();
                }

                // Ctrl+Y ou Ctrl+Shift+Z : Rétablir
                if ((event.ctrlKey && event.key === 'y') || (event.ctrlKey && event.shiftKey && event.key === 'z')) {
                    event.preventDefault();
                    redoLastAction();
                }

                // Ctrl+A : Tout sélectionner (dans le tableau)
                if (event.ctrlKey && event.key === 'a' && event.target.closest('.data-table')) {
                    event.preventDefault();
                    document.getElementById('selectAllRows').checked = true;
                    toggleSelectAllRows();
                }

                // Échap : Annuler l'édition en cours
                if (event.key === 'Escape') {
                    if (editingCell) {
                        cancelCellEdit();
                    }
                }

                // Enter : Valider l'édition et passer à la ligne suivante
                if (event.key === 'Enter' && editingCell) {
                    event.preventDefault();
                    saveCellEdit();
                    moveToNextRow();
                }

                // Tab : Passer à la cellule suivante
                if (event.key === 'Tab' && editingCell) {
                    event.preventDefault();
                    saveCellEdit();
                    if (event.shiftKey) {
                        moveToPreviousCell();
                    } else {
                        moveToNextCell();
                    }
                }
            });
        }

        // ===== ÉDITION INLINE DES CELLULES =====

        // Commencer l'édition d'une cellule
        function startCellEdit(cell, budgetId, field) {
            // Annuler l'édition précédente si elle existe
            if (editingCell) {
                saveCellEdit();
            }

            editingCell = {
                element: cell,
                budgetId: budgetId,
                field: field,
                originalValue: cell.textContent
            };

            // Ajouter la classe d'édition
            cell.classList.add('editing');

            // Créer l'éditeur selon le type de champ
            createCellEditor(cell, field);
        }

        // Créer l'éditeur de cellule
        function createCellEditor(cell, field) {
            const currentValue = cell.textContent;
            let editor;

            if (field === 'categoryName') {
                // Éditeur de texte simple
                editor = document.createElement('input');
                editor.type = 'text';
                editor.value = currentValue;
            } else if (field === 'costCenter' || field === 'analyticCode') {
                // Liste déroulante
                editor = document.createElement('select');
                populateSelectEditor(editor, field);
                editor.value = getCurrentSelectValue(field, editingCell.budgetId);
            } else if (field.includes('forecast') || field.includes('realized')) {
                // Éditeur numérique
                editor = document.createElement('input');
                editor.type = 'number';
                editor.step = '0.01';
                editor.value = parseFloat(currentValue.replace(/[^\d.-]/g, '')) || 0;
            } else {
                // Éditeur de texte par défaut
                editor = document.createElement('input');
                editor.type = 'text';
                editor.value = currentValue;
            }

            editor.className = 'cell-editor';
            cell.appendChild(editor);
            editor.focus();
            editor.select();
        }

        // Peupler un éditeur de type select
        function populateSelectEditor(select, field) {
            if (field === 'costCenter') {
                costCenters.forEach(center => {
                    const option = document.createElement('option');
                    option.value = center.id;
                    option.textContent = `${center.code} - ${center.name}`;
                    select.appendChild(option);
                });
            } else if (field === 'analyticCode') {
                analyticCodes.forEach(code => {
                    const option = document.createElement('option');
                    option.value = code.id;
                    option.textContent = `${code.code} - ${code.name}`;
                    select.appendChild(option);
                });
            }
        }

        // Obtenir la valeur actuelle pour un select
        function getCurrentSelectValue(field, budgetId) {
            const budget = budgetCategories.find(b => b.id === budgetId);
            if (!budget) return '';

            if (field === 'costCenter') {
                return budget.costCenter;
            } else if (field === 'analyticCode') {
                return budget.analyticCode;
            }
            return '';
        }

        // Sauvegarder l'édition de cellule
        function saveCellEdit() {
            if (!editingCell) return;

            const editor = editingCell.element.querySelector('.cell-editor');
            if (!editor) return;

            const newValue = editor.value;
            const budget = budgetCategories.find(b => b.id === editingCell.budgetId);

            if (budget) {
                // Sauvegarder l'ancienne valeur pour l'historique
                const oldBudget = JSON.parse(JSON.stringify(budget));

                // Appliquer la nouvelle valeur
                if (editingCell.field === 'categoryName') {
                    budget.categoryName = newValue;
                } else if (editingCell.field === 'costCenter') {
                    budget.costCenter = newValue;
                    const center = costCenters.find(c => c.id === newValue);
                    budget.costCenterName = center ? center.name : '';
                } else if (editingCell.field === 'analyticCode') {
                    budget.analyticCode = newValue;
                    const code = analyticCodes.find(c => c.id === newValue);
                    budget.analyticCodeName = code ? code.name : '';
                } else if (editingCell.field.includes('forecast') || editingCell.field.includes('realized')) {
                    // Gérer les montants mensuels
                    const numericValue = parseFloat(newValue) || 0;
                    // Logique pour mettre à jour les données mensuelles
                    updateMonthlyValue(budget, editingCell.field, numericValue);
                }

                budget.modifiedDate = new Date().toISOString();

                // Sauvegarder dans l'historique
                saveToHistory('edit', {
                    id: budget.id,
                    oldValue: oldBudget,
                    newValue: JSON.parse(JSON.stringify(budget))
                });

                // Auto-save
                triggerAutoSave();
            }

            // Nettoyer l'édition
            cleanupCellEdit();

            // Actualiser l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();
        }

        // Annuler l'édition de cellule
        function cancelCellEdit() {
            if (!editingCell) return;
            cleanupCellEdit();
        }

        // Nettoyer l'édition de cellule
        function cleanupCellEdit() {
            if (editingCell) {
                editingCell.element.classList.remove('editing');
                const editor = editingCell.element.querySelector('.cell-editor');
                if (editor) {
                    editor.remove();
                }
                editingCell = null;
            }
        }

        // Mettre à jour une valeur mensuelle
        function updateMonthlyValue(budget, field, value) {
            // Extraire le mois et le type (forecast/realized) du nom du champ
            // Exemple: "jan_forecast" -> mois: "janvier", type: "forecast"
            const months = {
                'jan': 'janvier', 'feb': 'février', 'mar': 'mars', 'apr': 'avril',
                'may': 'mai', 'jun': 'juin', 'jul': 'juillet', 'aug': 'août',
                'sep': 'septembre', 'oct': 'octobre', 'nov': 'novembre', 'dec': 'décembre'
            };

            const parts = field.split('_');
            if (parts.length === 2) {
                const monthKey = months[parts[0]];
                const type = parts[1];

                if (monthKey && budget.monthlyData && budget.monthlyData[monthKey]) {
                    budget.monthlyData[monthKey][type] = value;

                    // Recalculer les totaux
                    budget.forecast = Object.values(budget.monthlyData).reduce((sum, month) => sum + (month.forecast || 0), 0);
                    budget.realized = Object.values(budget.monthlyData).reduce((sum, month) => sum + (month.realized || 0), 0);
                }
            }
        }

        // Navigation entre cellules
        function moveToNextCell() {
            // Logique pour passer à la cellule suivante
            // À implémenter selon la structure du tableau
        }

        function moveToPreviousCell() {
            // Logique pour passer à la cellule précédente
            // À implémenter selon la structure du tableau
        }

        function moveToNextRow() {
            // Logique pour passer à la ligne suivante
            // À implémenter selon la structure du tableau
        }

        // ===== GESTION SÉLECTION LIGNES =====

        // Basculer la sélection d'une ligne
        function toggleRowSelection(budgetId) {
            if (selectedRows.has(budgetId)) {
                selectedRows.delete(budgetId);
            } else {
                selectedRows.add(budgetId);
            }

            updateSelectedRowsDisplay();

            // Mettre à jour la case "Tout sélectionner"
            const totalRows = document.querySelectorAll('.row-checkbox').length;
            const selectAllCheckbox = document.getElementById('selectAllRows');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = selectedRows.size === totalRows;
                selectAllCheckbox.indeterminate = selectedRows.size > 0 && selectedRows.size < totalRows;
            }
        }

        function refreshAccounts() {
            loadFinanceData();
            showAlert('Données actualisées avec succès', 'success');
        }

        function refreshMovements() {
            loadFinanceData();
            showAlert('Données actualisées avec succès', 'success');
        }

        function viewAccountDetails(accountId) {
            const account = accounts.find(a => a.id === accountId);
            if (!account) return;

            const accountMovements = movements.filter(m => m.accountId === accountId);
            const totalIncome = accountMovements.filter(m => m.type === 'income').reduce((sum, m) => sum + m.amount, 0);
            const totalExpenses = accountMovements.filter(m => m.type === 'expense').reduce((sum, m) => sum + m.amount, 0);

            alert(`Détails du compte ${account.name}:\n\n` +
                  `Banque: ${account.bank}\n` +
                  `Type: ${account.type}\n` +
                  `Solde actuel: ${account.balance.toLocaleString()}€\n` +
                  `Seuil d'alerte: ${account.alertThreshold.toLocaleString()}€\n\n` +
                  `Mouvements:\n` +
                  `- Total recettes: ${totalIncome.toLocaleString()}€\n` +
                  `- Total dépenses: ${totalExpenses.toLocaleString()}€\n` +
                  `- Nombre de mouvements: ${accountMovements.length}`);
        }

        function editMovement(id) {
            const movement = movements.find(m => m.id === id);
            if (!movement) return;

            editingMovementId = id;

            // Remplir le formulaire
            document.getElementById('movementDate').value = movement.date;
            document.getElementById('movementAccount').value = movement.accountId;
            document.getElementById('movementType').value = movement.type;
            document.getElementById('movementAmount').value = movement.amount;
            document.getElementById('movementLabel').value = movement.label;
            document.getElementById('movementReference').value = movement.reference || '';
            document.getElementById('movementCategory').value = movement.category || '';
            document.getElementById('movementNotes').value = movement.notes || '';

            openModal('movementModal');
        }

        function deleteMovement(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce mouvement ?')) {
                movements = movements.filter(m => m.id !== id);
                renderMovementsTable();
                updateStats();
                showAlert('Mouvement supprimé avec succès', 'success');
            }
        }

        // Génération du rapport financier
        function generateFinanceReport() {
            const totalTreasury = accounts.reduce((sum, acc) => sum + acc.balance, 0);
            const totalBudget = budgets.reduce((sum, b) => sum + b.budgetAmount, 0);
            const totalSpent = budgets.reduce((sum, b) => sum + b.spentAmount, 0);

            // Calculs par catégorie
            const categoryTotals = {};
            movements.forEach(movement => {
                const category = movement.category || 'other';
                if (!categoryTotals[category]) {
                    categoryTotals[category] = { income: 0, expense: 0 };
                }
                if (movement.type === 'income') {
                    categoryTotals[category].income += movement.amount;
                } else if (movement.type === 'expense') {
                    categoryTotals[category].expense += movement.amount;
                }
            });

            let reportContent = `
                <h2>Rapport Financier - ${new Date().toLocaleDateString()}</h2>

                <h3>Synthèse Trésorerie</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Compte</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Solde</th>
                    </tr>
            `;

            accounts.forEach(account => {
                const balanceColor = account.balance >= 0 ? '#10b981' : '#ef4444';
                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${account.name} (${account.bank})</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${balanceColor};">${account.balance.toLocaleString()}€</td>
                    </tr>
                `;
            });

            reportContent += `
                    <tr style="background: #f9fafb; font-weight: bold;">
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">TOTAL TRÉSORERIE</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${totalTreasury.toLocaleString()}€</td>
                    </tr>
                </table>

                <h3>Analyse par Catégorie</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Catégorie</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Recettes</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Dépenses</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Solde</th>
                    </tr>
            `;

            Object.keys(categoryTotals).forEach(category => {
                const cat = categoryTotals[category];
                const balance = cat.income - cat.expense;
                const balanceColor = balance >= 0 ? '#10b981' : '#ef4444';
                const categoryName = {
                    'sales': 'Ventes',
                    'purchases': 'Achats',
                    'salaries': 'Salaires',
                    'taxes': 'Taxes et charges',
                    'investments': 'Investissements',
                    'loans': 'Emprunts',
                    'other': 'Autres'
                }[category] || category;

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${categoryName}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #10b981;">${cat.income.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: #ef4444;">${cat.expense.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${balanceColor};">${balance.toLocaleString()}€</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>

                <h3>Suivi Budgétaire</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr style="background: #f9fafb;">
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left;">Département</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Budget</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Dépensé</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">Disponible</th>
                        <th style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">% Utilisé</th>
                    </tr>
            `;

            budgets.forEach(budget => {
                const available = budget.budgetAmount - budget.spentAmount;
                const percentUsed = (budget.spentAmount / budget.budgetAmount * 100).toFixed(1);
                const percentColor = percentUsed > 80 ? '#ef4444' : percentUsed > 60 ? '#f59e0b' : '#10b981';

                reportContent += `
                    <tr>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb;">${budget.department}</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${budget.budgetAmount.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${budget.spentAmount.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right;">${available.toLocaleString()}€</td>
                        <td style="padding: 0.5rem; border: 1px solid #e5e7eb; text-align: right; color: ${percentColor};">${percentUsed}%</td>
                    </tr>
                `;
            });

            reportContent += `
                </table>
            `;

            // Ouvrir le rapport dans une nouvelle fenêtre
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Rapport Financier - ERP HUB</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; }
                        h2 { color: #f97316; margin-bottom: 1rem; }
                        h3 { color: #374151; margin: 1.5rem 0 0.5rem 0; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; }
                        th { background: #f9fafb; font-weight: 600; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div style="margin-top: 2rem; text-align: center;">
                        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #f97316; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                            Imprimer
                        </button>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');

            // Activer l'onglet sélectionné
            event.target.closest('.nav-tab').classList.add('active');

            currentTab = tabName;

            // Charger les données spécifiques à l'onglet
            if (tabName === 'budget') {
                loadBudgetData();
            }
        }

        // Chargement des données budgétaires
        function loadBudgetData() {
            budgetCategories = [...demoBudgetCategories];
            loadFiltersInSelects();
            updateBudgetView();
        }

        // Mise à jour de la vue budget selon les filtres
        function updateBudgetView() {
            currentPeriodFilter = document.getElementById('budgetPeriodFilter').value;

            // Récupérer les filtres sélectionnés via les cases à cocher
            const selectedCostCenters = getSelectedFilterValues('costCenter');
            const selectedAnalyticCodes = getSelectedFilterValues('analyticCode');
            const selectedBudgetTypes = getSelectedFilterValues('budgetType');

            // Filtrer les données
            filteredBudgetData = budgetCategories.filter(budget => {
                let matches = true;

                // Filtre centres de coûts
                if (selectedCostCenters.length > 0 && !selectedCostCenters.includes(budget.costCenter)) {
                    matches = false;
                }

                // Filtre codes analytiques
                if (selectedAnalyticCodes.length > 0 && !selectedAnalyticCodes.includes(budget.analyticCode)) {
                    matches = false;
                }

                // Filtre types de budget
                if (selectedBudgetTypes.length > 0 && !selectedBudgetTypes.includes(budget.categoryType)) {
                    matches = false;
                }

                return matches;
            });

            // Mettre à jour l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();
            renderBudgetChart();
            renderMonthlyChart();
        }

        // Récupérer les valeurs sélectionnées pour un type de filtre
        function getSelectedFilterValues(filterType) {
            const items = document.getElementById(filterType + 'Items');
            const checkedBoxes = items.querySelectorAll('input[type="checkbox"]:checked');
            return Array.from(checkedBoxes).map(checkbox => checkbox.value);
        }

        // Mise à jour des statistiques budgétaires
        function updateBudgetStats() {
            const totalAllocated = budgetCategories.reduce((sum, cat) => sum + cat.forecast, 0);
            const totalSpent = budgetCategories.reduce((sum, cat) => sum + cat.realized, 0);
            const totalRemaining = totalAllocated - totalSpent;
            const utilizationRate = totalAllocated > 0 ? (totalSpent / totalAllocated * 100) : 0;

            document.getElementById('totalBudgetAllocated').textContent = totalAllocated.toLocaleString() + '€';
            document.getElementById('totalBudgetSpent').textContent = totalSpent.toLocaleString() + '€';
            document.getElementById('totalBudgetRemaining').textContent = totalRemaining.toLocaleString() + '€';
            document.getElementById('budgetUtilizationRate').textContent = utilizationRate.toFixed(1) + '%';

            // Coloration des valeurs
            const remainingElement = document.getElementById('totalBudgetRemaining');
            remainingElement.className = 'stat-value ' + (totalRemaining >= 0 ? 'amount-positive' : 'amount-negative');

            const rateElement = document.getElementById('budgetUtilizationRate');
            let rateClass = 'stat-value ';
            if (utilizationRate > 90) rateClass += 'amount-negative';
            else if (utilizationRate > 75) rateClass += 'amount-warning';
            else rateClass += 'amount-positive';
            rateElement.className = rateClass;
        }

        // Rendu du tableau des budgets par catégorie
        function renderBudgetCategoriesTable() {
            const tbody = document.getElementById('budgetCategoriesTableBody');
            const dataToRender = filteredBudgetData.length > 0 ? filteredBudgetData : budgetCategories;

            if (dataToRender.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="31" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun budget trouvé. Créez votre premier budget ou ajustez les filtres.
                        </td>
                    </tr>
                `;
                return;
            }

            const budgetRows = dataToRender.map((budget, index) => {
                const variance = budget.realized - budget.forecast;
                const percentage = budget.forecast > 0 ? (budget.realized / budget.forecast * 100) : 0;

                let varianceClass = 'amount-positive';
                let percentageClass = 'amount-positive';

                if (budget.categoryType === 'revenue') {
                    // Pour les revenus, un écart positif est bon
                    varianceClass = variance >= 0 ? 'amount-positive' : 'amount-negative';
                    percentageClass = percentage >= 100 ? 'amount-positive' : percentage >= 75 ? 'amount-warning' : 'amount-negative';
                } else {
                    // Pour les dépenses, un écart négatif est bon (moins dépensé que prévu)
                    varianceClass = variance <= 0 ? 'amount-positive' : 'amount-negative';
                    percentageClass = percentage <= 100 ? 'amount-positive' : percentage <= 110 ? 'amount-warning' : 'amount-negative';
                }

                // Générer les colonnes mensuelles
                const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                              'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];

                let forecastColumns = '';
                let realizedColumns = '';

                months.forEach((month, monthIndex) => {
                    const monthData = budget.monthlyData && budget.monthlyData[month] ? budget.monthlyData[month] : { forecast: 0, realized: 0 };

                    // Colonnes budget prévisionnel éditables
                    forecastColumns += `
                        <td style="text-align: right; font-size: 0.875rem; background: #fef3e2;"
                            class="editable-cell"
                            data-budget-id="${budget.id}"
                            data-field="${monthIndex}_forecast"
                            onclick="startInlineEdit(this, '${budget.id}', '${monthIndex}_forecast', ${index}, ${5 + monthIndex})">
                            ${monthData.forecast.toLocaleString()}
                        </td>`;

                    // Colonnes montant réalisé éditables avec couleur selon valeur
                    const realizedClass = monthData.realized > 0 ? 'amount-positive' : '';
                    realizedColumns += `
                        <td style="text-align: right; font-size: 0.875rem; background: #f0fdf4;"
                            class="editable-cell ${realizedClass}"
                            data-budget-id="${budget.id}"
                            data-field="${monthIndex}_realized"
                            onclick="startInlineEdit(this, '${budget.id}', '${monthIndex}_realized', ${index}, ${17 + monthIndex})">
                            ${monthData.realized.toLocaleString()}
                        </td>`;
                });

                return `
                    <tr class="${selectedRows.has(budget.id) ? 'row-selected' : ''}">
                        <td style="position: sticky; left: 0; background: white; z-index: 10; width: 50px;">
                            <input type="checkbox" class="row-checkbox" value="${budget.id}"
                                   ${selectedRows.has(budget.id) ? 'checked' : ''}
                                   onchange="toggleRowSelection('${budget.id}')">
                        </td>
                        <td style="position: sticky; left: 50px; background: white; z-index: 10; width: 120px;">
                            <div style="display: flex; gap: 0.25rem;">
                                <button class="btn btn-sm btn-primary" onclick="editBudgetCategory('${budget.id}')" title="Modifier">
                                    <span class="material-icons" style="font-size: 0.875rem;">edit</span>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteBudgetCategory('${budget.id}')" title="Supprimer">
                                    <span class="material-icons" style="font-size: 0.875rem;">delete</span>
                                </button>
                            </div>
                        </td>
                        <td style="position: sticky; left: 170px; background: white; z-index: 10;"
                            class="editable-cell"
                            data-budget-id="${budget.id}"
                            data-field="categoryName"
                            onclick="startInlineEdit(this, '${budget.id}', 'categoryName', ${index}, 2)">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="font-weight: 600; font-size: 0.875rem;">${budget.categoryName}</span>
                                <span style="font-size: 0.75rem; color: #6b7280;">(${budget.categoryType})</span>
                            </div>
                        </td>
                        <td style="position: sticky; left: 200px; background: white; z-index: 10;"
                            class="editable-cell"
                            data-budget-id="${budget.id}"
                            data-field="costCenter"
                            onclick="startInlineEdit(this, '${budget.id}', 'costCenter', ${index}, 3)">
                            <div style="font-size: 0.875rem;">
                                <div style="font-weight: 600;">${budget.costCenterName || 'Non défini'}</div>
                                <div style="color: #6b7280; font-size: 0.75rem;">${budget.costCenter || ''}</div>
                            </div>
                        </td>
                        <td style="position: sticky; left: 320px; background: white; z-index: 10;"
                            class="editable-cell"
                            data-budget-id="${budget.id}"
                            data-field="analyticCode"
                            onclick="startInlineEdit(this, '${budget.id}', 'analyticCode', ${index}, 4)">
                            <div style="font-size: 0.875rem;">
                                <div style="font-weight: 600;">${budget.analyticCodeName || 'Non défini'}</div>
                                <div style="color: #6b7280; font-size: 0.75rem;">${budget.analyticCode || ''}</div>
                            </div>
                        </td>
                        ${forecastColumns}
                        ${realizedColumns}
                        <td style="text-align: right; font-weight: 600;" class="${varianceClass}">${variance >= 0 ? '+' : ''}${variance.toLocaleString()}€</td>
                        <td style="text-align: right; font-weight: 600;" class="${percentageClass}">${percentage.toFixed(1)}%</td>
                        <td style="font-size: 0.875rem;"
                            class="editable-cell"
                            data-budget-id="${budget.id}"
                            data-field="responsible"
                            onclick="startInlineEdit(this, '${budget.id}', 'responsible', ${index}, 30)">
                            ${budget.responsible || 'Non défini'}
                        </td>
                    </tr>
                `;
            }).join('');

            // Ajouter la ligne de création permanente
            const newRowHtml = createNewBudgetRow();

            tbody.innerHTML = budgetRows + newRowHtml;
        }

        // Créer une ligne de création permanente
        function createNewBudgetRow() {
            const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                          'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];

            let forecastColumns = '';
            let realizedColumns = '';

            months.forEach((month, monthIndex) => {
                forecastColumns += `
                    <td style="text-align: right; font-size: 0.875rem; background: #fef3e2;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="${monthIndex}_forecast"
                        onclick="startNewBudgetEdit(this, '${monthIndex}_forecast')">
                        0
                    </td>`;

                realizedColumns += `
                    <td style="text-align: right; font-size: 0.875rem; background: #f0fdf4;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="${monthIndex}_realized"
                        onclick="startNewBudgetEdit(this, '${monthIndex}_realized')">
                        0
                    </td>`;
            });

            return `
                <tr class="new-row">
                    <td style="position: sticky; left: 0; background: #f9fafb; z-index: 10; width: 50px;">
                        <button class="btn btn-sm btn-success" onclick="createNewBudget()" title="Créer nouveau budget">
                            <span class="material-icons" style="font-size: 0.875rem;">add</span>
                        </button>
                    </td>
                    <td style="position: sticky; left: 50px; background: #f9fafb; z-index: 10; width: 120px;">
                        <span style="font-style: italic; color: #6b7280; font-size: 0.75rem;">Nouveau budget</span>
                    </td>
                    <td style="position: sticky; left: 170px; background: #f9fafb; z-index: 10;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="categoryName"
                        onclick="startNewBudgetEdit(this, 'categoryName')">
                        <span style="font-style: italic; color: #6b7280;">Cliquer pour saisir...</span>
                    </td>
                    <td style="position: sticky; left: 200px; background: #f9fafb; z-index: 10;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="costCenter"
                        onclick="startNewBudgetEdit(this, 'costCenter')">
                        <span style="font-style: italic; color: #6b7280;">Sélectionner...</span>
                    </td>
                    <td style="position: sticky; left: 320px; background: #f9fafb; z-index: 10;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="analyticCode"
                        onclick="startNewBudgetEdit(this, 'analyticCode')">
                        <span style="font-style: italic; color: #6b7280;">Sélectionner...</span>
                    </td>
                    ${forecastColumns}
                    ${realizedColumns}
                    <td style="text-align: right; font-weight: 600; color: #6b7280;">0€</td>
                    <td style="text-align: right; font-weight: 600; color: #6b7280;">0%</td>
                    <td style="font-size: 0.875rem;"
                        class="editable-cell new-cell"
                        data-budget-id="new"
                        data-field="responsible"
                        onclick="startNewBudgetEdit(this, 'responsible')">
                        <span style="font-style: italic; color: #6b7280;">Responsable...</span>
                    </td>
                </tr>
            `;
        }

        // ===== GESTION CRÉATION NOUVEAUX BUDGETS =====

        let newBudgetData = {};

        // Démarrer l'édition d'un nouveau budget
        function startNewBudgetEdit(cell, field) {
            // Initialiser les données du nouveau budget si nécessaire
            if (Object.keys(newBudgetData).length === 0) {
                newBudgetData = {
                    id: 'new_' + Date.now(),
                    categoryName: '',
                    categoryType: 'expense',
                    costCenter: '',
                    analyticCode: '',
                    responsible: '',
                    department: '',
                    notes: '',
                    forecast: 0,
                    realized: 0,
                    monthlyData: generateEmptyMonthlyData()
                };
            }

            // Créer l'éditeur spécialisé
            createNewBudgetEditor(cell, field);
        }

        // Créer un éditeur pour nouveau budget
        function createNewBudgetEditor(cell, field) {
            let editor;
            const currentValue = getNewBudgetFieldValue(field);

            // Déterminer le type d'éditeur selon le champ
            if (field === 'categoryName') {
                editor = createTextEditor(currentValue);
                editor.placeholder = 'Nom de la catégorie budgétaire';
            } else if (field === 'costCenter') {
                editor = createSelectEditor(currentValue, getCostCenterOptions());
            } else if (field === 'analyticCode') {
                editor = createSelectEditor(currentValue, getAnalyticCodeOptions());
            } else if (field === 'responsible') {
                editor = createTextEditor(currentValue);
                editor.placeholder = 'Nom du responsable';
            } else if (field.includes('forecast') || field.includes('realized')) {
                editor = createNumberEditor(currentValue);
            } else {
                editor = createTextEditor(currentValue);
            }

            // Ajouter l'éditeur à la cellule
            cell.innerHTML = '';
            cell.appendChild(editor);

            // Focus et sélection
            editor.focus();
            if (editor.select) editor.select();

            // Gestionnaires d'événements spéciaux pour nouveau budget
            setupNewBudgetEditorEvents(editor, cell, field);
        }

        // Configurer les événements pour l'éditeur de nouveau budget
        function setupNewBudgetEditorEvents(editor, cell, field) {
            // Gestionnaire de validation temps réel
            editor.addEventListener('input', () => {
                validateFieldValue(editor, field);
            });

            // Gestionnaire de touches
            editor.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveNewBudgetField(field, editor.value);
                    restoreNewBudgetCell(cell, field);

                    // Si c'est le nom de catégorie, passer au centre de coûts
                    if (field === 'categoryName') {
                        const nextCell = cell.parentNode.querySelector('[data-field="costCenter"]');
                        if (nextCell) {
                            setTimeout(() => startNewBudgetEdit(nextCell, 'costCenter'), 100);
                        }
                    }
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    restoreNewBudgetCell(cell, field);
                } else if (e.key === 'Tab') {
                    e.preventDefault();
                    saveNewBudgetField(field, editor.value);
                    restoreNewBudgetCell(cell, field);
                    // Navigation automatique vers la cellule suivante
                }
            });

            // Gestionnaire de perte de focus
            editor.addEventListener('blur', () => {
                setTimeout(() => {
                    saveNewBudgetField(field, editor.value);
                    restoreNewBudgetCell(cell, field);
                }, 100);
            });

            // Gestionnaire de changement pour les selects
            if (editor.tagName === 'SELECT') {
                editor.addEventListener('change', () => {
                    saveNewBudgetField(field, editor.value);
                    restoreNewBudgetCell(cell, field);
                });
            }
        }

        // Obtenir la valeur d'un champ du nouveau budget
        function getNewBudgetFieldValue(field) {
            if (field === 'categoryName') return newBudgetData.categoryName || '';
            if (field === 'costCenter') return newBudgetData.costCenter || '';
            if (field === 'analyticCode') return newBudgetData.analyticCode || '';
            if (field === 'responsible') return newBudgetData.responsible || '';

            // Gérer les champs mensuels
            if (field.includes('_')) {
                const [monthIndex, type] = field.split('_');
                const monthKey = getMonthKey(parseInt(monthIndex));
                if (newBudgetData.monthlyData && newBudgetData.monthlyData[monthKey]) {
                    return newBudgetData.monthlyData[monthKey][type] || 0;
                }
            }

            return '';
        }

        // Sauvegarder un champ du nouveau budget
        function saveNewBudgetField(field, value) {
            if (field === 'categoryName') {
                newBudgetData.categoryName = value;
            } else if (field === 'costCenter') {
                newBudgetData.costCenter = value;
                const center = costCenters.find(c => c.id === value);
                newBudgetData.costCenterName = center ? center.name : '';
            } else if (field === 'analyticCode') {
                newBudgetData.analyticCode = value;
                const code = analyticCodes.find(c => c.id === value);
                newBudgetData.analyticCodeName = code ? code.name : '';
            } else if (field === 'responsible') {
                newBudgetData.responsible = value;
            } else if (field.includes('_')) {
                // Gérer les champs mensuels
                const [monthIndex, type] = field.split('_');
                const monthKey = getMonthKey(parseInt(monthIndex));

                if (!newBudgetData.monthlyData[monthKey]) {
                    newBudgetData.monthlyData[monthKey] = { forecast: 0, realized: 0 };
                }

                newBudgetData.monthlyData[monthKey][type] = parseFloat(value) || 0;

                // Recalculer les totaux
                newBudgetData.forecast = Object.values(newBudgetData.monthlyData).reduce((sum, month) => sum + (month.forecast || 0), 0);
                newBudgetData.realized = Object.values(newBudgetData.monthlyData).reduce((sum, month) => sum + (month.realized || 0), 0);
            }
        }

        // Restaurer l'affichage d'une cellule de nouveau budget
        function restoreNewBudgetCell(cell, field) {
            const value = getNewBudgetFieldValue(field);

            if (field === 'categoryName') {
                cell.innerHTML = value ? `<span style="font-weight: 600;">${value}</span>` : '<span style="font-style: italic; color: #6b7280;">Cliquer pour saisir...</span>';
            } else if (field === 'costCenter') {
                const centerName = newBudgetData.costCenterName || 'Sélectionner...';
                cell.innerHTML = value ? `<span>${centerName}</span>` : '<span style="font-style: italic; color: #6b7280;">Sélectionner...</span>';
            } else if (field === 'analyticCode') {
                const codeName = newBudgetData.analyticCodeName || 'Sélectionner...';
                cell.innerHTML = value ? `<span>${codeName}</span>` : '<span style="font-style: italic; color: #6b7280;">Sélectionner...</span>';
            } else if (field === 'responsible') {
                cell.innerHTML = value ? `<span>${value}</span>` : '<span style="font-style: italic; color: #6b7280;">Responsable...</span>';
            } else if (field.includes('forecast') || field.includes('realized')) {
                cell.innerHTML = value ? value.toLocaleString() : '0';
            } else {
                cell.innerHTML = value || '<span style="font-style: italic; color: #6b7280;">...</span>';
            }
        }

        // Créer un nouveau budget complet
        function createNewBudget() {
            // Vérifier que les champs obligatoires sont remplis
            if (!newBudgetData.categoryName || !newBudgetData.costCenter || !newBudgetData.analyticCode) {
                showAlert('Veuillez remplir au minimum le nom de catégorie, le centre de coûts et le code analytique', 'warning');
                return;
            }

            // Générer un ID unique
            const newId = 'budget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            newBudgetData.id = newId;
            newBudgetData.createdDate = new Date().toISOString();
            newBudgetData.modifiedDate = new Date().toISOString();

            // Ajouter au tableau des budgets
            budgetCategories.push(JSON.parse(JSON.stringify(newBudgetData)));

            // Sauvegarder en base de données
            saveBudgetToDatabase(newBudgetData);

            // Sauvegarder dans l'historique
            saveToHistory('create', {
                id: newId,
                budget: JSON.parse(JSON.stringify(newBudgetData))
            });

            // Réinitialiser les données du nouveau budget
            newBudgetData = {};

            // Mettre à jour l'affichage
            renderBudgetCategoriesTable();
            updateBudgetStats();
            renderBudgetChart();
            renderMonthlyChart();

            // Auto-save
            triggerAutoSave();

            showAlert('Nouveau budget créé avec succès', 'success');
        }

        // Générer des données mensuelles vides
        function generateEmptyMonthlyData() {
            const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                          'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
            const monthlyData = {};

            months.forEach(month => {
                monthlyData[month] = { forecast: 0, realized: 0 };
            });

            return monthlyData;
        }

        // Fonction pour générer des données mensuelles réalistes
        function generateMonthlyData(totalForecast, totalRealized) {
            const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                          'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];

            const monthlyForecast = totalForecast / 12;
            const monthlyData = {};

            let remainingRealized = totalRealized;

            months.forEach((month, index) => {
                let realized = 0;

                // Répartir le réalisé sur les premiers mois (simulation réaliste)
                if (index < 4 && remainingRealized > 0) { // Janvier à Avril
                    const variation = 0.8 + Math.random() * 0.4; // Variation de 80% à 120%
                    realized = Math.min(monthlyForecast * variation, remainingRealized);
                    remainingRealized -= realized;
                }

                monthlyData[month] = {
                    forecast: Math.round(monthlyForecast),
                    realized: Math.round(realized)
                };
            });

            return monthlyData;
        }

        // ===== FONCTIONS CRUD INLINE POUR LES BUDGETS =====

        // Supprimer un budget (remplace editBudgetCategory qui utilisait les modals)
        function editBudgetCategory(budgetId) {
            // Cette fonction n'est plus nécessaire avec l'édition inline
            // Rediriger vers la première cellule éditable de la ligne
            const row = document.querySelector(`tr .editable-cell[data-budget-id="${budgetId}"]`);
            if (row) {
                const budgetIndex = budgetCategories.findIndex(b => b.id === budgetId);
                startInlineEdit(row, budgetId, 'categoryName', budgetIndex, 2);
            }
        }

        function deleteBudgetCategory(budgetId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce budget ?')) {
                const index = budgetCategories.findIndex(b => b.id === budgetId);
                if (index !== -1) {
                    // Sauvegarder dans l'historique pour annulation
                    const deletedBudget = budgetCategories[index];
                    saveToHistory('delete', {
                        id: budgetId,
                        budget: JSON.parse(JSON.stringify(deletedBudget)),
                        index: index
                    });

                    // Supprimer du tableau local
                    budgetCategories.splice(index, 1);

                    // Supprimer de la base de données
                    deleteBudgetFromDatabase(budgetId);

                    // Mettre à jour l'affichage
                    renderBudgetCategoriesTable();
                    updateBudgetStats();
                    renderBudgetChart();
                    renderMonthlyChart();

                    // Auto-save
                    triggerAutoSave();

                    showAlert('Budget supprimé avec succès', 'success');
                }
            }
        }

        function refreshBudgets() {
            loadBudgetData();
            showAlert('Données budgétaires actualisées', 'success');
        }

        // Rendu du graphique budgétaire (version simplifiée)
        function renderBudgetChart() {
            const canvas = document.getElementById('budgetChartCanvas');
            const ctx = canvas.getContext('2d');

            // Effacer le canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Données pour le graphique
            const categories = budgetCategories.slice(0, 8); // Limiter à 8 catégories pour la lisibilité
            const colors = ['#f97316', '#10b981', '#3b82f6', '#8b5cf6', '#ef4444', '#f59e0b', '#06b6d4', '#84cc16'];

            if (categories.length === 0) {
                ctx.fillStyle = '#6b7280';
                ctx.font = '16px Inter';
                ctx.textAlign = 'center';
                ctx.fillText('Aucune donnée à afficher', canvas.width / 2, canvas.height / 2);
                return;
            }

            // Graphique en barres horizontales
            const maxValue = Math.max(...categories.map(c => Math.max(c.forecast, c.realized)));
            const barHeight = 30;
            const barSpacing = 10;
            const startY = 20;
            const chartWidth = canvas.width - 200;

            categories.forEach((category, index) => {
                const y = startY + index * (barHeight + barSpacing);

                // Barre de budget prévisionnel (fond)
                const forecastWidth = (category.forecast / maxValue) * chartWidth;
                ctx.fillStyle = colors[index % colors.length] + '40'; // Transparence
                ctx.fillRect(100, y, forecastWidth, barHeight);

                // Barre de réalisé
                const realizedWidth = (category.realized / maxValue) * chartWidth;
                ctx.fillStyle = colors[index % colors.length];
                ctx.fillRect(100, y, realizedWidth, barHeight);

                // Étiquette de catégorie
                ctx.fillStyle = '#374151';
                ctx.font = '12px Inter';
                ctx.textAlign = 'right';
                ctx.fillText(category.categoryName, 95, y + barHeight / 2 + 4);

                // Valeurs
                ctx.textAlign = 'left';
                ctx.fillText(`${category.realized.toLocaleString()}€ / ${category.forecast.toLocaleString()}€`,
                           100 + Math.max(forecastWidth, realizedWidth) + 10, y + barHeight / 2 + 4);
            });

            // Légende
            ctx.fillStyle = '#6b7280';
            ctx.font = '12px Inter';
            ctx.textAlign = 'left';
            ctx.fillText('■ Réalisé', 100, canvas.height - 30);
            ctx.fillText('□ Prévisionnel', 180, canvas.height - 30);
        }

        // Rendu du graphique mensuel
        function renderMonthlyChart() {
            const canvas = document.getElementById('monthlyChartCanvas');
            const ctx = canvas.getContext('2d');

            // Effacer le canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const dataToRender = filteredBudgetData.length > 0 ? filteredBudgetData : budgetCategories;

            if (dataToRender.length === 0 || !dataToRender[0].monthlyData) {
                ctx.fillStyle = '#6b7280';
                ctx.font = '16px Inter';
                ctx.textAlign = 'center';
                ctx.fillText('Aucune donnée mensuelle à afficher', canvas.width / 2, canvas.height / 2);
                return;
            }

            const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                          'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
            const monthsShort = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                               'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];

            // Calculer les totaux mensuels
            const monthlyTotals = months.map(month => {
                let forecastTotal = 0;
                let realizedTotal = 0;

                dataToRender.forEach(budget => {
                    if (budget.monthlyData && budget.monthlyData[month]) {
                        forecastTotal += budget.monthlyData[month].forecast;
                        realizedTotal += budget.monthlyData[month].realized;
                    }
                });

                return { forecast: forecastTotal, realized: realizedTotal };
            });

            // Paramètres du graphique
            const maxValue = Math.max(...monthlyTotals.map(m => Math.max(m.forecast, m.realized)));
            const barWidth = 25;
            const barSpacing = 15;
            const groupSpacing = 40;
            const startX = 60;
            const chartHeight = canvas.height - 100;
            const chartBottom = canvas.height - 50;

            // Dessiner les barres
            monthlyTotals.forEach((monthData, index) => {
                const x = startX + index * (barWidth * 2 + barSpacing + groupSpacing);

                // Barre prévisionnel
                const forecastHeight = (monthData.forecast / maxValue) * chartHeight;
                ctx.fillStyle = '#f97316';
                ctx.fillRect(x, chartBottom - forecastHeight, barWidth, forecastHeight);

                // Barre réalisé
                const realizedHeight = (monthData.realized / maxValue) * chartHeight;
                ctx.fillStyle = '#10b981';
                ctx.fillRect(x + barWidth + 5, chartBottom - realizedHeight, barWidth, realizedHeight);

                // Étiquettes des mois
                ctx.fillStyle = '#374151';
                ctx.font = '12px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(monthsShort[index], x + barWidth, chartBottom + 20);

                // Valeurs au-dessus des barres
                ctx.font = '10px Inter';
                if (monthData.forecast > 0) {
                    ctx.fillText(monthData.forecast.toLocaleString(), x + barWidth/2, chartBottom - forecastHeight - 5);
                }
                if (monthData.realized > 0) {
                    ctx.fillText(monthData.realized.toLocaleString(), x + barWidth + 5 + barWidth/2, chartBottom - realizedHeight - 5);
                }
            });

            // Légende
            ctx.fillStyle = '#f97316';
            ctx.fillRect(50, 20, 15, 15);
            ctx.fillStyle = '#374151';
            ctx.font = '12px Inter';
            ctx.textAlign = 'left';
            ctx.fillText('Prévisionnel', 70, 32);

            ctx.fillStyle = '#10b981';
            ctx.fillRect(180, 20, 15, 15);
            ctx.fillStyle = '#374151';
            ctx.fillText('Réalisé', 200, 32);

            // Titre
            ctx.font = '14px Inter';
            ctx.textAlign = 'center';
            ctx.fillText('Évolution Mensuelle des Budgets (€)', canvas.width / 2, 15);
        }

        // Fonction pour mettre à jour le graphique mensuel selon les options sélectionnées
        function updateMonthlyChart() {
            renderMonthlyChart();
        }

        // Fonction pour changer le type de graphique
        function toggleChartType(type) {
            currentChartType = type;

            // Mettre à jour les boutons
            document.getElementById('barChartBtn').classList.remove('btn-primary');
            document.getElementById('barChartBtn').classList.add('btn-secondary');
            document.getElementById('pieChartBtn').classList.remove('btn-primary');
            document.getElementById('pieChartBtn').classList.add('btn-secondary');

            if (type === 'bar') {
                document.getElementById('barChartBtn').classList.remove('btn-secondary');
                document.getElementById('barChartBtn').classList.add('btn-primary');
            } else {
                document.getElementById('pieChartBtn').classList.remove('btn-secondary');
                document.getElementById('pieChartBtn').classList.add('btn-primary');
            }

            renderBudgetChart();
        }

        // ===== NOUVELLES FONCTIONS POUR L'ANALYSE FINANCIÈRE MULTI-ANNÉES =====

        let financialChart = null;
        let financialData = {
            2024: {
                revenue: [120000, 135000, 142000, 138000, 155000, 162000, 158000, 165000, 172000, 168000, 175000, 180000],
                expenses: [85000, 92000, 98000, 95000, 105000, 110000, 108000, 112000, 118000, 115000, 120000, 125000],
                investments: [25000, 30000, 35000, 28000, 40000, 45000, 38000, 42000, 48000, 45000, 50000, 55000],
                margins: [35000, 43000, 44000, 43000, 50000, 52000, 50000, 53000, 54000, 53000, 55000, 55000],
                costCenters: [45000, 48000, 52000, 50000, 55000, 58000, 56000, 59000, 62000, 60000, 63000, 65000],
                revenueByCategory: [95000, 108000, 114000, 110000, 124000, 130000, 126000, 132000, 138000, 134000, 140000, 144000]
            },
            2023: {
                revenue: [110000, 125000, 132000, 128000, 145000, 152000, 148000, 155000, 162000, 158000, 165000, 170000],
                expenses: [80000, 87000, 93000, 90000, 100000, 105000, 103000, 107000, 113000, 110000, 115000, 120000],
                investments: [20000, 25000, 30000, 23000, 35000, 40000, 33000, 37000, 43000, 40000, 45000, 50000],
                margins: [30000, 38000, 39000, 38000, 45000, 47000, 45000, 48000, 49000, 48000, 50000, 50000],
                costCenters: [40000, 43000, 47000, 45000, 50000, 53000, 51000, 54000, 57000, 55000, 58000, 60000],
                revenueByCategory: [88000, 100000, 106000, 102000, 116000, 122000, 118000, 124000, 130000, 126000, 132000, 136000]
            },
            2022: {
                revenue: [100000, 115000, 122000, 118000, 135000, 142000, 138000, 145000, 152000, 148000, 155000, 160000],
                expenses: [75000, 82000, 88000, 85000, 95000, 100000, 98000, 102000, 108000, 105000, 110000, 115000],
                investments: [15000, 20000, 25000, 18000, 30000, 35000, 28000, 32000, 38000, 35000, 40000, 45000],
                margins: [25000, 33000, 34000, 33000, 40000, 42000, 40000, 43000, 44000, 43000, 45000, 45000],
                costCenters: [35000, 38000, 42000, 40000, 45000, 48000, 46000, 49000, 52000, 50000, 53000, 55000],
                revenueByCategory: [80000, 92000, 98000, 94000, 108000, 114000, 110000, 116000, 122000, 118000, 124000, 128000]
            },
            2021: {
                revenue: [90000, 105000, 112000, 108000, 125000, 132000, 128000, 135000, 142000, 138000, 145000, 150000],
                expenses: [70000, 77000, 83000, 80000, 90000, 95000, 93000, 97000, 103000, 100000, 105000, 110000],
                investments: [10000, 15000, 20000, 13000, 25000, 30000, 23000, 27000, 33000, 30000, 35000, 40000],
                margins: [20000, 28000, 29000, 28000, 35000, 37000, 35000, 38000, 39000, 38000, 40000, 40000],
                costCenters: [30000, 33000, 37000, 35000, 40000, 43000, 41000, 44000, 47000, 45000, 48000, 50000],
                revenueByCategory: [72000, 84000, 90000, 86000, 100000, 106000, 102000, 108000, 114000, 110000, 116000, 120000]
            }
        };

        const monthNames = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];

        // Fonction principale pour mettre à jour le graphique financier
        function updateFinancialChart() {
            const year1 = document.getElementById('year1Selector').value;
            const year2 = document.getElementById('year2Selector').value;

            // Calculer et afficher le ratio
            updateYearRatio(year1, year2);

            // Mettre à jour le graphique
            renderFinancialChart(year1, year2);

            // Mettre à jour les ratios et insights
            updateRatiosAndInsights(year1, year2);
        }

        // Fonction pour calculer et afficher le ratio entre deux années
        function updateYearRatio(year1, year2) {
            const data1 = financialData[year1];
            const data2 = financialData[year2];

            if (!data1 || !data2) return;

            // Calculer les totaux annuels pour le chiffre d'affaires
            const total1 = data1.revenue.reduce((sum, val) => sum + val, 0);
            const total2 = data2.revenue.reduce((sum, val) => sum + val, 0);

            const ratio = ((total1 - total2) / total2 * 100);
            const ratioElement = document.getElementById('yearRatio');

            ratioElement.textContent = `${ratio > 0 ? '+' : ''}${ratio.toFixed(1)}%`;
            ratioElement.style.color = ratio > 0 ? '#10b981' : ratio < 0 ? '#ef4444' : '#6b7280';
        }

        // Fonction pour rendre le graphique financier
        function renderFinancialChart(year1, year2) {
            const canvas = document.getElementById('financialChartCanvas');
            const ctx = canvas.getContext('2d');

            // Détruire le graphique existant s'il existe
            if (financialChart) {
                financialChart.destroy();
            }

            const datasets = [];
            const colors = {
                revenue: { year1: '#10b981', year2: '#34d399' },
                expenses: { year1: '#ef4444', year2: '#f87171' },
                investments: { year1: '#8b5cf6', year2: '#a78bfa' },
                margins: { year1: '#f59e0b', year2: '#fbbf24' },
                costCenters: { year1: '#6366f1', year2: '#818cf8' },
                revenueByCategory: { year1: '#14b8a6', year2: '#5eead4' }
            };

            // Ajouter les datasets selon les indicateurs sélectionnés
            const indicators = ['revenue', 'expenses', 'investments', 'margins', 'costCenters', 'revenueByCategory'];
            const indicatorNames = {
                revenue: 'Chiffre d\'Affaires',
                expenses: 'Charges d\'Exploitation',
                investments: 'Investissements',
                margins: 'Marges Bénéficiaires',
                costCenters: 'Coûts par Centre',
                revenueByCategory: 'Revenus par Catégorie'
            };

            indicators.forEach(indicator => {
                const checkbox = document.getElementById(`show${indicator.charAt(0).toUpperCase() + indicator.slice(1)}`);
                if (checkbox && checkbox.checked) {
                    // Données pour l'année 1
                    datasets.push({
                        label: `${indicatorNames[indicator]} ${year1}`,
                        data: financialData[year1][indicator],
                        borderColor: colors[indicator].year1,
                        backgroundColor: colors[indicator].year1 + '20',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        pointRadius: document.getElementById('showDataPoints').checked ? 6 : 0,
                        pointHoverRadius: 8
                    });

                    // Données pour l'année 2
                    datasets.push({
                        label: `${indicatorNames[indicator]} ${year2}`,
                        data: financialData[year2][indicator],
                        borderColor: colors[indicator].year2,
                        backgroundColor: colors[indicator].year2 + '20',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        borderDash: [5, 5],
                        pointRadius: document.getElementById('showDataPoints').checked ? 6 : 0,
                        pointHoverRadius: 8
                    });
                }
            });

            // Configuration du graphique
            financialChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthNames,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Comparaison Financière ${year1} vs ${year2}`,
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    const value = document.getElementById('showPercentages').checked
                                        ? (context.parsed.y / 1000).toFixed(1) + 'K€'
                                        : context.parsed.y.toLocaleString('fr-FR') + '€';
                                    return context.dataset.label + ': ' + value;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Mois'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Montant (€)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return document.getElementById('showPercentages').checked
                                        ? (value / 1000) + 'K€'
                                        : value.toLocaleString('fr-FR') + '€';
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // Fonction pour mettre à jour les ratios et insights
        function updateRatiosAndInsights(year1, year2) {
            updateAnnualRatios(year1, year2);
            updateTrendSummary(year1, year2);
            updateFinancialInsights(year1, year2);
        }

        // Fonction pour calculer et afficher les ratios annuels
        function updateAnnualRatios(year1, year2) {
            const data1 = financialData[year1];
            const data2 = financialData[year2];

            const ratiosContainer = document.getElementById('annualRatios');

            // Calculer les totaux annuels
            const totals1 = {
                revenue: data1.revenue.reduce((sum, val) => sum + val, 0),
                expenses: data1.expenses.reduce((sum, val) => sum + val, 0),
                investments: data1.investments.reduce((sum, val) => sum + val, 0),
                margins: data1.margins.reduce((sum, val) => sum + val, 0)
            };

            const totals2 = {
                revenue: data2.revenue.reduce((sum, val) => sum + val, 0),
                expenses: data2.expenses.reduce((sum, val) => sum + val, 0),
                investments: data2.investments.reduce((sum, val) => sum + val, 0),
                margins: data2.margins.reduce((sum, val) => sum + val, 0)
            };

            // Calculer les ratios
            const revenueRatio = ((totals1.revenue - totals2.revenue) / totals2.revenue * 100);
            const expenseRatio = ((totals1.expenses - totals2.expenses) / totals2.expenses * 100);
            const marginRatio = ((totals1.margins - totals2.margins) / totals2.margins * 100);
            const profitabilityRatio1 = (totals1.margins / totals1.revenue * 100);
            const profitabilityRatio2 = (totals2.margins / totals2.revenue * 100);

            ratiosContainer.innerHTML = `
                <div class="ratio-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="font-size: 0.875rem; color: #6b7280;">CA ${year1} vs ${year2}:</span>
                    <span style="font-weight: 600; color: ${revenueRatio > 0 ? '#10b981' : '#ef4444'};">
                        ${revenueRatio > 0 ? '+' : ''}${revenueRatio.toFixed(1)}%
                    </span>
                </div>
                <div class="ratio-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="font-size: 0.875rem; color: #6b7280;">Charges ${year1} vs ${year2}:</span>
                    <span style="font-weight: 600; color: ${expenseRatio < 0 ? '#10b981' : '#ef4444'};">
                        ${expenseRatio > 0 ? '+' : ''}${expenseRatio.toFixed(1)}%
                    </span>
                </div>
                <div class="ratio-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="font-size: 0.875rem; color: #6b7280;">Marges ${year1} vs ${year2}:</span>
                    <span style="font-weight: 600; color: ${marginRatio > 0 ? '#10b981' : '#ef4444'};">
                        ${marginRatio > 0 ? '+' : ''}${marginRatio.toFixed(1)}%
                    </span>
                </div>
                <div class="ratio-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="font-size: 0.875rem; color: #6b7280;">Rentabilité ${year1}:</span>
                    <span style="font-weight: 600; color: #3b82f6;">${profitabilityRatio1.toFixed(1)}%</span>
                </div>
                <div class="ratio-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0;">
                    <span style="font-size: 0.875rem; color: #6b7280;">Rentabilité ${year2}:</span>
                    <span style="font-weight: 600; color: #3b82f6;">${profitabilityRatio2.toFixed(1)}%</span>
                </div>
            `;
        }

        // Fonction pour analyser les tendances
        function updateTrendSummary(year1, year2) {
            const data1 = financialData[year1];
            const data2 = financialData[year2];

            const trendContainer = document.getElementById('trendSummary');

            // Analyser la croissance mensuelle moyenne
            const avgGrowth1 = calculateMonthlyGrowth(data1.revenue);
            const avgGrowth2 = calculateMonthlyGrowth(data2.revenue);

            // Identifier le meilleur mois
            const bestMonth1 = data1.revenue.indexOf(Math.max(...data1.revenue));
            const bestMonth2 = data2.revenue.indexOf(Math.max(...data2.revenue));

            // Analyser la volatilité
            const volatility1 = calculateVolatility(data1.revenue);
            const volatility2 = calculateVolatility(data2.revenue);

            trendContainer.innerHTML = `
                <div class="trend-item" style="margin-bottom: 0.75rem;">
                    <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Croissance mensuelle moyenne:</div>
                    <div style="font-weight: 600;">
                        ${year1}: <span style="color: ${avgGrowth1 > 0 ? '#10b981' : '#ef4444'};">${avgGrowth1 > 0 ? '+' : ''}${avgGrowth1.toFixed(1)}%</span><br>
                        ${year2}: <span style="color: ${avgGrowth2 > 0 ? '#10b981' : '#ef4444'};">${avgGrowth2 > 0 ? '+' : ''}${avgGrowth2.toFixed(1)}%</span>
                    </div>
                </div>
                <div class="trend-item" style="margin-bottom: 0.75rem;">
                    <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Meilleur mois:</div>
                    <div style="font-weight: 600;">
                        ${year1}: ${monthNames[bestMonth1]}<br>
                        ${year2}: ${monthNames[bestMonth2]}
                    </div>
                </div>
                <div class="trend-item">
                    <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Stabilité:</div>
                    <div style="font-weight: 600;">
                        ${year1}: <span style="color: ${volatility1 < 10 ? '#10b981' : volatility1 < 20 ? '#f59e0b' : '#ef4444'};">${getStabilityLabel(volatility1)}</span><br>
                        ${year2}: <span style="color: ${volatility2 < 10 ? '#10b981' : volatility2 < 20 ? '#f59e0b' : '#ef4444'};">${getStabilityLabel(volatility2)}</span>
                    </div>
                </div>
            `;
        }

        // Fonction pour générer des insights automatiques
        function updateFinancialInsights(year1, year2) {
            const data1 = financialData[year1];
            const data2 = financialData[year2];

            const insightsContainer = document.getElementById('financialInsights');
            const insights = [];

            // Calculer les totaux
            const revenue1 = data1.revenue.reduce((sum, val) => sum + val, 0);
            const revenue2 = data2.revenue.reduce((sum, val) => sum + val, 0);
            const expenses1 = data1.expenses.reduce((sum, val) => sum + val, 0);
            const expenses2 = data2.expenses.reduce((sum, val) => sum + val, 0);

            // Générer des insights
            const revenueGrowth = ((revenue1 - revenue2) / revenue2 * 100);
            if (revenueGrowth > 10) {
                insights.push({
                    type: 'positive',
                    text: `Excellente croissance du CA de ${revenueGrowth.toFixed(1)}% entre ${year2} et ${year1}`
                });
            } else if (revenueGrowth < -5) {
                insights.push({
                    type: 'negative',
                    text: `Baisse du CA de ${Math.abs(revenueGrowth).toFixed(1)}% à surveiller`
                });
            }

            const expenseGrowth = ((expenses1 - expenses2) / expenses2 * 100);
            if (expenseGrowth > revenueGrowth + 5) {
                insights.push({
                    type: 'warning',
                    text: `Les charges augmentent plus vite que le CA (+${expenseGrowth.toFixed(1)}%)`
                });
            }

            // Analyser la saisonnalité
            const q4Revenue1 = data1.revenue.slice(9, 12).reduce((sum, val) => sum + val, 0);
            const totalRevenue1 = revenue1;
            if ((q4Revenue1 / totalRevenue1) > 0.35) {
                insights.push({
                    type: 'info',
                    text: `Forte saisonnalité en fin d'année (${((q4Revenue1 / totalRevenue1) * 100).toFixed(1)}% du CA en Q4)`
                });
            }

            insightsContainer.innerHTML = insights.map(insight => `
                <div class="insight-item" style="padding: 0.5rem; margin-bottom: 0.5rem; border-radius: 0.375rem; background: ${getInsightColor(insight.type)};">
                    <div style="font-size: 0.875rem; color: #374151;">${insight.text}</div>
                </div>
            `).join('') || '<div style="color: #6b7280; font-style: italic;">Aucun insight particulier détecté</div>';
        }

        // Fonctions utilitaires
        function calculateMonthlyGrowth(data) {
            let totalGrowth = 0;
            let validMonths = 0;

            for (let i = 1; i < data.length; i++) {
                if (data[i-1] > 0) {
                    totalGrowth += ((data[i] - data[i-1]) / data[i-1] * 100);
                    validMonths++;
                }
            }

            return validMonths > 0 ? totalGrowth / validMonths : 0;
        }

        function calculateVolatility(data) {
            const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
            const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
            return Math.sqrt(variance) / mean * 100;
        }

        function getStabilityLabel(volatility) {
            if (volatility < 10) return 'Très stable';
            if (volatility < 20) return 'Stable';
            if (volatility < 30) return 'Modérée';
            return 'Volatile';
        }

        function getInsightColor(type) {
            switch (type) {
                case 'positive': return '#dcfce7';
                case 'negative': return '#fef2f2';
                case 'warning': return '#fef3c7';
                case 'info': return '#dbeafe';
                default: return '#f9fafb';
            }
        }

        // Fonctions d'export et de réinitialisation
        function exportChartData() {
            const year1 = document.getElementById('year1Selector').value;
            const year2 = document.getElementById('year2Selector').value;

            const exportData = {
                comparison: `${year1} vs ${year2}`,
                year1Data: financialData[year1],
                year2Data: financialData[year2],
                exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analyse_financiere_${year1}_vs_${year2}.json`;
            a.click();
            URL.revokeObjectURL(url);

            showAlert('Données exportées avec succès', 'success');
        }

        function resetChartView() {
            // Réinitialiser les sélecteurs
            document.getElementById('year1Selector').value = '2024';
            document.getElementById('year2Selector').value = '2023';

            // Réinitialiser les indicateurs
            document.getElementById('showRevenue').checked = true;
            document.getElementById('showExpenses').checked = true;
            document.getElementById('showInvestments').checked = false;
            document.getElementById('showMargins').checked = false;
            document.getElementById('showCostCenters').checked = false;
            document.getElementById('showRevenueByCategory').checked = false;

            // Réinitialiser les options
            document.getElementById('showTrendLines').checked = false;
            document.getElementById('showDataPoints').checked = true;
            document.getElementById('showPercentages').checked = false;

            // Mettre à jour le graphique
            updateFinancialChart();

            showAlert('Vue réinitialisée', 'info');
        }

        // Fonctions TCD (Tableau Croisé Dynamique)
        function generatePivotTable() {
            updatePivotTable();
        }

        function refreshPivotTable() {
            updatePivotTable();
            showAlert('TCD actualisé avec succès', 'success');
        }

        function updatePivotTable() {
            const rowField = document.getElementById('pivotRows').value;
            const columnField = document.getElementById('pivotColumns').value;
            const valueField = document.getElementById('pivotValues').value;
            const aggregation = document.getElementById('pivotAggregation').value;

            // Utiliser les données filtrées de la gestion budgétaire
            // Si aucun filtre n'est appliqué, utiliser toutes les données budgétaires
            let dataToAnalyze;

            // Utiliser les filtres d'analyse si disponibles, sinon les filtres budget
            let selectedCostCenters, selectedAnalyticCodes, selectedBudgetTypes;

            // Vérifier si on est dans l'onglet analyse et si les filtres d'analyse existent
            if (currentTab === 'analysis' && document.getElementById('analysisCostCenterItems')) {
                // Utiliser les filtres d'analyse
                selectedCostCenters = getSelectedFilterValues('analysisCostCenter');
                selectedAnalyticCodes = getSelectedFilterValues('analysisAnalyticCode');
                selectedBudgetTypes = getSelectedFilterValues('analysisBudgetType');
            } else if (currentTab === 'budget') {
                // Utiliser les filtres de la gestion budgétaire
                selectedCostCenters = getSelectedFilterValues('costCenter');
                selectedAnalyticCodes = getSelectedFilterValues('analyticCode');
                selectedBudgetTypes = getSelectedFilterValues('budgetType');
            } else {
                // Aucun filtre, utiliser toutes les données
                dataToAnalyze = budgetCategories;
            }

            // Appliquer les filtres si des sélections existent
            if (selectedCostCenters !== undefined) {
                dataToAnalyze = budgetCategories.filter(budget => {
                    let matches = true;

                    // Filtre centres de coûts
                    if (selectedCostCenters.length > 0 && !selectedCostCenters.includes(budget.costCenter)) {
                        matches = false;
                    }

                    // Filtre codes analytiques
                    if (selectedAnalyticCodes.length > 0 && !selectedAnalyticCodes.includes(budget.analyticCode)) {
                        matches = false;
                    }

                    // Filtre types de budget
                    if (selectedBudgetTypes.length > 0 && !selectedBudgetTypes.includes(budget.categoryType)) {
                        matches = false;
                    }

                    return matches;
                });
            }

            if (dataToAnalyze.length === 0) {
                document.getElementById('pivotTableBody').innerHTML = `
                    <tr><td colspan="100%" style="text-align: center; padding: 2rem;">Aucune donnée disponible pour le TCD</td></tr>
                `;
                return;
            }

            // Préparer les données selon le champ colonne
            let pivotData = {};
            let columnValues = new Set();

            dataToAnalyze.forEach(budget => {
                const rowValue = getFieldValue(budget, rowField);

                if (columnField === 'month') {
                    // Traitement spécial pour les mois
                    const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                                  'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];

                    months.forEach(month => {
                        if (budget.monthlyData && budget.monthlyData[month]) {
                            const monthData = budget.monthlyData[month];
                            const value = getMonthlyValue(monthData, valueField);

                            if (!pivotData[rowValue]) pivotData[rowValue] = {};
                            if (!pivotData[rowValue][month]) pivotData[rowValue][month] = [];

                            pivotData[rowValue][month].push(value);
                            columnValues.add(month);
                        }
                    });
                } else if (columnField === 'quarter') {
                    // Traitement pour les trimestres
                    const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
                    quarters.forEach(quarter => {
                        const value = getQuarterValue(budget, quarter, valueField);

                        if (!pivotData[rowValue]) pivotData[rowValue] = {};
                        if (!pivotData[rowValue][quarter]) pivotData[rowValue][quarter] = [];

                        pivotData[rowValue][quarter].push(value);
                        columnValues.add(quarter);
                    });
                } else {
                    // Traitement standard
                    const columnValue = getFieldValue(budget, columnField);
                    const value = getFieldValue(budget, valueField);

                    if (!pivotData[rowValue]) pivotData[rowValue] = {};
                    if (!pivotData[rowValue][columnValue]) pivotData[rowValue][columnValue] = [];

                    pivotData[rowValue][columnValue].push(value);
                    columnValues.add(columnValue);
                }
            });

            // Générer le tableau
            renderPivotTable(pivotData, Array.from(columnValues).sort(), aggregation, rowField, columnField, valueField);
        }

        function getFieldValue(budget, field) {
            switch (field) {
                case 'categoryType':
                    return budget.categoryType || 'N/A';
                case 'costCenter':
                    return budget.costCenterName || 'N/A';
                case 'analyticCode':
                    return budget.analyticCodeName || 'N/A';
                case 'department':
                    return budget.department || 'N/A';
                case 'responsible':
                    return budget.responsible || 'N/A';
                case 'forecast':
                    return budget.forecast || 0;
                case 'realized':
                    return budget.realized || 0;
                case 'variance':
                    return (budget.realized || 0) - (budget.forecast || 0);
                case 'percentage':
                    return budget.forecast > 0 ? ((budget.realized || 0) / budget.forecast * 100) : 0;
                default:
                    return 'N/A';
            }
        }

        function getMonthlyValue(monthData, valueField) {
            switch (valueField) {
                case 'forecast':
                    return monthData.forecast || 0;
                case 'realized':
                    return monthData.realized || 0;
                case 'variance':
                    return (monthData.realized || 0) - (monthData.forecast || 0);
                case 'percentage':
                    return monthData.forecast > 0 ? ((monthData.realized || 0) / monthData.forecast * 100) : 0;
                default:
                    return 0;
            }
        }

        function getQuarterValue(budget, quarter, valueField) {
            const quarterMonths = {
                'Q1': ['janvier', 'février', 'mars'],
                'Q2': ['avril', 'mai', 'juin'],
                'Q3': ['juillet', 'août', 'septembre'],
                'Q4': ['octobre', 'novembre', 'décembre']
            };

            let total = 0;
            quarterMonths[quarter].forEach(month => {
                if (budget.monthlyData && budget.monthlyData[month]) {
                    total += getMonthlyValue(budget.monthlyData[month], valueField);
                }
            });

            return total;
        }

        function renderPivotTable(pivotData, columnValues, aggregation, rowField, columnField, valueField) {
            const thead = document.getElementById('pivotTableHead');
            const tbody = document.getElementById('pivotTableBody');

            // Générer l'en-tête
            let headerHTML = `
                <tr>
                    <th style="position: sticky; left: 0; background: #f9fafb; z-index: 10;">${getFieldLabel(rowField)}</th>
            `;

            columnValues.forEach(col => {
                headerHTML += `<th style="text-align: center;">${col}</th>`;
            });

            headerHTML += `<th style="text-align: center; background: #fef3e2;">Total</th></tr>`;
            thead.innerHTML = headerHTML;

            // Générer le corps
            let bodyHTML = '';
            let grandTotal = 0;
            const columnTotals = {};

            Object.keys(pivotData).forEach(rowValue => {
                let rowHTML = `<td style="position: sticky; left: 0; background: white; z-index: 10; font-weight: 600;">${rowValue}</td>`;
                let rowTotal = 0;

                columnValues.forEach(colValue => {
                    const values = pivotData[rowValue][colValue] || [];
                    const aggregatedValue = aggregateValues(values, aggregation);

                    rowHTML += `<td style="text-align: right;">${formatValue(aggregatedValue, valueField)}</td>`;
                    rowTotal += aggregatedValue;

                    if (!columnTotals[colValue]) columnTotals[colValue] = 0;
                    columnTotals[colValue] += aggregatedValue;
                });

                rowHTML += `<td style="text-align: right; background: #fef3e2; font-weight: 600;">${formatValue(rowTotal, valueField)}</td>`;
                bodyHTML += `<tr>${rowHTML}</tr>`;
                grandTotal += rowTotal;
            });

            // Ligne de total
            let totalRowHTML = `<td style="position: sticky; left: 0; background: #f9fafb; z-index: 10; font-weight: 600;">TOTAL</td>`;
            columnValues.forEach(colValue => {
                totalRowHTML += `<td style="text-align: right; background: #f9fafb; font-weight: 600;">${formatValue(columnTotals[colValue] || 0, valueField)}</td>`;
            });
            totalRowHTML += `<td style="text-align: right; background: #fed7aa; font-weight: 600;">${formatValue(grandTotal, valueField)}</td>`;

            bodyHTML += `<tr style="border-top: 2px solid #e5e7eb;">${totalRowHTML}</tr>`;
            tbody.innerHTML = bodyHTML;
        }

        function getFieldLabel(field) {
            const labels = {
                'categoryType': 'Type de Catégorie',
                'costCenter': 'Centre de Coûts',
                'analyticCode': 'Code Analytique',
                'department': 'Département',
                'responsible': 'Responsable'
            };
            return labels[field] || field;
        }

        function aggregateValues(values, aggregation) {
            if (values.length === 0) return 0;

            switch (aggregation) {
                case 'sum':
                    return values.reduce((sum, val) => sum + val, 0);
                case 'average':
                    return values.reduce((sum, val) => sum + val, 0) / values.length;
                case 'count':
                    return values.length;
                case 'min':
                    return Math.min(...values);
                case 'max':
                    return Math.max(...values);
                default:
                    return values.reduce((sum, val) => sum + val, 0);
            }
        }

        function formatValue(value, valueField) {
            if (valueField === 'percentage') {
                return value.toFixed(1) + '%';
            } else if (valueField === 'count') {
                return value.toString();
            } else {
                return value.toLocaleString() + '€';
            }
        }

        // Fonctions d'export
        function exportData() {
            openModal('exportModal');
        }

        function exportToFormat(format) {
            const exportAccounts = document.getElementById('exportAccounts').checked;
            const exportMovements = document.getElementById('exportMovements').checked;
            const exportBudgets = document.getElementById('exportBudgets').checked;

            let data = {};

            if (exportAccounts) data.accounts = accounts;
            if (exportMovements) data.movements = movements;
            if (exportBudgets) data.budgetCategories = budgetCategories;

            switch (format) {
                case 'csv':
                    exportToCSV(data);
                    break;
                case 'excel':
                    exportToExcel(data);
                    break;
                case 'json':
                    exportToJSON(data);
                    break;
                case 'pdf':
                    exportToPDF(data);
                    break;
            }

            closeModal('exportModal');
        }

        function exportToCSV(data) {
            let csvContent = '';

            if (data.accounts) {
                csvContent += 'COMPTES BANCAIRES\n';
                csvContent += 'ID,Nom,Banque,Type,Solde,Seuil Alerte,IBAN\n';
                data.accounts.forEach(acc => {
                    csvContent += `${acc.id},"${acc.name}","${acc.bank}","${acc.type}",${acc.balance},${acc.alertThreshold},"${acc.iban}"\n`;
                });
                csvContent += '\n';
            }

            if (data.movements) {
                csvContent += 'MOUVEMENTS DE TRESORERIE\n';
                csvContent += 'ID,Date,Libellé,Compte,Type,Montant,Référence,Catégorie\n';
                data.movements.forEach(mov => {
                    csvContent += `${mov.id},"${mov.date}","${mov.label}","${mov.accountName}","${mov.type}",${mov.amount},"${mov.reference || ''}","${mov.category || ''}"\n`;
                });
                csvContent += '\n';
            }

            if (data.budgetCategories) {
                csvContent += 'BUDGETS PAR CATEGORIE\n';
                csvContent += 'ID,Catégorie,Type,Prévisionnel,Réalisé,Écart,Pourcentage,Département,Responsable,Date Création,Date Modification\n';
                data.budgetCategories.forEach(budget => {
                    const variance = budget.realized - budget.forecast;
                    const percentage = budget.forecast > 0 ? (budget.realized / budget.forecast * 100) : 0;
                    csvContent += `${budget.id},"${budget.categoryName}","${budget.categoryType}",${budget.forecast},${budget.realized},${variance},${percentage.toFixed(1)},"${budget.department}","${budget.responsible}","${formatDate(budget.createdDate)}","${formatDate(budget.modifiedDate)}"\n`;
                });
            }

            downloadFile(csvContent, 'erp-finance-export.csv', 'text/csv');
        }

        function exportToJSON(data) {
            const jsonContent = JSON.stringify(data, null, 2);
            downloadFile(jsonContent, 'erp-finance-export.json', 'application/json');
        }

        function exportToExcel(data) {
            // Version simplifiée - génère un CSV avec extension .xls
            let content = '';

            if (data.accounts) {
                content += 'COMPTES BANCAIRES\t\t\t\t\t\t\n';
                content += 'ID\tNom\tBanque\tType\tSolde\tSeuil Alerte\tIBAN\n';
                data.accounts.forEach(acc => {
                    content += `${acc.id}\t${acc.name}\t${acc.bank}\t${acc.type}\t${acc.balance}\t${acc.alertThreshold}\t${acc.iban}\n`;
                });
                content += '\n';
            }

            if (data.movements) {
                content += 'MOUVEMENTS DE TRESORERIE\t\t\t\t\t\t\t\n';
                content += 'ID\tDate\tLibellé\tCompte\tType\tMontant\tRéférence\tCatégorie\n';
                data.movements.forEach(mov => {
                    content += `${mov.id}\t${mov.date}\t${mov.label}\t${mov.accountName}\t${mov.type}\t${mov.amount}\t${mov.reference || ''}\t${mov.category || ''}\n`;
                });
                content += '\n';
            }

            if (data.budgetCategories) {
                content += 'BUDGETS PAR CATEGORIE\t\t\t\t\t\t\t\t\t\t\n';
                content += 'ID\tCatégorie\tType\tPrévisionnel\tRéalisé\tÉcart\tPourcentage\tDépartement\tResponsable\tDate Création\tDate Modification\n';
                data.budgetCategories.forEach(budget => {
                    const variance = budget.realized - budget.forecast;
                    const percentage = budget.forecast > 0 ? (budget.realized / budget.forecast * 100) : 0;
                    content += `${budget.id}\t${budget.categoryName}\t${budget.categoryType}\t${budget.forecast}\t${budget.realized}\t${variance}\t${percentage.toFixed(1)}%\t${budget.department}\t${budget.responsible}\t${formatDate(budget.createdDate)}\t${formatDate(budget.modifiedDate)}\n`;
                });
            }

            downloadFile(content, 'erp-finance-export.xls', 'application/vnd.ms-excel');
        }

        function exportToPDF(data) {
            // Génère un rapport HTML qui peut être imprimé en PDF
            let htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Export ERP Finance - ${new Date().toLocaleDateString('fr-FR')}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 2rem; }
                        h1 { color: #f97316; text-align: center; }
                        h2 { color: #374151; margin-top: 2rem; }
                        table { width: 100%; border-collapse: collapse; margin: 1rem 0; }
                        th, td { border: 1px solid #e5e7eb; padding: 0.5rem; text-align: left; }
                        th { background: #f9fafb; font-weight: 600; }
                        .amount-positive { color: #10b981; }
                        .amount-negative { color: #ef4444; }
                        .amount-warning { color: #f59e0b; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <h1>📊 Export Données Financières ERP HUB</h1>
                    <p style="text-align: center; color: #6b7280;">Généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p>
            `;

            if (data.accounts) {
                htmlContent += `
                    <h2>🏦 Comptes Bancaires</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Banque</th>
                                <th>Type</th>
                                <th>Solde</th>
                                <th>Seuil Alerte</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                data.accounts.forEach(acc => {
                    const status = getAccountStatus(acc);
                    htmlContent += `
                        <tr>
                            <td>${acc.id}</td>
                            <td>${acc.name}</td>
                            <td>${acc.bank}</td>
                            <td>${acc.type}</td>
                            <td class="${status.class}">${acc.balance.toLocaleString()}€</td>
                            <td>${acc.alertThreshold.toLocaleString()}€</td>
                        </tr>
                    `;
                });
                htmlContent += '</tbody></table>';
            }

            if (data.budgetCategories) {
                htmlContent += `
                    <h2>💰 Budgets par Catégorie</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Catégorie</th>
                                <th>Type</th>
                                <th>Prévisionnel</th>
                                <th>Réalisé</th>
                                <th>Écart</th>
                                <th>%</th>
                                <th>Département</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                data.budgetCategories.forEach(budget => {
                    const variance = budget.realized - budget.forecast;
                    const percentage = budget.forecast > 0 ? (budget.realized / budget.forecast * 100) : 0;
                    let varianceClass = 'amount-positive';
                    if (budget.categoryType === 'revenue') {
                        varianceClass = variance >= 0 ? 'amount-positive' : 'amount-negative';
                    } else {
                        varianceClass = variance <= 0 ? 'amount-positive' : 'amount-negative';
                    }

                    htmlContent += `
                        <tr>
                            <td>${budget.categoryName}</td>
                            <td>${budget.categoryType}</td>
                            <td>${budget.forecast.toLocaleString()}€</td>
                            <td>${budget.realized.toLocaleString()}€</td>
                            <td class="${varianceClass}">${variance >= 0 ? '+' : ''}${variance.toLocaleString()}€</td>
                            <td>${percentage.toFixed(1)}%</td>
                            <td>${budget.department}</td>
                        </tr>
                    `;
                });
                htmlContent += '</tbody></table>';
            }

            htmlContent += `
                    <div style="margin-top: 2rem; text-align: center;">
                        <button onclick="window.print()" style="padding: 0.5rem 1rem; background: #f97316; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                            Imprimer / Sauvegarder en PDF
                        </button>
                    </div>
                </body>
                </html>
            `;

            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(htmlContent);
            reportWindow.document.close();
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert(`Fichier ${filename} téléchargé avec succès`, 'success');
        }

        // Gestion du formulaire mouvement
        document.getElementById('movementForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const accountId = formData.get('movementAccount');
            const account = accounts.find(a => a.id === accountId);

            if (!account) {
                showAlert('Compte non trouvé', 'error', 'movementModalAlertContainer');
                isLoading = false;
                return;
            }

            const movementData = {
                date: formData.get('movementDate'),
                accountId: accountId,
                accountName: account.name,
                type: formData.get('movementType'),
                amount: parseFloat(formData.get('movementAmount')),
                label: formData.get('movementLabel'),
                reference: formData.get('movementReference'),
                category: formData.get('movementCategory'),
                notes: formData.get('movementNotes')
            };

            try {
                if (editingMovementId) {
                    // Modification
                    const index = movements.findIndex(m => m.id === editingMovementId);
                    if (index !== -1) {
                        movements[index] = { ...movements[index], ...movementData };
                        showAlert('Mouvement modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newMovement = {
                        id: `MOV-${String(movements.length + 1).padStart(3, '0')}`,
                        ...movementData
                    };
                    movements.push(newMovement);
                    showAlert('Mouvement créé avec succès', 'success');
                }

                renderMovementsTable();
                updateStats();
                closeModal('movementModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'movementModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Gestion du formulaire budget
        document.getElementById('budgetForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (isLoading) return;
            isLoading = true;

            const formData = new FormData(e.target);
            const categoryValue = formData.get('budgetCategory');

            // Trouver le nom de la catégorie
            const categorySelect = document.getElementById('budgetCategory');
            const selectedOption = categorySelect.options[categorySelect.selectedIndex];
            const categoryName = selectedOption ? selectedOption.textContent : '';

            // Déterminer le type de catégorie
            let categoryType = 'expense';
            if (categoryValue.includes('revenue')) categoryType = 'revenue';
            else if (categoryValue.includes('investment')) categoryType = 'investment';
            else if (categoryValue.includes('interest') || categoryValue.includes('bank_fees') || categoryValue.includes('insurance')) categoryType = 'financial';
            else if (categoryValue.includes('taxes') || categoryValue.includes('provisions') || categoryValue.includes('depreciation')) categoryType = 'exceptional';

            // Récupérer les informations du centre de coûts et code analytique
            const costCenterId = formData.get('budgetCostCenter');
            const analyticCodeId = formData.get('budgetAnalyticCode');

            const costCenter = costCenters.find(cc => cc.id === costCenterId);
            const analyticCode = analyticCodes.find(ac => ac.id === analyticCodeId);

            const budgetData = {
                category: categoryValue,
                categoryName: categoryName,
                categoryType: categoryType,
                period: formData.get('budgetPeriod'),
                forecast: parseFloat(formData.get('budgetForecast')),
                realized: parseFloat(formData.get('budgetRealized')) || 0,
                department: formData.get('budgetDepartment') || 'General',
                responsible: formData.get('budgetResponsible') || '',
                costCenter: costCenterId,
                costCenterName: costCenter ? costCenter.name : '',
                analyticCode: analyticCodeId,
                analyticCodeName: analyticCode ? analyticCode.name : '',
                notes: formData.get('budgetNotes') || '',
                createdDate: new Date().toISOString(),
                modifiedDate: new Date().toISOString(),
                monthlyData: generateMonthlyData(parseFloat(formData.get('budgetForecast')), parseFloat(formData.get('budgetRealized')) || 0)
            };

            try {
                if (editingBudgetId) {
                    // Modification
                    const index = budgetCategories.findIndex(b => b.id === editingBudgetId);
                    if (index !== -1) {
                        budgetCategories[index] = {
                            ...budgetCategories[index],
                            ...budgetData,
                            modifiedDate: new Date().toISOString()
                        };
                        showAlert('Budget modifié avec succès', 'success');
                    }
                } else {
                    // Création
                    const newBudget = {
                        id: `BUD-${String(budgetCategories.length + 1).padStart(3, '0')}`,
                        ...budgetData
                    };
                    budgetCategories.push(newBudget);
                    showAlert('Budget créé avec succès', 'success');
                }

                renderBudgetCategoriesTable();
                updateBudgetStats();
                updateStats();
                renderBudgetChart();
                renderMonthlyChart();
                closeModal('budgetModal');

            } catch (error) {
                console.error('Erreur:', error);
                showAlert('Erreur lors de l\'enregistrement', 'error', 'budgetModalAlertContainer');
            } finally {
                isLoading = false;
            }
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (localStorage.getItem('isAuthenticated') !== 'true') {
                alert('Vous devez être connecté pour accéder à cette page');
                window.location.href = 'login-demo.html';
                return false;
            }
            return true;
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadFinanceData();

                // Initialiser le bouton de graphique en barres comme actif
                setTimeout(() => {
                    if (document.getElementById('barChartBtn')) {
                        toggleChartType('bar');
                    }

                    // Initialiser le nouveau graphique financier
                    if (document.getElementById('financialChartCanvas')) {
                        updateFinancialChart();
                    }
                }, 100);
            }
        });
    </script>
</body>
</html>
