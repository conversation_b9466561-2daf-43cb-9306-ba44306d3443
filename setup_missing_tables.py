#!/usr/bin/env python3
"""
Script pour créer les tables manquantes dans PostgreSQL
"""

import psycopg2
import sys
import os

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'erp_hub',
    'user': 'erp_admin',
    'password': 'erp_secure_2024'
}

def create_missing_tables():
    """Créer les tables manquantes pour Sales et Stocks"""
    
    # SQL pour créer les tables manquantes
    sql_commands = [
        # Table customers (si elle n'existe pas déjà)
        """
        CREATE TABLE IF NOT EXISTS customers (
            id VARCHAR(50) PRIMARY KEY,
            customer_code VARCHAR(20) UNIQUE NOT NULL,
            company_name VARCHAR(200) NOT NULL,
            contact_person VARCHAR(100),
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(100),
            postal_code VARCHAR(20),
            country VARCHAR(100),
            industry VARCHAR(100),
            customer_type VARCHAR(20) DEFAULT 'client',
            credit_limit DECIMAL(15,2) DEFAULT 0,
            payment_terms INTEGER DEFAULT 30,
            sales_rep_id VARCHAR(50),
            status VARCHAR(20) DEFAULT 'active',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """,
        
        # Table opportunities
        """
        CREATE TABLE IF NOT EXISTS opportunities (
            id VARCHAR(50) PRIMARY KEY,
            customer_id VARCHAR(50),
            title VARCHAR(200) NOT NULL,
            description TEXT,
            value DECIMAL(15,2),
            probability DECIMAL(5,2),
            stage VARCHAR(50),
            expected_close_date DATE,
            actual_close_date DATE,
            sales_rep_id VARCHAR(50),
            source VARCHAR(100),
            competitor VARCHAR(100),
            next_action TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (sales_rep_id) REFERENCES employees(id)
        );
        """,
        
        # Table orders
        """
        CREATE TABLE IF NOT EXISTS orders (
            id VARCHAR(50) PRIMARY KEY,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            customer_id VARCHAR(50),
            opportunity_id VARCHAR(50),
            order_date DATE,
            delivery_date DATE,
            status VARCHAR(50),
            total_amount DECIMAL(15,2),
            tax_amount DECIMAL(15,2),
            discount_amount DECIMAL(15,2),
            shipping_address TEXT,
            billing_address TEXT,
            payment_method VARCHAR(50),
            payment_status VARCHAR(50),
            sales_rep_id VARCHAR(50),
            notes TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (opportunity_id) REFERENCES opportunities(id),
            FOREIGN KEY (sales_rep_id) REFERENCES employees(id)
        );
        """,
        
        # Table quotes
        """
        CREATE TABLE IF NOT EXISTS quotes (
            id VARCHAR(50) PRIMARY KEY,
            quote_number VARCHAR(50) UNIQUE NOT NULL,
            client_id VARCHAR(50),
            client_name VARCHAR(200),
            quote_date DATE,
            validity_date DATE,
            amount_ht DECIMAL(15,2),
            amount_tva DECIMAL(15,2),
            amount_ttc DECIMAL(15,2),
            status VARCHAR(50),
            description TEXT,
            file_path VARCHAR(500),
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """,
        
        # Table suppliers
        """
        CREATE TABLE IF NOT EXISTS suppliers (
            id VARCHAR(50) PRIMARY KEY,
            supplier_code VARCHAR(20) UNIQUE NOT NULL,
            company_name VARCHAR(200) NOT NULL,
            contact_person VARCHAR(100),
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(100),
            postal_code VARCHAR(20),
            country VARCHAR(100),
            industry VARCHAR(100),
            payment_terms INTEGER DEFAULT 30,
            credit_rating VARCHAR(10),
            preferred BOOLEAN DEFAULT FALSE,
            status VARCHAR(20) DEFAULT 'active',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """,
        
        # Table warehouses
        """
        CREATE TABLE IF NOT EXISTS warehouses (
            id VARCHAR(50) PRIMARY KEY,
            warehouse_code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            postal_code VARCHAR(20),
            country VARCHAR(100),
            manager_id VARCHAR(50),
            capacity DECIMAL(15,2),
            current_utilization DECIMAL(15,2),
            warehouse_type VARCHAR(50),
            status VARCHAR(20) DEFAULT 'active',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (manager_id) REFERENCES employees(id)
        );
        """,
        
        # Table products
        """
        CREATE TABLE IF NOT EXISTS products (
            id VARCHAR(50) PRIMARY KEY,
            product_code VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category VARCHAR(100),
            brand VARCHAR(100),
            unit_of_measure VARCHAR(20),
            weight DECIMAL(10,3),
            dimensions VARCHAR(100),
            cost_price DECIMAL(15,2),
            selling_price DECIMAL(15,2),
            min_stock_level INTEGER,
            max_stock_level INTEGER,
            reorder_point INTEGER,
            supplier_id VARCHAR(50),
            barcode VARCHAR(100),
            status VARCHAR(20) DEFAULT 'active',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        );
        """,
        
        # Table inventory
        """
        CREATE TABLE IF NOT EXISTS inventory (
            id VARCHAR(50) PRIMARY KEY,
            product_id VARCHAR(50),
            warehouse_id VARCHAR(50),
            quantity_on_hand INTEGER DEFAULT 0,
            quantity_reserved INTEGER DEFAULT 0,
            quantity_available INTEGER GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
            last_count_date DATE,
            last_movement_date TIMESTAMP,
            location VARCHAR(100),
            batch_number VARCHAR(100),
            expiry_date DATE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
        );
        """,
        
        # Table stock_movements
        """
        CREATE TABLE IF NOT EXISTS stock_movements (
            id VARCHAR(50) PRIMARY KEY,
            product_id VARCHAR(50),
            warehouse_id VARCHAR(50),
            movement_type VARCHAR(20),
            quantity INTEGER,
            reference_type VARCHAR(50),
            reference_id VARCHAR(50),
            reason VARCHAR(200),
            cost_per_unit DECIMAL(15,2),
            total_cost DECIMAL(15,2),
            performed_by VARCHAR(50),
            movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
            FOREIGN KEY (performed_by) REFERENCES employees(id)
        );
        """,
        
        # Table purchase_orders
        """
        CREATE TABLE IF NOT EXISTS purchase_orders (
            id VARCHAR(50) PRIMARY KEY,
            po_number VARCHAR(50) UNIQUE NOT NULL,
            supplier_id VARCHAR(50),
            request_id VARCHAR(50),
            order_date DATE,
            expected_delivery_date DATE,
            status VARCHAR(50),
            total_amount DECIMAL(15,2),
            tax_amount DECIMAL(15,2),
            shipping_cost DECIMAL(15,2),
            payment_terms VARCHAR(100),
            delivery_address TEXT,
            buyer_id VARCHAR(50),
            notes TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
            FOREIGN KEY (buyer_id) REFERENCES employees(id)
        );
        """,
        
        # Table shipments
        """
        CREATE TABLE IF NOT EXISTS shipments (
            id VARCHAR(50) PRIMARY KEY,
            shipment_number VARCHAR(50) UNIQUE NOT NULL,
            order_id VARCHAR(50),
            warehouse_id VARCHAR(50),
            carrier VARCHAR(100),
            tracking_number VARCHAR(100),
            ship_date DATE,
            expected_delivery_date DATE,
            actual_delivery_date DATE,
            status VARCHAR(50),
            shipping_cost DECIMAL(15,2),
            weight DECIMAL(10,3),
            dimensions VARCHAR(100),
            destination_address TEXT,
            special_instructions TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id),
            FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
        );
        """,
        
        # Table interactions
        """
        CREATE TABLE IF NOT EXISTS interactions (
            id VARCHAR(50) PRIMARY KEY,
            customer_id VARCHAR(50),
            contact_id VARCHAR(50),
            interaction_type VARCHAR(50),
            subject VARCHAR(200),
            description TEXT,
            interaction_date TIMESTAMP,
            duration_minutes INTEGER,
            outcome VARCHAR(200),
            next_action TEXT,
            next_action_date DATE,
            assigned_to VARCHAR(50),
            priority VARCHAR(20),
            status VARCHAR(50),
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (contact_id) REFERENCES contacts(id),
            FOREIGN KEY (assigned_to) REFERENCES employees(id)
        );
        """
    ]
    
    try:
        # Connexion à la base de données
        print("🔗 Connexion à PostgreSQL...")
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("📊 Création des tables manquantes...")
        
        # Exécuter chaque commande SQL
        for i, sql in enumerate(sql_commands, 1):
            try:
                cursor.execute(sql)
                table_name = sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' ')[0]
                print(f"✅ Table {table_name} créée/vérifiée ({i}/{len(sql_commands)})")
            except Exception as e:
                print(f"❌ Erreur lors de la création d'une table: {e}")
                continue
        
        # Valider les changements
        conn.commit()
        
        # Vérifier les tables créées
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        print(f"\n📋 Tables disponibles dans la base de données ({len(tables)}) :")
        for table in tables:
            print(f"   - {table[0]}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Configuration terminée avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de connexion à PostgreSQL: {e}")
        print("Vérifiez que PostgreSQL est démarré et que les paramètres de connexion sont corrects.")
        return False

if __name__ == "__main__":
    print("🚀 Configuration des tables ERP HUB...")
    success = create_missing_tables()
    
    if success:
        print("\n✅ Toutes les tables sont maintenant disponibles !")
        print("🔗 Vous pouvez maintenant tester les endpoints API.")
    else:
        print("\n❌ Échec de la configuration.")
        sys.exit(1)
