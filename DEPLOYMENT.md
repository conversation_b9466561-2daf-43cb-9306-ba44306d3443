# 🚀 Guide de Déploiement en Production - ERP HUB

## 📋 Prérequis

### Serveur de Production
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 8GB (16GB recommandé)
- **CPU**: Minimum 4 cores (8 cores recommandé)
- **Stockage**: Minimum 100GB SSD
- **R<PERSON>eau**: Connexion internet stable

### Logiciels Requis
```bash
# Docker & Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Git
sudo apt update && sudo apt install -y git curl

# Nginx (optionnel, déj<PERSON> dans Docker)
sudo apt install -y nginx
```

## 🔧 Configuration Initiale

### 1. Clonage du Projet
```bash
git clone https://github.com/votre-org/erp-hub.git
cd erp-hub
```

### 2. Configuration des Variables d'Environnement
```bash
# Copier le template
cp .env.production .env.local

# Éditer les variables (IMPORTANT!)
nano .env.local
```

**Variables Critiques à Modifier:**
```env
# Sécurité
SECRET_KEY=votre-clé-secrète-très-longue-et-complexe
JWT_SECRET_KEY=votre-clé-jwt-différente-de-secret-key

# Base de données
POSTGRES_PASSWORD=mot-de-passe-postgres-très-fort
REDIS_PASSWORD=mot-de-passe-redis-très-fort

# Domaine
ALLOWED_HOSTS=votre-domaine.com,www.votre-domaine.com
CORS_ALLOWED_ORIGINS=https://votre-domaine.com,https://www.votre-domaine.com

# Email
EMAIL_HOST=smtp.votre-provider.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-email

# IA (optionnel)
OPENAI_API_KEY=votre-clé-openai
```

### 3. Configuration SSL (Production)
```bash
# Avec Let's Encrypt (recommandé)
sudo apt install -y certbot python3-certbot-nginx

# Obtenir les certificats
sudo certbot --nginx -d votre-domaine.com -d www.votre-domaine.com

# Copier les certificats pour Docker
sudo mkdir -p ./ssl
sudo cp /etc/letsencrypt/live/votre-domaine.com/fullchain.pem ./ssl/cert.pem
sudo cp /etc/letsencrypt/live/votre-domaine.com/privkey.pem ./ssl/key.pem
sudo chown -R $USER:$USER ./ssl
```

## 🚀 Déploiement

### Déploiement Automatique (Recommandé)
```bash
# Rendre le script exécutable
chmod +x deploy.sh

# Déploiement complet
./deploy.sh production
```

### Déploiement Manuel
```bash
# 1. Construction des images
docker-compose -f docker-compose.prod.yml build

# 2. Démarrage des services
docker-compose -f docker-compose.prod.yml up -d

# 3. Migrations
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# 4. Collecte des fichiers statiques
docker-compose -f docker-compose.prod.yml exec backend python manage.py collectstatic --noinput

# 5. Création du superutilisateur
docker-compose -f docker-compose.prod.yml exec backend python manage.py createsuperuser
```

## 🔍 Vérification du Déploiement

### Tests de Santé
```bash
# Backend API
curl -f http://localhost:8000/api/health/

# Frontend
curl -f http://localhost:3000

# Base de données
docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U erp_user_prod
```

### Accès aux Services
- **Frontend**: http://votre-domaine.com
- **API Backend**: http://votre-domaine.com/api
- **Admin Django**: http://votre-domaine.com/admin
- **Documentation API**: http://votre-domaine.com/docs
- **Monitoring Grafana**: http://votre-domaine.com:3001
- **Prometheus**: http://votre-domaine.com:9090

## 📊 Monitoring et Maintenance

### Logs
```bash
# Tous les services
docker-compose -f docker-compose.prod.yml logs -f

# Service spécifique
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f postgres
```

### Métriques
- **Grafana**: Interface de monitoring avec dashboards
- **Prometheus**: Collecte des métriques
- **Health Checks**: Vérifications automatiques

### Sauvegarde
```bash
# Sauvegarde complète
./scripts/backup.sh full

# Sauvegarde incrémentale
./scripts/backup.sh incremental

# Sauvegarde programmée (crontab)
0 2 * * * /path/to/erp-hub/scripts/backup.sh full
0 */6 * * * /path/to/erp-hub/scripts/backup.sh incremental
```

## 🔄 Mise à Jour

### Mise à Jour de Code
```bash
# 1. Sauvegarde
./scripts/backup.sh full

# 2. Récupération du nouveau code
git pull origin main

# 3. Redéploiement
./deploy.sh production
```

### Mise à Jour des Dépendances
```bash
# Reconstruction complète
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

## 🛡️ Sécurité

### Pare-feu
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### Certificats SSL
```bash
# Renouvellement automatique
sudo crontab -e
# Ajouter: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Sauvegardes Sécurisées
```bash
# Chiffrement des sauvegardes
gpg --symmetric --cipher-algo AES256 backup_file.sql.gz
```

## 🚨 Dépannage

### Problèmes Courants

#### Service ne démarre pas
```bash
# Vérifier les logs
docker-compose -f docker-compose.prod.yml logs service_name

# Vérifier l'état
docker-compose -f docker-compose.prod.yml ps
```

#### Base de données inaccessible
```bash
# Redémarrer PostgreSQL
docker-compose -f docker-compose.prod.yml restart postgres

# Vérifier la connectivité
docker-compose -f docker-compose.prod.yml exec postgres pg_isready
```

#### Problème de permissions
```bash
# Corriger les permissions
sudo chown -R $USER:$USER ./logs ./media
chmod -R 755 ./logs ./media
```

#### Mémoire insuffisante
```bash
# Vérifier l'utilisation
docker stats

# Ajuster les ressources dans docker-compose.prod.yml
```

### Commandes Utiles
```bash
# Redémarrage complet
docker-compose -f docker-compose.prod.yml restart

# Nettoyage
docker system prune -f
docker volume prune -f

# Sauvegarde d'urgence
docker exec erp_postgres_prod pg_dump -U erp_user_prod erp_hub_prod > emergency_backup.sql
```

## 📞 Support

### Logs Importants
- **Application**: `./logs/erp_hub.log`
- **Erreurs**: `./logs/erp_hub_errors.log`
- **Nginx**: `docker-compose logs nginx`
- **PostgreSQL**: `docker-compose logs postgres`

### Monitoring
- **Grafana**: Dashboards de performance
- **Prometheus**: Métriques détaillées
- **Health Checks**: Status des services

### Contact
- **Documentation**: [Wiki du projet]
- **Issues**: [GitHub Issues]
- **Support**: <EMAIL>

---

## ✅ Checklist de Déploiement

- [ ] Serveur configuré avec les prérequis
- [ ] Variables d'environnement configurées
- [ ] Certificats SSL installés
- [ ] Déploiement exécuté avec succès
- [ ] Tests de santé passés
- [ ] Superutilisateur créé
- [ ] Monitoring configuré
- [ ] Sauvegardes programmées
- [ ] Pare-feu configuré
- [ ] Documentation équipe mise à jour

**🎉 Félicitations ! ERP HUB est maintenant en production !**
