#!/bin/bash
# 🔒 GÉNÉRATION DE CERTIFICATS SSL POUR ERP HUB
# Script pour créer des certificats SSL auto-signés pour le développement

set -e

echo "🔒 Génération des certificats SSL pour ERP HUB..."

# C<PERSON>er le répertoire SSL s'il n'existe pas
mkdir -p ssl/certs
mkdir -p ssl/private

# Configuration
DOMAIN="localhost"
COUNTRY="FR"
STATE="France"
CITY="Paris"
ORGANIZATION="ERP HUB"
ORGANIZATIONAL_UNIT="IT Department"
EMAIL="<EMAIL>"

# Fichiers de sortie
PRIVATE_KEY="ssl/private/erp-hub.key"
CERTIFICATE="ssl/certs/erp-hub.crt"
CSR="ssl/certs/erp-hub.csr"
CONFIG_FILE="ssl/openssl.cnf"

echo "📝 Création du fichier de configuration OpenSSL..."

# <PERSON><PERSON><PERSON> le fichier de configuration OpenSSL
cat > $CONFIG_FILE << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=$COUNTRY
ST=$STATE
L=$CITY
O=$ORGANIZATION
OU=$ORGANIZATIONAL_UNIT
emailAddress=$EMAIL
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = erp-hub.local
DNS.4 = *.erp-hub.local
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

echo "🔑 Génération de la clé privée..."
openssl genrsa -out $PRIVATE_KEY 2048

echo "📋 Génération de la demande de certificat (CSR)..."
openssl req -new -key $PRIVATE_KEY -out $CSR -config $CONFIG_FILE

echo "📜 Génération du certificat auto-signé..."
openssl x509 -req -in $CSR -signkey $PRIVATE_KEY -out $CERTIFICATE -days 365 -extensions v3_req -extfile $CONFIG_FILE

echo "🔒 Configuration des permissions..."
chmod 600 $PRIVATE_KEY
chmod 644 $CERTIFICATE

echo "✅ Certificats SSL générés avec succès !"
echo ""
echo "📁 Fichiers créés :"
echo "   - Clé privée : $PRIVATE_KEY"
echo "   - Certificat : $CERTIFICATE"
echo "   - CSR : $CSR"
echo "   - Config : $CONFIG_FILE"
echo ""
echo "⚠️  ATTENTION : Ces certificats sont auto-signés et destinés au développement uniquement."
echo "   Pour la production, utilisez des certificats signés par une autorité de certification."
echo ""
echo "🔧 Pour utiliser ces certificats :"
echo "   1. Configurez votre serveur web pour utiliser ces fichiers"
echo "   2. Ajoutez une exception de sécurité dans votre navigateur"
echo "   3. Ou ajoutez le certificat aux autorités de certification de confiance"
echo ""

# Afficher les informations du certificat
echo "📊 Informations du certificat :"
openssl x509 -in $CERTIFICATE -text -noout | grep -A 2 "Subject:"
openssl x509 -in $CERTIFICATE -text -noout | grep -A 3 "Subject Alternative Name"
openssl x509 -in $CERTIFICATE -text -noout | grep "Not After"

echo ""
echo "🎉 Configuration SSL terminée !"
