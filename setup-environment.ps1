# 🚀 Script de Configuration Environnement ERP HUB
# Résolution des problèmes serveur et configuration complète

Write-Host "🚀 Configuration de l'environnement ERP HUB..." -ForegroundColor Green

# 1. Configuration PowerShell
Write-Host "📋 Configuration PowerShell..." -ForegroundColor Yellow
try {
    Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
    Write-Host "✅ Politique d'exécution PowerShell configurée" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur configuration PowerShell: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérification Node.js
Write-Host "📋 Vérification Node.js..." -ForegroundColor Yellow
$nodeVersion = node --version 2>$null
if ($nodeVersion) {
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js non trouvé. Installation requise..." -ForegroundColor Red
    Write-Host "📥 Téléchargement depuis https://nodejs.org/" -ForegroundColor Cyan
    
    # Télécharger et installer Node.js automatiquement
    $nodeUrl = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
    $nodeInstaller = "$env:TEMP\nodejs-installer.msi"
    
    try {
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller
        Write-Host "📦 Installation Node.js..." -ForegroundColor Yellow
        Start-Process msiexec.exe -Wait -ArgumentList "/i $nodeInstaller /quiet"
        Remove-Item $nodeInstaller
        Write-Host "✅ Node.js installé avec succès" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Erreur installation Node.js: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 3. Vérification npm
Write-Host "📋 Vérification npm..." -ForegroundColor Yellow
$npmVersion = npm --version 2>$null
if ($npmVersion) {
    Write-Host "✅ npm détecté: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "❌ npm non trouvé" -ForegroundColor Red
}

# 4. Installation des dépendances globales
Write-Host "📋 Installation des outils globaux..." -ForegroundColor Yellow
$globalPackages = @("create-vite", "serve", "http-server", "live-server")

foreach ($package in $globalPackages) {
    try {
        npm install -g $package
        Write-Host "✅ $package installé" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Erreur installation $package" -ForegroundColor Red
    }
}

# 5. Création du projet React avec Vite
Write-Host "📋 Création du projet React..." -ForegroundColor Yellow
if (!(Test-Path "erp-hub-react")) {
    try {
        npm create vite@latest erp-hub-react -- --template react
        Write-Host "✅ Projet React créé" -ForegroundColor Green
        
        Set-Location erp-hub-react
        npm install
        Write-Host "✅ Dépendances installées" -ForegroundColor Green
        Set-Location ..
    } catch {
        Write-Host "⚠️ Erreur création projet React" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Projet React déjà existant" -ForegroundColor Green
}

# 6. Configuration Docker (optionnel)
Write-Host "📋 Vérification Docker..." -ForegroundColor Yellow
$dockerVersion = docker --version 2>$null
if ($dockerVersion) {
    Write-Host "✅ Docker détecté: $dockerVersion" -ForegroundColor Green
    
    # Créer Dockerfile pour l'application
    $dockerfileContent = @"
# Dockerfile pour ERP HUB
FROM node:20-alpine

WORKDIR /app

# Copier les fichiers de configuration
COPY package*.json ./
RUN npm install

# Copier le code source
COPY . .

# Construire l'application
RUN npm run build

# Serveur de production
FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"@
    
    $dockerfileContent | Out-File -FilePath "Dockerfile" -Encoding UTF8
    Write-Host "✅ Dockerfile créé" -ForegroundColor Green
} else {
    Write-Host "⚠️ Docker non trouvé (optionnel)" -ForegroundColor Yellow
}

# 7. Configuration WSL (si disponible)
Write-Host "📋 Vérification WSL..." -ForegroundColor Yellow
$wslVersion = wsl --version 2>$null
if ($wslVersion) {
    Write-Host "✅ WSL détecté" -ForegroundColor Green
    Write-Host "💡 Vous pouvez utiliser WSL pour un environnement Unix" -ForegroundColor Cyan
} else {
    Write-Host "⚠️ WSL non trouvé (optionnel)" -ForegroundColor Yellow
}

# 8. Création des scripts de démarrage
Write-Host "📋 Création des scripts de démarrage..." -ForegroundColor Yellow

# Script de démarrage simple
$startScript = @"
@echo off
echo 🚀 Démarrage ERP HUB...
echo.

REM Démarrer le serveur de développement
echo 📡 Démarrage du serveur...
start "ERP HUB Server" cmd /k "cd frontend && python -m http.server 8080"

REM Attendre 2 secondes
timeout /t 2 /nobreak >nul

REM Ouvrir le navigateur
echo 🌐 Ouverture du navigateur...
start http://localhost:8080

echo ✅ ERP HUB démarré sur http://localhost:8080
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
"@

$startScript | Out-File -FilePath "start-erp.bat" -Encoding UTF8
Write-Host "✅ Script start-erp.bat créé" -ForegroundColor Green

# Script de démarrage avec Node.js
$startNodeScript = @"
@echo off
echo 🚀 Démarrage ERP HUB avec Node.js...
echo.

REM Vérifier si http-server est installé
where http-server >nul 2>nul
if %errorlevel% neq 0 (
    echo 📦 Installation http-server...
    npm install -g http-server
)

REM Démarrer le serveur
echo 📡 Démarrage du serveur Node.js...
start "ERP HUB Server" cmd /k "cd frontend && http-server -p 8080 -o"

echo ✅ ERP HUB démarré sur http://localhost:8080
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
"@

$startNodeScript | Out-File -FilePath "start-erp-node.bat" -Encoding UTF8
Write-Host "✅ Script start-erp-node.bat créé" -ForegroundColor Green

# 9. Création du package.json pour le projet
Write-Host "📋 Création package.json..." -ForegroundColor Yellow
$packageJson = @"
{
  "name": "erp-hub",
  "version": "1.0.0",
  "description": "Système ERP modulaire avec React et Django",
  "main": "index.js",
  "scripts": {
    "start": "http-server frontend -p 8080 -o",
    "dev": "live-server frontend --port=8080",
    "build": "echo 'Build process for production'",
    "serve": "serve frontend -l 8080",
    "docker:build": "docker build -t erp-hub .",
    "docker:run": "docker run -p 8080:80 erp-hub"
  },
  "keywords": ["erp", "react", "django", "finance", "hr", "crm"],
  "author": "ERP HUB Team",
  "license": "MIT",
  "devDependencies": {
    "http-server": "^14.1.1",
    "live-server": "^1.2.2",
    "serve": "^14.2.1"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  }
}
"@

$packageJson | Out-File -FilePath "package.json" -Encoding UTF8
Write-Host "✅ package.json créé" -ForegroundColor Green

# 10. Installation des dépendances du projet
Write-Host "📋 Installation des dépendances du projet..." -ForegroundColor Yellow
try {
    npm install
    Write-Host "✅ Dépendances installées" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur installation dépendances" -ForegroundColor Red
}

# 11. Résumé et instructions
Write-Host ""
Write-Host "🎉 Configuration terminée !" -ForegroundColor Green
Write-Host ""
Write-Host "📋 RÉSUMÉ DE LA CONFIGURATION:" -ForegroundColor Cyan
Write-Host "✅ PowerShell configuré (RemoteSigned)" -ForegroundColor Green
Write-Host "✅ Node.js et npm vérifiés/installés" -ForegroundColor Green
Write-Host "✅ Outils globaux installés" -ForegroundColor Green
Write-Host "✅ Scripts de démarrage créés" -ForegroundColor Green
Write-Host "✅ package.json configuré" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 COMMANDES DISPONIBLES:" -ForegroundColor Cyan
Write-Host "• start-erp.bat          - Démarrage avec Python" -ForegroundColor Yellow
Write-Host "• start-erp-node.bat     - Démarrage avec Node.js" -ForegroundColor Yellow
Write-Host "• npm start              - Serveur http-server" -ForegroundColor Yellow
Write-Host "• npm run dev            - Serveur live-server" -ForegroundColor Yellow
Write-Host "• npm run serve          - Serveur serve" -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 URL d'accès: http://localhost:8080" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 RECOMMANDATIONS:" -ForegroundColor Cyan
Write-Host "• Utilisez 'start-erp-node.bat' pour un démarrage simple" -ForegroundColor Yellow
Write-Host "• Utilisez 'npm run dev' pour le développement avec rechargement automatique" -ForegroundColor Yellow
Write-Host "• Consultez la documentation dans les fichiers .md" -ForegroundColor Yellow
Write-Host ""

# 12. Test de démarrage automatique (optionnel)
$response = Read-Host "Voulez-vous tester le démarrage maintenant ? (o/n)"
if ($response -eq "o" -or $response -eq "O") {
    Write-Host "🚀 Test de démarrage..." -ForegroundColor Green
    Start-Process "start-erp-node.bat"
}

Write-Host "✅ Configuration terminée avec succès !" -ForegroundColor Green
