# 🧹 SCRIPT DE NETTOYAGE DU PROJET ERP HUB
# Supprime les fichiers dupliqués et obsolètes

import os
import shutil
from pathlib import Path

def cleanup_project():
    """Nettoie le projet en supprimant les fichiers obsolètes"""
    
    print("🧹 NETTOYAGE DU PROJET ERP HUB")
    print("=" * 50)
    
    # Fichiers à supprimer (versions obsolètes)
    files_to_remove = [
        # Anciennes versions des pages (non-PostgreSQL)
        "frontend/hr-management.html",
        "frontend/sales-management.html", 
        "frontend/purchase-management.html",
        "frontend/stock-management.html",
        "frontend/logistics-management.html",
        "frontend/crm-management.html",
        "frontend/bi-management.html",
        "frontend/finance-management.html",
        "frontend/accounting-management.html",
        "frontend/manager-management.html",
        
        # Pages de démonstration obsolètes
        "frontend/demo.html",
        "frontend/homepage-demo.html",
        "frontend/login-demo.html",
        "frontend/test-simple.html",
        "frontend/dashboard-demo.html",
        "frontend/index.html",
        
        # Serveurs de test obsolètes
        "frontend/erp-server.js",
        "frontend/server.js",
        "frontend/simple-server.js",
        "frontend/test-server.js",
        "frontend/python-server.py",
        "frontend/run-erp.py",
        "frontend/start-erp.js",
        
        # Scripts obsolètes
        "simple_api_server.py",
        "simple_test_server.py",
        "run_local_dev.py",
        
        # Fichiers de configuration obsolètes
        "docker-compose-simple.yml",
        "docker-compose.dev.yml",
        "docker-compose.local.yml",
        "docker-compose.prod.yml",
        "docker-compose.production.yml",
        "docker-compose.yml",
        
        # Scripts de démarrage obsolètes
        "run-erp.bat",
        "start-erp.bat",
        "start-erp-simple.bat",
        "start-erp-full.bat",
        "launch-erp-complete.bat",
        "start-network-node.bat",
        "start-network-server.bat",
        
        # Tests obsolètes
        "test_core_features.py",
        "test_final_integration.py",
        "test_postgres_connection.py",
        "test_postgresql_api.py",
        "test_production_deployment.py",
        "test_system_complete.py",
        
        # Documentation obsolète
        "frontend/date-improvements-complete.md",
        "frontend/date-improvements-completed.md",
        "frontend/date-improvements-final-complete.md",
        "frontend/date-improvements-final.md",
        "frontend/date-improvements-plan.md",
        "frontend/date-improvements-summary.md",
    ]
    
    # Dossiers à supprimer
    folders_to_remove = [
        "backend-node",
        "frontend/src",
        "frontend/node_modules",
        "backend/agents",
        "backend/apps",
        "backend/config",
        "backend/core",
        "backend/scripts",
        "database/init",
        "monitoring",
        "nginx",
        "ssl",
        "tests",
        "logs",
        "backups",
        "scripts",
    ]
    
    removed_files = []
    removed_folders = []
    
    # Supprimer les fichiers
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"✅ Supprimé: {file_path}")
            except Exception as e:
                print(f"❌ Erreur suppression {file_path}: {e}")
        else:
            print(f"⚠️ Fichier non trouvé: {file_path}")
    
    # Supprimer les dossiers
    for folder_path in folders_to_remove:
        if os.path.exists(folder_path):
            try:
                shutil.rmtree(folder_path)
                removed_folders.append(folder_path)
                print(f"✅ Dossier supprimé: {folder_path}")
            except Exception as e:
                print(f"❌ Erreur suppression dossier {folder_path}: {e}")
        else:
            print(f"⚠️ Dossier non trouvé: {folder_path}")
    
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DU NETTOYAGE")
    print("=" * 50)
    print(f"📄 Fichiers supprimés: {len(removed_files)}")
    print(f"📁 Dossiers supprimés: {len(removed_folders)}")
    
    return removed_files, removed_folders

def list_remaining_files():
    """Liste les fichiers conservés"""
    
    print("\n🗂️ FICHIERS CONSERVÉS")
    print("=" * 50)
    
    essential_files = {
        # API et serveur principal
        "postgresql_api_server.py": "Serveur API principal PostgreSQL",
        "test_all_endpoints.py": "Script de test des endpoints API",
        
        # Pages frontend PostgreSQL (optimisées)
        "frontend/hr-management-postgresql.html": "Page Agent HR (Ressources Humaines)",
        "frontend/sales-management-postgresql.html": "Page Agent Sales (Ventes)",
        "frontend/purchase-management-postgresql.html": "Page Agent Purchase (Achats)",
        "frontend/stock-management-postgresql.html": "Page Agent Stock (Inventaire)",
        "frontend/logistics-management-postgresql.html": "Page Agent Logistics (Logistique)",
        "frontend/crm-management-postgresql.html": "Page Agent CRM (Relation Client)",
        "frontend/bi-management-postgresql.html": "Page Agent BI (Business Intelligence)",
        "frontend/dashboard-global-postgresql.html": "Dashboard Global consolidé",
        
        # Base de données
        "create_all_erp_tables.sql": "Script création tables PostgreSQL",
        "insert_demo_data_all_agents.sql": "Données de démonstration",
        
        # Configuration Docker
        "docker-compose-postgresql.yml": "Configuration Docker PostgreSQL",
        
        # Scripts de démarrage
        "start_erp_postgresql.bat": "Script démarrage Windows",
        "start_erp_postgresql.sh": "Script démarrage Linux/Mac",
        
        # Documentation
        "README.md": "Documentation principale",
        "DEPLOYMENT.md": "Guide de déploiement",
        "MULTI_COMPUTER_SETUP_GUIDE.md": "Guide installation multi-ordinateurs",
        
        # Utilitaires
        "frontend/auth_manager.js": "Gestionnaire d'authentification",
        "frontend/database_api.js": "API base de données",
        "frontend/date-utils.js": "Utilitaires de dates",
        "frontend/exemple-budget-import.csv": "Exemple import CSV",
        
        # Configuration
        "frontend/package.json": "Configuration Node.js",
        "frontend/tailwind.config.js": "Configuration Tailwind CSS",
        "frontend/vite.config.js": "Configuration Vite",
        "frontend/postcss.config.js": "Configuration PostCSS",
    }
    
    for file_path, description in essential_files.items():
        if os.path.exists(file_path):
            print(f"✅ {file_path:<50} - {description}")
        else:
            print(f"❌ {file_path:<50} - MANQUANT!")
    
    print(f"\n📊 Total fichiers essentiels: {len(essential_files)}")

if __name__ == "__main__":
    removed_files, removed_folders = cleanup_project()
    list_remaining_files()
    
    print("\n🎉 NETTOYAGE TERMINÉ!")
    print("Le projet ERP HUB est maintenant optimisé et prêt pour le déploiement.")
