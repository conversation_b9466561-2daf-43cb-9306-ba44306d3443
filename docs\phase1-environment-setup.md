# Phase 1 : Configuration de l'environnement Docker

## Vue d'ensemble

Cette phase établit l'infrastructure de base pour le système ERP HUB avec une architecture modulaire et une configuration Docker complète pour le développement et la production.

## Architecture mise en place

### Conteneurs Docker

1. **Base de données PostgreSQL 15**
   - Stockage persistant avec volumes Docker
   - Scripts d'initialisation automatique
   - Configuration multi-schémas (core, agents, modules)
   - Extensions PostgreSQL activées (uuid-ossp, pg_trgm, btree_gin)

2. **Backend Django REST Framework**
   - Python 3.11 avec Django 4.2
   - Configuration de développement avec hot reload
   - Dépendances pour l'IA et les agents (OpenAI, LangChain)
   - Support JWT et RBAC

3. **Frontend React avec Vite**
   - Node.js 18 avec React 18
   - Configuration Tailwind CSS
   - Hot Module Replacement (HMR)
   - TypeScript et outils de développement

4. **Services auxiliaires**
   - Redis pour le cache et les tâches asynchrones
   - Adminer pour la gestion de base de données
   - Nginx pour le reverse proxy (production)

### Structure du projet

```
ERP_HUB/
├── docker-compose.yml          # Configuration production
├── docker-compose.dev.yml      # Configuration développement
├── .env.example               # Variables d'environnement
├── backend/                   # Django REST API
│   ├── Dockerfile            # Image production
│   ├── Dockerfile.dev        # Image développement
│   ├── requirements.txt      # Dépendances Python
│   └── [structure Django]
├── frontend/                  # React Frontend
│   ├── Dockerfile            # Image production
│   ├── Dockerfile.dev        # Image développement
│   ├── package.json          # Dépendances Node.js
│   ├── vite.config.js        # Configuration Vite
│   └── [structure React]
├── database/                  # Scripts PostgreSQL
│   └── init/                 # Scripts d'initialisation
├── scripts/                   # Scripts utilitaires
│   ├── dev-start.ps1         # Démarrage développement
│   ├── dev-stop.ps1          # Arrêt développement
│   └── test-environment.ps1  # Test environnement
└── docs/                     # Documentation
```

## Configuration réalisée

### 1. Docker Compose

- **Développement** : `docker-compose.dev.yml`
  - Hot reload activé pour backend et frontend
  - Volumes montés pour le code source
  - Ports exposés pour le debugging
  - Adminer inclus pour la gestion DB

- **Production** : `docker-compose.yml`
  - Images optimisées
  - Nginx reverse proxy
  - Volumes persistants
  - Configuration sécurisée

### 2. Base de données PostgreSQL

- **Schémas organisés** :
  - `core` : Tables système et configuration
  - `agents` : Architecture des agents
  - `modules` : Modules ERP métier

- **Fonctionnalités** :
  - UUID automatiques
  - Timestamps automatiques
  - Fonctions utilitaires
  - Index optimisés

### 3. Backend Django

- **Dépendances principales** :
  - Django REST Framework
  - JWT Authentication
  - PostgreSQL driver
  - Redis client
  - OpenAI et LangChain pour l'IA

- **Configuration** :
  - Structure modulaire préparée
  - Variables d'environnement
  - Logging configuré
  - Sécurité de base

### 4. Frontend React

- **Technologies** :
  - React 18 avec TypeScript
  - Vite pour le build rapide
  - Tailwind CSS pour le styling
  - React Query pour l'état serveur

- **Configuration** :
  - Alias de chemins configurés
  - Hot reload optimisé
  - Build de production optimisé
  - Palette de couleurs ERP

## Scripts utilitaires

### Démarrage rapide

```powershell
# Test de l'environnement
.\scripts\test-environment.ps1

# Démarrage développement
.\scripts\dev-start.ps1

# Arrêt développement
.\scripts\dev-stop.ps1
```

### URLs d'accès

- **Frontend** : http://localhost:3000
- **Backend API** : http://localhost:8000
- **Admin Django** : http://localhost:8000/admin
- **Documentation API** : http://localhost:8000/api/docs/
- **Adminer (DB)** : http://localhost:8080

## Prochaines étapes

La Phase 1 étant terminée, nous pouvons maintenant passer à la **Phase 2 : Fondation Core** qui inclura :

1. Configuration complète de Django avec les modèles de base
2. Système d'authentification JWT et RBAC
3. Interface React de base avec routing
4. API REST de base pour l'authentification
5. Tests de base pour valider le fonctionnement

## Validation de la Phase 1

Pour valider que cette phase est complète :

1. Exécuter `.\scripts\test-environment.ps1`
2. Démarrer l'environnement avec `.\scripts\dev-start.ps1`
3. Vérifier l'accès aux URLs listées ci-dessus
4. Confirmer que les conteneurs démarrent sans erreur

## Notes importantes

- Les fichiers `.env` doivent être configurés avant le premier démarrage
- Les volumes Docker persistent les données entre les redémarrages
- Le hot reload est configuré pour un développement efficace
- La structure est préparée pour l'architecture multi-agents
