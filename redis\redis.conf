# 🚀 CONFIGURATION REDIS POUR ERP HUB
# Configuration optimisée pour performance et sécurité

# ===== CONFIGURATION RÉSEAU =====
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# ===== CONFIGURATION GÉNÉRALE =====
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# ===== SÉCURITÉ =====
# Le mot de passe sera défini via --requirepass dans Docker
# requirepass sera défini par la variable d'environnement

# Désactiver les commandes dangereux
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"
rename-command SHUTDOWN "SHUTDOWN_a8f7e2d1c9b4f6e3"
rename-command DEBUG ""
rename-command EVAL ""

# Protection contre les attaques
protected-mode yes

# ===== PERSISTANCE =====
# Sauvegarde RDB
save 900 1
save 300 10
save 60 10000

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File) pour durabilité
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# ===== PERFORMANCE =====
# Mémoire
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Clients
maxclients 10000

# Optimisations
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# Lazy freeing
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# ===== MONITORING =====
# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# ===== RÉPLICATION (si nécessaire) =====
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5

# ===== MODULES ET EXTENSIONS =====
# Pas de modules externes pour la sécurité

# ===== NOTIFICATIONS =====
notify-keyspace-events "Ex"

# ===== CONFIGURATION SPÉCIFIQUE ERP =====
# Optimisations pour les cas d'usage ERP

# Cache des sessions utilisateur (expire automatiquement)
# Cache des requêtes SQL (TTL court)
# Cache des permissions (TTL moyen)
# Cache des données de dashboard (TTL court)

# ===== LOGS ET DEBUG =====
# En production, réduire le niveau de log
# loglevel warning

# ===== LIMITES DE SÉCURITÉ =====
# Limite le nombre de connexions par IP
# tcp-keepalive 300

# ===== COMMENTAIRES DE CONFIGURATION =====
# Cette configuration est optimisée pour :
# 1. Performance : LRU eviction, lazy freeing, optimisations ziplist
# 2. Sécurité : Commandes dangereuses désactivées, protected mode
# 3. Durabilité : AOF + RDB, sauvegarde fréquente
# 4. Monitoring : Slow log, latency monitoring
# 5. ERP Usage : Notifications pour expiration, politique de mémoire adaptée

# ===== VARIABLES D'ENVIRONNEMENT =====
# REDIS_PASSWORD : Mot de passe (défini dans Docker)
# REDIS_MAXMEMORY : Limite mémoire (défaut 512mb)
# REDIS_LOGLEVEL : Niveau de log (défaut notice)
