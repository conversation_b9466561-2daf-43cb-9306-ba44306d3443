# 🔒 API REST SÉCURISÉE ERP HUB
# Serveur Flask avec sécurité renforcée, httpOnly cookies, rate limiting

from flask import Flask, request, jsonify, make_response
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager, create_access_token, create_refresh_token, jwt_required, get_jwt_identity, get_jwt
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
from database_postgresql import ERPDatabasePostgreSQL
import secrets
import hashlib
import time
import logging
import os
from datetime import datetime, timedelta
from functools import wraps
import redis
import json

# Configuration sécurisée
app = Flask(__name__)

# Configuration JWT sécurisée
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', secrets.token_hex(32))
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
app.config['JWT_TOKEN_LOCATION'] = ['cookies']
app.config['JWT_COOKIE_SECURE'] = True  # HTTPS uniquement en production
app.config['JWT_COOKIE_HTTPONLY'] = True  # Pas d'accès JavaScript
app.config['JWT_COOKIE_SAMESITE'] = 'Lax'
app.config['JWT_COOKIE_CSRF_PROTECT'] = True

# Configuration sécurité
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))

# Initialisation des extensions
jwt = JWTManager(app)

# Configuration CORS sécurisée
CORS(app, 
     origins=['http://localhost:3000', 'https://localhost:3000'],
     supports_credentials=True,
     allow_headers=['Content-Type', 'X-CSRF-Token', 'X-Requested-With'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# Configuration Talisman pour la sécurité HTTP
Talisman(app, 
         force_https=False,  # True en production
         strict_transport_security=True,
         content_security_policy={
             'default-src': "'self'",
             'script-src': "'self' 'unsafe-inline'",
             'style-src': "'self' 'unsafe-inline'",
             'img-src': "'self' data:",
             'font-src': "'self'"
         })

# Configuration Redis pour le cache et rate limiting
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    redis_client.ping()
    print("✅ Redis connecté")
except:
    redis_client = None
    print("⚠️ Redis non disponible, utilisation mémoire")

# Rate Limiter
limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="redis://localhost:6379" if redis_client else "memory://"
)

# Base de données
db = ERPDatabasePostgreSQL()

# Configuration des logs sécurisés
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('security.log'),
        logging.StreamHandler()
    ]
)
security_logger = logging.getLogger('security')

# ===== UTILITAIRES SÉCURITÉ =====

def generate_csrf_token():
    """Générer un token CSRF sécurisé"""
    return secrets.token_hex(32)

def validate_csrf_token(token):
    """Valider un token CSRF"""
    if not token or len(token) != 64:
        return False
    try:
        bytes.fromhex(token)
        return True
    except ValueError:
        return False

def log_security_event(event_type, user_id=None, details=None):
    """Logger les événements de sécurité"""
    event = {
        'timestamp': datetime.utcnow().isoformat(),
        'type': event_type,
        'user_id': user_id,
        'ip': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'details': details or {}
    }
    security_logger.info(f"SECURITY_EVENT: {json.dumps(event)}")

def require_csrf():
    """Décorateur pour vérifier le token CSRF"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            csrf_token = request.headers.get('X-CSRF-Token')
            if not validate_csrf_token(csrf_token):
                log_security_event('csrf_validation_failed', details={'endpoint': request.endpoint})
                return jsonify({'success': False, 'error': 'Token CSRF invalide'}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def sanitize_input(data):
    """Nettoyer et valider les entrées utilisateur"""
    if isinstance(data, str):
        # Supprimer les caractères dangereux
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            data = data.replace(char, '')
        return data.strip()
    elif isinstance(data, dict):
        return {k: sanitize_input(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [sanitize_input(item) for item in data]
    return data

# ===== GESTION DES ERREURS JWT =====

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    log_security_event('token_expired', user_id=jwt_payload.get('sub'))
    return jsonify({'success': False, 'error': 'Token expiré'}), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    log_security_event('invalid_token', details={'error': str(error)})
    return jsonify({'success': False, 'error': 'Token invalide'}), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({'success': False, 'error': 'Token requis'}), 401

# ===== ENDPOINTS D'AUTHENTIFICATION SÉCURISÉS =====

@app.route('/api/auth/secure-login', methods=['POST'])
@limiter.limit("5 per minute")  # Limite stricte pour les tentatives de connexion
@require_csrf()
def secure_login():
    """Connexion sécurisée avec httpOnly cookies"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'Données manquantes'}), 400

        # Nettoyer les entrées
        username = sanitize_input(data.get('username', ''))
        password = data.get('password', '')

        # Validation
        if not username or not password:
            log_security_event('login_attempt_missing_credentials', details={'username': username})
            return jsonify({'success': False, 'error': 'Identifiants requis'}), 400

        if len(username) < 3 or len(password) < 6:
            log_security_event('login_attempt_invalid_format', details={'username': username})
            return jsonify({'success': False, 'error': 'Format d\'identifiants invalide'}), 400

        # Vérifier les tentatives de brute force
        attempt_key = f"login_attempts:{request.remote_addr}:{username}"
        if redis_client:
            attempts = redis_client.get(attempt_key)
            if attempts and int(attempts) >= 5:
                log_security_event('login_blocked_brute_force', details={'username': username})
                return jsonify({'success': False, 'error': 'Trop de tentatives. Réessayez dans 15 minutes.'}), 429

        # Authentifier l'utilisateur
        user = db.authenticate_user(username, password)

        if user:
            # Réinitialiser les tentatives
            if redis_client:
                redis_client.delete(attempt_key)

            # Créer les tokens
            access_token = create_access_token(
                identity=str(user['id']),
                additional_claims={
                    'username': user['username'],
                    'role': user['role'],
                    'department': user['department']
                }
            )
            
            refresh_token = create_refresh_token(identity=str(user['id']))

            # Créer la réponse avec cookies sécurisés
            response = make_response(jsonify({
                'success': True,
                'message': 'Connexion réussie',
                'user': {
                    'id': str(user['id']),
                    'username': user['username'],
                    'email': user['email'],
                    'first_name': user['first_name'],
                    'last_name': user['last_name'],
                    'role': user['role'],
                    'department': user['department']
                },
                'permissions': get_user_permissions(user['role'])
            }))

            # Définir les cookies httpOnly
            response.set_cookie(
                'access_token_cookie',
                access_token,
                max_age=3600,  # 1 heure
                httponly=True,
                secure=True,  # HTTPS uniquement en production
                samesite='Lax'
            )
            
            response.set_cookie(
                'refresh_token_cookie',
                refresh_token,
                max_age=30*24*3600,  # 30 jours
                httponly=True,
                secure=True,
                samesite='Lax'
            )

            # Logger la connexion réussie
            log_security_event('login_success', user_id=str(user['id']), details={'username': username})

            return response

        else:
            # Incrémenter les tentatives échouées
            if redis_client:
                redis_client.incr(attempt_key)
                redis_client.expire(attempt_key, 900)  # 15 minutes

            log_security_event('login_failed', details={'username': username})
            return jsonify({'success': False, 'error': 'Identifiants incorrects'}), 401

    except Exception as e:
        log_security_event('login_error', details={'error': str(e)})
        return jsonify({'success': False, 'error': 'Erreur interne'}), 500

@app.route('/api/auth/secure-logout', methods=['POST'])
@jwt_required()
@require_csrf()
def secure_logout():
    """Déconnexion sécurisée"""
    try:
        user_id = get_jwt_identity()
        
        # Créer la réponse
        response = make_response(jsonify({
            'success': True,
            'message': 'Déconnexion réussie'
        }))

        # Supprimer les cookies
        response.set_cookie('access_token_cookie', '', expires=0, httponly=True, secure=True)
        response.set_cookie('refresh_token_cookie', '', expires=0, httponly=True, secure=True)

        # Logger la déconnexion
        log_security_event('logout_success', user_id=user_id)

        return response

    except Exception as e:
        log_security_event('logout_error', details={'error': str(e)})
        return jsonify({'success': False, 'error': 'Erreur lors de la déconnexion'}), 500

@app.route('/api/auth/refresh-token', methods=['POST'])
@jwt_required(refresh=True)
@require_csrf()
def refresh_token():
    """Rafraîchir le token d'accès"""
    try:
        user_id = get_jwt_identity()
        
        # Créer un nouveau token d'accès
        new_access_token = create_access_token(identity=user_id)
        
        response = make_response(jsonify({
            'success': True,
            'message': 'Token rafraîchi'
        }))

        # Mettre à jour le cookie
        response.set_cookie(
            'access_token_cookie',
            new_access_token,
            max_age=3600,
            httponly=True,
            secure=True,
            samesite='Lax'
        )

        log_security_event('token_refreshed', user_id=user_id)
        return response

    except Exception as e:
        log_security_event('token_refresh_error', details={'error': str(e)})
        return jsonify({'success': False, 'error': 'Erreur refresh token'}), 500

@app.route('/api/auth/status', methods=['GET'])
def auth_status():
    """Vérifier le statut d'authentification"""
    try:
        # Vérifier si l'utilisateur a un token valide
        from flask_jwt_extended import verify_jwt_in_request
        
        try:
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            
            # Récupérer les infos utilisateur
            user = db.get_user_by_id(user_id)
            if user:
                return jsonify({
                    'success': True,
                    'authenticated': True,
                    'user': {
                        'id': str(user['id']),
                        'username': user['username'],
                        'email': user['email'],
                        'first_name': user['first_name'],
                        'last_name': user['last_name'],
                        'role': user['role'],
                        'department': user['department']
                    },
                    'permissions': get_user_permissions(user['role'])
                })
            
        except:
            pass

        return jsonify({
            'success': True,
            'authenticated': False
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Erreur vérification statut'
        }), 500

@app.route('/api/security/log', methods=['POST'])
@limiter.limit("10 per minute")
def log_client_security_event():
    """Recevoir les logs de sécurité du client"""
    try:
        data = request.get_json()
        if data:
            # Nettoyer les données
            clean_data = sanitize_input(data)
            log_security_event('client_security_event', details=clean_data)
        
        return jsonify({'success': True})
    except:
        return jsonify({'success': False}), 400

# ===== UTILITAIRES =====

def get_user_permissions(role):
    """Obtenir les permissions selon le rôle"""
    permissions = {
        'admin': {
            'budgets': ['read', 'write', 'delete', 'export'],
            'movements': ['read', 'write', 'delete', 'export'],
            'accounts': ['read', 'write', 'delete', 'export'],
            'employees': ['read', 'write', 'delete', 'export'],
            'customers': ['read', 'write', 'delete', 'export'],
            'products': ['read', 'write', 'delete', 'export'],
            'reports': ['read', 'write', 'export'],
            'system': ['read', 'write', 'admin']
        },
        'manager': {
            'budgets': ['read', 'write', 'export'],
            'movements': ['read', 'write', 'export'],
            'accounts': ['read', 'export'],
            'employees': ['read', 'write', 'export'],
            'customers': ['read', 'write', 'export'],
            'products': ['read', 'write', 'export'],
            'reports': ['read', 'write', 'export']
        },
        'user': {
            'budgets': ['read', 'export'],
            'movements': ['read'],
            'accounts': ['read'],
            'employees': ['read'],
            'customers': ['read'],
            'products': ['read'],
            'reports': ['read']
        }
    }
    return permissions.get(role, permissions['user'])

# ===== ENDPOINTS SÉCURISÉS =====

@app.route('/api/health', methods=['GET'])
@limiter.limit("30 per minute")
def health_check():
    """Health check sécurisé"""
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '2.0.0-secure'
    })

# ===== GESTION DES ERREURS =====

@app.errorhandler(429)
def ratelimit_handler(e):
    log_security_event('rate_limit_exceeded', details={'limit': str(e.description)})
    return jsonify({
        'success': False,
        'error': 'Trop de requêtes. Veuillez patienter.'
    }), 429

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint non trouvé'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    log_security_event('internal_server_error', details={'error': str(error)})
    return jsonify({
        'success': False,
        'error': 'Erreur interne du serveur'
    }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🔒 Démarrage du serveur API ERP HUB SÉCURISÉ...")
    print("🛡️ Fonctionnalités de sécurité activées:")
    print("   - httpOnly cookies pour JWT")
    print("   - Protection CSRF")
    print("   - Rate limiting")
    print("   - Headers de sécurité HTTP")
    print("   - Logging des événements de sécurité")
    print("   - Validation et sanitisation des entrées")
    print("   - Protection contre le brute force")
    
    # Créer un utilisateur admin par défaut si nécessaire
    try:
        admin_user = db.get_user_by_username('admin')
        if not admin_user:
            print("🔧 Création de l'utilisateur admin par défaut...")
            # Ici vous devriez implémenter la création d'utilisateur
    except:
        pass
    
    print("🌐 Serveur disponible sur : https://localhost:5000")
    print("🔑 Connexion sécurisée requise")
    
    # Lancer le serveur
    app.run(debug=False, host='0.0.0.0', port=5000, ssl_context='adhoc')
