# 🌐 API REST SERVER POUR ERP HUB
# Serveur Flask pour connecter frontend à la base de données

from flask import Flask, request, jsonify
from flask_cors import CORS
from database_postgresql import ERPDatabasePostgreSQL
from ai_module import ERPAIModule
from document_search import DocumentSearchEngine
from cross_analysis import CrossAnalysisEngine
import json
import psycopg2.extras
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)  # Permettre les requêtes depuis le frontend

# Initialiser la base de données PostgreSQL
db = ERPDatabasePostgreSQL()

# Initialiser le module IA
ai_module = ERPAIModule(openai_api_key=os.getenv('OPENAI_API_KEY'))

# Initialiser le moteur de recherche documentaire
document_search = DocumentSearchEngine(db)

# Initialiser le moteur d'analyse croisée
cross_analysis = CrossAnalysisEngine(db)

# ===== ENDPOINTS BUDGETS =====

@app.route('/api/budgets', methods=['GET'])
def get_budgets():
    """Récupérer tous les budgets"""
    try:
        budgets = db.read_budgets()
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets)
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets', methods=['POST'])
def create_budget():
    """Créer un nouveau budget"""
    try:
        budget_data = request.get_json()
        
        # Validation des champs obligatoires
        required_fields = ['id', 'categoryName', 'categoryType']
        for field in required_fields:
            if field not in budget_data:
                return jsonify({
                    'success': False,
                    'error': f'Champ obligatoire manquant : {field}'
                }), 400
        
        # Ajouter les dates
        budget_data['createdDate'] = datetime.now().isoformat()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        success = db.create_budget(budget_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget créé avec succès',
                'data': budget_data
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la création du budget'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['PUT'])
def update_budget(budget_id):
    """Mettre à jour un budget"""
    try:
        budget_data = request.get_json()
        budget_data['modifiedDate'] = datetime.now().isoformat()
        
        success = db.update_budget(budget_id, budget_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget mis à jour avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de mise à jour'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/<budget_id>', methods=['DELETE'])
def delete_budget(budget_id):
    """Supprimer un budget"""
    try:
        success = db.delete_budget(budget_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Budget supprimé avec succès'
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'Budget non trouvé ou erreur de suppression'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS HISTORIQUE =====

@app.route('/api/history', methods=['POST'])
def save_action():
    """Sauvegarder une action dans l'historique"""
    try:
        action_data = request.get_json()
        
        success = db.save_action_history(
            action_data['action'],
            action_data['data'],
            action_data.get('user_id', 'default')
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Action sauvegardée dans l\'historique'
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Erreur lors de la sauvegarde de l\'historique'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS UTILITAIRES =====

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Récupérer les statistiques de la base de données"""
    try:
        stats = db.get_database_stats()
        return jsonify({
            'success': True,
            'data': stats
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifier l'état du serveur"""
    return jsonify({
        'success': True,
        'message': 'Serveur ERP HUB opérationnel',
        'timestamp': datetime.now().isoformat()
    }), 200

# ===== ENDPOINTS DASHBOARD =====

@app.route('/api/dashboard', methods=['GET'])
def get_dashboard():
    """Récupérer les données du tableau de bord"""
    try:
        stats = db.get_database_stats()
        budgets = db.read_budgets()

        # Calculer les métriques du dashboard
        total_forecast = sum(float(b.get('forecast', 0)) for b in budgets)
        total_realized = sum(float(b.get('realized', 0)) for b in budgets)
        realization_rate = (total_realized / total_forecast * 100) if total_forecast > 0 else 0

        dashboard_data = {
            'stats': stats,
            'financial_summary': {
                'total_forecast': total_forecast,
                'total_realized': total_realized,
                'realization_rate': round(realization_rate, 2),
                'budget_count': len(budgets)
            },
            'recent_budgets': budgets[:5]  # 5 budgets les plus récents
        }

        return jsonify({
            'success': True,
            'data': dashboard_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS EMPLOYÉS =====

@app.route('/api/employees', methods=['GET'])
def get_employees():
    """Récupérer tous les employés"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('SELECT * FROM employees ORDER BY created_date DESC')
        rows = cursor.fetchall()

        employees = []
        for row in rows:
            employee = {
                'id': row['id'],
                'firstName': row['first_name'],
                'lastName': row['last_name'],
                'email': row['email'],
                'phone': row['phone'],
                'position': row['position'],
                'department': row['department'],
                'hireDate': row['hire_date'].isoformat() if row['hire_date'] else None,
                'salary': float(row['salary']) if row['salary'] else 0,
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            employees.append(employee)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(employees),
            'data': employees
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/leaves', methods=['GET'])
def get_leaves():
    """Récupérer tous les congés"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT l.*, e.first_name, e.last_name
            FROM leaves l
            LEFT JOIN employees e ON l.employee_id = e.id
            ORDER BY l.created_date DESC
        ''')
        rows = cursor.fetchall()

        leaves = []
        for row in rows:
            leave = {
                'id': row['id'],
                'employeeId': row['employee_id'],
                'employeeName': f"{row['first_name']} {row['last_name']}" if row['first_name'] else 'Employé inconnu',
                'leaveType': row['leave_type'],
                'startDate': row['start_date'].isoformat() if row['start_date'] else None,
                'endDate': row['end_date'].isoformat() if row['end_date'] else None,
                'daysCount': row['days_count'],
                'status': row['status'],
                'reason': row['reason'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None
            }
            leaves.append(leave)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(leaves),
            'data': leaves
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS CONTACTS/CRM =====

@app.route('/api/contacts', methods=['GET'])
def get_contacts():
    """Récupérer tous les contacts"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('SELECT * FROM contacts ORDER BY created_date DESC')
        rows = cursor.fetchall()

        contacts = []
        for row in rows:
            contact = {
                'id': row['id'],
                'companyName': row['company_name'],
                'contactName': row['contact_name'],
                'email': row['email'],
                'phone': row['phone'],
                'address': row['address'],
                'contactType': row['contact_type'],
                'status': row['status'],
                'notes': row['notes'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            contacts.append(contact)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(contacts),
            'data': contacts
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS KPIs =====

@app.route('/api/kpis', methods=['GET'])
def get_kpis():
    """Récupérer tous les KPIs"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('SELECT * FROM kpis ORDER BY created_date DESC')
        rows = cursor.fetchall()

        kpis = []
        for row in rows:
            kpi = {
                'id': row['id'],
                'name': row['name'],
                'category': row['category'],
                'currentValue': float(row['current_value']) if row['current_value'] else 0,
                'targetValue': float(row['target_value']) if row['target_value'] else 0,
                'unit': row['unit'],
                'period': row['period'],
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            kpis.append(kpi)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(kpis),
            'data': kpis
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS LOGISTIQUE =====

@app.route('/api/suppliers', methods=['GET'])
def get_suppliers():
    """Récupérer tous les fournisseurs"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('SELECT * FROM suppliers ORDER BY created_date DESC')
        rows = cursor.fetchall()

        suppliers = []
        for row in rows:
            supplier = {
                'id': row['id'],
                'supplierCode': row['supplier_code'],
                'companyName': row['company_name'],
                'contactPerson': row['contact_person'],
                'email': row['email'],
                'phone': row['phone'],
                'address': row['address'],
                'city': row['city'],
                'postalCode': row['postal_code'],
                'country': row['country'],
                'industry': row['industry'],
                'paymentTerms': row['payment_terms'],
                'creditRating': row['credit_rating'],
                'preferred': row['preferred'],
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            suppliers.append(supplier)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(suppliers),
            'data': suppliers
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/purchase-orders', methods=['GET'])
def get_purchase_orders():
    """Récupérer toutes les commandes d'achat"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT po.*, s.company_name as supplier_name, e.first_name || ' ' || e.last_name as buyer_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN employees e ON po.buyer_id = e.id
            ORDER BY po.created_date DESC
        ''')
        rows = cursor.fetchall()

        purchase_orders = []
        for row in rows:
            po = {
                'id': row['id'],
                'poNumber': row['po_number'],
                'supplierId': row['supplier_id'],
                'supplierName': row['supplier_name'],
                'requestId': row['request_id'],
                'orderDate': row['order_date'].isoformat() if row['order_date'] else None,
                'expectedDeliveryDate': row['expected_delivery_date'].isoformat() if row['expected_delivery_date'] else None,
                'status': row['status'],
                'totalAmount': float(row['total_amount']) if row['total_amount'] else 0,
                'taxAmount': float(row['tax_amount']) if row['tax_amount'] else 0,
                'shippingCost': float(row['shipping_cost']) if row['shipping_cost'] else 0,
                'paymentTerms': row['payment_terms'],
                'deliveryAddress': row['delivery_address'],
                'buyerId': row['buyer_id'],
                'buyerName': row['buyer_name'],
                'notes': row['notes'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            purchase_orders.append(po)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(purchase_orders),
            'data': purchase_orders
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/shipments', methods=['GET'])
def get_shipments():
    """Récupérer toutes les expéditions"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT s.*, w.name as warehouse_name, o.order_number
            FROM shipments s
            LEFT JOIN warehouses w ON s.warehouse_id = w.id
            LEFT JOIN orders o ON s.order_id = o.id
            ORDER BY s.created_date DESC
        ''')
        rows = cursor.fetchall()

        shipments = []
        for row in rows:
            shipment = {
                'id': row['id'],
                'shipmentNumber': row['shipment_number'],
                'orderId': row['order_id'],
                'orderNumber': row['order_number'],
                'warehouseId': row['warehouse_id'],
                'warehouseName': row['warehouse_name'],
                'carrier': row['carrier'],
                'trackingNumber': row['tracking_number'],
                'shipDate': row['ship_date'].isoformat() if row['ship_date'] else None,
                'expectedDeliveryDate': row['expected_delivery_date'].isoformat() if row['expected_delivery_date'] else None,
                'actualDeliveryDate': row['actual_delivery_date'].isoformat() if row['actual_delivery_date'] else None,
                'status': row['status'],
                'shippingCost': float(row['shipping_cost']) if row['shipping_cost'] else 0,
                'weight': float(row['weight']) if row['weight'] else 0,
                'dimensions': row['dimensions'],
                'destinationAddress': row['destination_address'],
                'specialInstructions': row['special_instructions'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            shipments.append(shipment)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(shipments),
            'data': shipments
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/warehouses', methods=['GET'])
def get_warehouses():
    """Récupérer tous les entrepôts"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT w.*, e.first_name || ' ' || e.last_name as manager_name
            FROM warehouses w
            LEFT JOIN employees e ON w.manager_id = e.id
            ORDER BY w.created_date DESC
        ''')
        rows = cursor.fetchall()

        warehouses = []
        for row in rows:
            warehouse = {
                'id': row['id'],
                'warehouseCode': row['warehouse_code'],
                'name': row['name'],
                'address': row['address'],
                'city': row['city'],
                'postalCode': row['postal_code'],
                'country': row['country'],
                'managerId': row['manager_id'],
                'managerName': row['manager_name'],
                'capacity': float(row['capacity']) if row['capacity'] else 0,
                'currentUtilization': float(row['current_utilization']) if row['current_utilization'] else 0,
                'warehouseType': row['warehouse_type'],
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            warehouses.append(warehouse)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(warehouses),
            'data': warehouses
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS STOCKS =====

@app.route('/api/products', methods=['GET'])
def get_products():
    """Récupérer tous les produits"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT p.*, s.company_name as supplier_name
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.created_date DESC
        ''')
        rows = cursor.fetchall()

        products = []
        for row in rows:
            product = {
                'id': row['id'],
                'productCode': row['product_code'],
                'name': row['name'],
                'description': row['description'],
                'category': row['category'],
                'brand': row['brand'],
                'unitOfMeasure': row['unit_of_measure'],
                'weight': float(row['weight']) if row['weight'] else 0,
                'dimensions': row['dimensions'],
                'costPrice': float(row['cost_price']) if row['cost_price'] else 0,
                'sellingPrice': float(row['selling_price']) if row['selling_price'] else 0,
                'minStockLevel': row['min_stock_level'],
                'maxStockLevel': row['max_stock_level'],
                'reorderPoint': row['reorder_point'],
                'supplierId': row['supplier_id'],
                'supplierName': row['supplier_name'],
                'barcode': row['barcode'],
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            products.append(product)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(products),
            'data': products
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/inventory', methods=['GET'])
def get_inventory():
    """Récupérer l'inventaire complet"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT i.*, p.name as product_name, p.product_code, w.name as warehouse_name, w.warehouse_code
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.id
            LEFT JOIN warehouses w ON i.warehouse_id = w.id
            ORDER BY i.last_movement_date DESC
        ''')
        rows = cursor.fetchall()

        inventory = []
        for row in rows:
            item = {
                'id': row['id'],
                'productId': row['product_id'],
                'productName': row['product_name'],
                'productCode': row['product_code'],
                'warehouseId': row['warehouse_id'],
                'warehouseName': row['warehouse_name'],
                'warehouseCode': row['warehouse_code'],
                'quantityOnHand': row['quantity_on_hand'],
                'quantityReserved': row['quantity_reserved'],
                'quantityAvailable': row['quantity_available'],
                'lastCountDate': row['last_count_date'].isoformat() if row['last_count_date'] else None,
                'lastMovementDate': row['last_movement_date'].isoformat() if row['last_movement_date'] else None,
                'location': row['location'],
                'batchNumber': row['batch_number'],
                'expiryDate': row['expiry_date'].isoformat() if row['expiry_date'] else None,
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            inventory.append(item)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(inventory),
            'data': inventory
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stock-movements', methods=['GET'])
def get_stock_movements():
    """Récupérer tous les mouvements de stock"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT sm.*, p.name as product_name, p.product_code, w.name as warehouse_name,
                   e.first_name || ' ' || e.last_name as performed_by_name
            FROM stock_movements sm
            LEFT JOIN products p ON sm.product_id = p.id
            LEFT JOIN warehouses w ON sm.warehouse_id = w.id
            LEFT JOIN employees e ON sm.performed_by = e.id
            ORDER BY sm.movement_date DESC
            LIMIT 1000
        ''')
        rows = cursor.fetchall()

        movements = []
        for row in rows:
            movement = {
                'id': row['id'],
                'productId': row['product_id'],
                'productName': row['product_name'],
                'productCode': row['product_code'],
                'warehouseId': row['warehouse_id'],
                'warehouseName': row['warehouse_name'],
                'movementType': row['movement_type'],
                'quantity': row['quantity'],
                'referenceType': row['reference_type'],
                'referenceId': row['reference_id'],
                'reason': row['reason'],
                'costPerUnit': float(row['cost_per_unit']) if row['cost_per_unit'] else 0,
                'totalCost': float(row['total_cost']) if row['total_cost'] else 0,
                'performedBy': row['performed_by'],
                'performedByName': row['performed_by_name'],
                'movementDate': row['movement_date'].isoformat() if row['movement_date'] else None,
                'notes': row['notes']
            }
            movements.append(movement)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(movements),
            'data': movements
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS CRM AVANCÉ =====

@app.route('/api/customers', methods=['GET'])
def get_customers():
    """Récupérer les clients (basé sur contacts)"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute("SELECT * FROM contacts WHERE contact_type = 'client' ORDER BY created_date DESC")
        rows = cursor.fetchall()

        customers = []
        for row in rows:
            customer = {
                'id': row['id'],
                'companyName': row['company_name'],
                'contactName': row['contact_name'],
                'email': row['email'],
                'phone': row['phone'],
                'address': row['address'],
                'status': row['status'],
                'notes': row['notes'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None
            }
            customers.append(customer)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(customers),
            'data': customers
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/opportunities', methods=['GET'])
def get_opportunities():
    """Récupérer toutes les opportunités"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT o.*, c.company_name as customer_name, e.first_name || ' ' || e.last_name as sales_rep
            FROM opportunities o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN employees e ON o.sales_rep_id = e.id
            ORDER BY o.created_date DESC
        ''')
        rows = cursor.fetchall()

        opportunities = []
        for row in rows:
            opportunity = {
                'id': row['id'],
                'customerId': row['customer_id'],
                'customerName': row['customer_name'] or 'Client inconnu',
                'title': row['title'],
                'description': row['description'],
                'value': float(row['value']) if row['value'] else 0,
                'probability': float(row['probability']) if row['probability'] else 0,
                'stage': row['stage'],
                'expectedCloseDate': row['expected_close_date'].isoformat() if row['expected_close_date'] else None,
                'actualCloseDate': row['actual_close_date'].isoformat() if row['actual_close_date'] else None,
                'salesRepId': row['sales_rep_id'],
                'salesRep': row['sales_rep'],
                'source': row['source'],
                'competitor': row['competitor'],
                'nextAction': row['next_action'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            opportunities.append(opportunity)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(opportunities),
            'data': opportunities
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/orders', methods=['GET'])
def get_orders():
    """Récupérer toutes les commandes"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT o.*, c.company_name as customer_name, e.first_name || ' ' || e.last_name as sales_rep
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN employees e ON o.sales_rep_id = e.id
            ORDER BY o.created_date DESC
        ''')
        rows = cursor.fetchall()

        orders = []
        for row in rows:
            order = {
                'id': row['id'],
                'orderNumber': row['order_number'],
                'customerId': row['customer_id'],
                'customerName': row['customer_name'] or 'Client inconnu',
                'opportunityId': row['opportunity_id'],
                'orderDate': row['order_date'].isoformat() if row['order_date'] else None,
                'deliveryDate': row['delivery_date'].isoformat() if row['delivery_date'] else None,
                'status': row['status'],
                'totalAmount': float(row['total_amount']) if row['total_amount'] else 0,
                'taxAmount': float(row['tax_amount']) if row['tax_amount'] else 0,
                'discountAmount': float(row['discount_amount']) if row['discount_amount'] else 0,
                'shippingAddress': row['shipping_address'],
                'billingAddress': row['billing_address'],
                'paymentMethod': row['payment_method'],
                'paymentStatus': row['payment_status'],
                'salesRepId': row['sales_rep_id'],
                'salesRep': row['sales_rep'],
                'notes': row['notes'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            orders.append(order)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(orders),
            'data': orders
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/quotes', methods=['GET'])
def get_quotes():
    """Récupérer tous les devis"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT * FROM quotes ORDER BY created_date DESC
        ''')
        rows = cursor.fetchall()

        quotes = []
        for row in rows:
            quote = {
                'id': row['id'],
                'quoteNumber': row['quote_number'],
                'clientId': row['client_id'],
                'customerName': row['client_name'],
                'createdDate': row['quote_date'].isoformat() if row['quote_date'] else None,
                'expirationDate': row['validity_date'].isoformat() if row['validity_date'] else None,
                'amountHT': float(row['amount_ht']) if row['amount_ht'] else 0,
                'amountTVA': float(row['amount_tva']) if row['amount_tva'] else 0,
                'amountTTC': float(row['amount_ttc']) if row['amount_ttc'] else 0,
                'status': row['status'],
                'description': row['description'],
                'filePath': row['file_path'],
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            quotes.append(quote)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(quotes),
            'data': quotes
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/interactions', methods=['GET'])
def get_interactions():
    """Récupérer toutes les interactions"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT i.*, c.company_name as customer_name, ct.first_name || ' ' || ct.last_name as contact_name,
                   e.first_name || ' ' || e.last_name as assigned_to_name
            FROM interactions i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN contacts ct ON i.contact_id = ct.id
            LEFT JOIN employees e ON i.assigned_to = e.id
            ORDER BY i.interaction_date DESC
        ''')
        rows = cursor.fetchall()

        interactions = []
        for row in rows:
            interaction = {
                'id': row['id'],
                'customerId': row['customer_id'],
                'customerName': row['customer_name'],
                'contactId': row['contact_id'],
                'contactName': row['contact_name'],
                'interactionType': row['interaction_type'],
                'subject': row['subject'],
                'description': row['description'],
                'interactionDate': row['interaction_date'].isoformat() if row['interaction_date'] else None,
                'durationMinutes': row['duration_minutes'],
                'outcome': row['outcome'],
                'nextAction': row['next_action'],
                'nextActionDate': row['next_action_date'].isoformat() if row['next_action_date'] else None,
                'assignedTo': row['assigned_to'],
                'assignedToName': row['assigned_to_name'],
                'priority': row['priority'],
                'status': row['status'],
                'createdDate': row['created_date'].isoformat() if row['created_date'] else None,
                'modifiedDate': row['modified_date'].isoformat() if row['modified_date'] else None
            }
            interactions.append(interaction)

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'count': len(interactions),
            'data': interactions
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS IMPORT/EXPORT =====

@app.route('/api/budgets/import', methods=['POST'])
def import_budgets():
    """Importer des budgets en lot"""
    try:
        import_data = request.get_json()
        budgets = import_data.get('budgets', [])
        
        success_count = 0
        error_count = 0
        errors = []
        
        for budget in budgets:
            try:
                # Générer un ID unique si pas fourni
                if 'id' not in budget:
                    budget['id'] = f"budget_{datetime.now().timestamp()}_{success_count}"
                
                budget['createdDate'] = datetime.now().isoformat()
                budget['modifiedDate'] = datetime.now().isoformat()
                
                if db.create_budget(budget):
                    success_count += 1
                else:
                    error_count += 1
                    errors.append(f"Erreur création budget {budget.get('id', 'inconnu')}")
                    
            except Exception as e:
                error_count += 1
                errors.append(f"Erreur budget {budget.get('id', 'inconnu')}: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': f'{success_count} budgets importés, {error_count} erreurs',
            'imported': success_count,
            'errors': error_count,
            'error_details': errors
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/budgets/export', methods=['GET'])
def export_budgets():
    """Exporter tous les budgets"""
    try:
        budgets = db.read_budgets()
        
        return jsonify({
            'success': True,
            'data': budgets,
            'count': len(budgets),
            'exported_at': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS INTELLIGENCE ARTIFICIELLE =====

@app.route('/api/ai/predict-budgets', methods=['POST'])
def ai_predict_budgets():
    """Prédictions budgétaires IA"""
    try:
        # Récupérer les budgets actuels
        budgets = db.read_budgets()

        # Analyser avec l'IA
        predictions = ai_module.predict_budget_trends(budgets)

        return jsonify(predictions)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai/analyze-kpis', methods=['POST'])
def ai_analyze_kpis():
    """Analyse des KPIs par IA"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('SELECT * FROM kpis ORDER BY created_date DESC')
        rows = cursor.fetchall()

        kpis = []
        for row in rows:
            kpi = {
                'id': row['id'],
                'name': row['name'],
                'category': row['category'],
                'currentValue': float(row['current_value']) if row['current_value'] else 0,
                'targetValue': float(row['target_value']) if row['target_value'] else 0,
                'unit': row['unit'],
                'period': row['period'],
                'status': row['status']
            }
            kpis.append(kpi)

        cursor.close()
        conn.close()

        # Analyser avec l'IA
        analysis = ai_module.analyze_kpis(kpis)

        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai/chat', methods=['POST'])
def ai_chat():
    """Assistant conversationnel IA"""
    try:
        data = request.get_json()
        message = data.get('message', '')

        if not message:
            return jsonify({
                'success': False,
                'error': 'Message requis'
            }), 400

        # Préparer le contexte avec les données actuelles
        context = {
            'budgets_count': len(db.read_budgets()),
            'timestamp': datetime.now().isoformat()
        }

        # Obtenir la réponse de l'IA
        response = ai_module.chat_assistant(message, context)

        return jsonify(response)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai/insights', methods=['GET'])
def ai_get_insights():
    """Obtenir des insights IA globaux"""
    try:
        # Récupérer toutes les données
        budgets = db.read_budgets()
        stats = db.get_database_stats()

        # Générer des insights combinés
        budget_predictions = ai_module.predict_budget_trends(budgets)
        budget_anomalies = ai_module.detect_anomalies(budgets, 'budgets')

        insights = {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_budgets': len(budgets),
                'database_stats': stats,
                'ai_analysis_date': datetime.now().isoformat()
            },
            'budget_insights': budget_predictions.get('insights', []),
            'anomalies': budget_anomalies.get('anomalies', []),
            'recommendations': [
                "📊 Consultez régulièrement les prédictions IA",
                "🔍 Surveillez les anomalies détectées",
                "📈 Optimisez les budgets selon les recommandations IA"
            ]
        }

        return jsonify(insights)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai/analyze-variances', methods=['POST'])
def ai_analyze_variances():
    """Analyse experte des écarts budgétaires"""
    try:
        # Récupérer les budgets actuels
        budgets = db.read_budgets()

        # Analyser avec l'expertise contrôle de gestion
        analysis = ai_module.analyze_budget_variances(budgets)

        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS RECHERCHE DOCUMENTAIRE =====

@app.route('/api/documents/search', methods=['POST'])
def search_documents():
    """Recherche dans tous les documents"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        filters = data.get('filters', {})

        results = document_search.search_documents(query, filters)

        return jsonify(results)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/documents/statistics', methods=['GET'])
def get_document_statistics():
    """Obtenir les statistiques des documents"""
    try:
        stats = document_search.get_document_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/documents/suggestions', methods=['POST'])
def get_search_suggestions():
    """Obtenir des suggestions de recherche"""
    try:
        data = request.get_json()
        partial_query = data.get('query', '')

        suggestions = document_search.get_search_suggestions(partial_query)

        return jsonify({
            'success': True,
            'suggestions': suggestions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/documents/<table_name>/<doc_id>', methods=['GET'])
def get_document_by_id(table_name, doc_id):
    """Récupérer un document spécifique"""
    try:
        result = document_search.get_document_by_id(table_name, doc_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ENDPOINTS ANALYSE CROISÉE =====

@app.route('/api/analysis/clients', methods=['GET'])
def analyze_clients():
    """Analyse de performance des clients"""
    try:
        analysis = cross_analysis.analyze_client_performance()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/suppliers', methods=['GET'])
def analyze_suppliers():
    """Analyse des dépenses fournisseurs"""
    try:
        analysis = cross_analysis.analyze_supplier_spending()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/budget-reality', methods=['GET'])
def analyze_budget_reality():
    """Analyse budget vs réalité avec détails"""
    try:
        analysis = cross_analysis.analyze_budget_vs_reality()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/cash-flow', methods=['GET'])
def analyze_cash_flow():
    """Analyse des flux de trésorerie par source"""
    try:
        analysis = cross_analysis.analyze_cash_flow_by_source()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/comprehensive', methods=['GET'])
def comprehensive_analysis():
    """Tableau de bord complet avec toutes les analyses croisées"""
    try:
        analysis = cross_analysis.get_comprehensive_dashboard()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/contacts-potential', methods=['GET'])
def analyze_contacts_potential():
    """Analyse du potentiel business des contacts"""
    try:
        analysis = cross_analysis.analyze_contacts_business_potential()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== GESTION DES ERREURS =====

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint non trouvé'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Erreur interne du serveur'
    }), 500

# ===== LANCEMENT DU SERVEUR =====

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API ERP HUB...")
    print("📊 Base de données initialisée")
    
    # Afficher les statistiques au démarrage
    stats = db.get_database_stats()
    print(f"📈 Statistiques : {stats}")
    
    print("🌐 Serveur disponible sur : http://localhost:5000")
    print("📋 Endpoints disponibles :")
    print("   === FINANCE & BUDGET ===")
    print("   GET  /api/budgets          - Récupérer tous les budgets")
    print("   POST /api/budgets          - Créer un nouveau budget")
    print("   PUT  /api/budgets/<id>     - Mettre à jour un budget")
    print("   DELETE /api/budgets/<id>   - Supprimer un budget")
    print("   POST /api/budgets/import   - Import en lot")
    print("   GET  /api/budgets/export   - Export complet")
    print("   === DASHBOARD ===")
    print("   GET  /api/dashboard        - Données tableau de bord")
    print("   === RESSOURCES HUMAINES ===")
    print("   GET  /api/employees        - Récupérer tous les employés")
    print("   GET  /api/leaves           - Récupérer tous les congés")
    print("   === CRM ===")
    print("   GET  /api/contacts         - Récupérer tous les contacts")
    print("   GET  /api/customers        - Récupérer les clients")
    print("   GET  /api/opportunities    - Récupérer les opportunités")
    print("   GET  /api/orders           - Récupérer les commandes")
    print("   GET  /api/quotes           - Récupérer les devis")
    print("   GET  /api/interactions     - Récupérer les interactions")
    print("   === BUSINESS INTELLIGENCE ===")
    print("   GET  /api/kpis             - Récupérer tous les KPIs")
    print("   === ACHATS & LOGISTIQUE ===")
    print("   GET  /api/suppliers        - Récupérer les fournisseurs")
    print("   GET  /api/purchase-orders  - Récupérer les commandes d'achat")
    print("   GET  /api/shipments        - Récupérer les expéditions")
    print("   GET  /api/warehouses       - Récupérer les entrepôts")
    print("   === STOCKS ===")
    print("   GET  /api/products         - Récupérer tous les produits")
    print("   GET  /api/inventory        - Récupérer l'inventaire complet")
    print("   GET  /api/stock-movements  - Récupérer les mouvements de stock")
    print("   === INTELLIGENCE ARTIFICIELLE ===")
    print("   POST /api/ai/predict-budgets  - Prédictions budgétaires IA")
    print("   POST /api/ai/analyze-kpis     - Analyse KPIs par IA")
    print("   POST /api/ai/analyze-variances - Analyse experte des écarts")
    print("   POST /api/ai/chat             - Assistant conversationnel IA")
    print("   GET  /api/ai/insights         - Insights IA globaux")
    print("   === RECHERCHE DOCUMENTAIRE ===")
    print("   POST /api/documents/search    - Recherche dans tous les documents")
    print("   GET  /api/documents/statistics - Statistiques des documents")
    print("   POST /api/documents/suggestions - Suggestions de recherche")
    print("   GET  /api/documents/<type>/<id> - Récupérer un document")
    print("   === ANALYSES CROISÉES ===")
    print("   GET  /api/analysis/clients    - Analyse performance clients")
    print("   GET  /api/analysis/suppliers  - Analyse dépenses fournisseurs")
    print("   GET  /api/analysis/budget-reality - Analyse budget vs réalité")
    print("   GET  /api/analysis/cash-flow  - Analyse flux de trésorerie")
    print("   GET  /api/analysis/comprehensive - Tableau de bord complet")
    print("   === UTILITAIRES ===")
    print("   POST /api/history          - Sauvegarder action historique")
    print("   GET  /api/stats            - Statistiques base de données")
    print("   GET  /api/health           - État du serveur")
    
    # Lancer le serveur
    app.run(debug=True, host='0.0.0.0', port=5000)
