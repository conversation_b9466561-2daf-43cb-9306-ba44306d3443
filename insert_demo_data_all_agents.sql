-- 📊 DONNÉES DE DÉMONSTRATION POUR TOUS LES AGENTS ERP
-- Insertion de données réalistes pour tester tous les modules

-- ===== AGENT HR (RESSOURCES HUMAINES) =====

-- Employés
INSERT INTO employees (id, employee_number, first_name, last_name, email, phone, position, department, hire_date, salary, status) VALUES
('emp_001', 'EMP001', '<PERSON>', '<PERSON><PERSON>', '<EMAIL>', '***********.89', 'Directeur Général', 'Direction', '2020-01-15', 85000, 'active'),
('emp_002', 'EMP002', '<PERSON>', '<PERSON>', '<EMAIL>', '***********.90', 'Directrice Commerciale', 'Commercial', '2020-03-01', 65000, 'active'),
('emp_003', 'EMP003', '<PERSON>', '<PERSON><PERSON>', '<EMAIL>', '***********.91', 'Responsable RH', '<PERSON><PERSON><PERSON><PERSON> Hu<PERSON>', '2020-06-15', 55000, 'active'),
('emp_004', '<PERSON><PERSON>004', '<PERSON>', '<PERSON>', '<EMAIL>', '***********.92', 'Directrice <PERSON>', 'Informatique', '2021-01-10', 70000, 'active'),
('emp_005', 'EMP005', '<PERSON>', '<PERSON>', '<EMAIL>', '***********.93', 'Directeur Financier', 'Finance', '2020-09-01', 75000, 'active'),
('emp_006', 'EMP006', 'Anne', 'Petit', '<EMAIL>', '***********.94', 'Commercial Senior', 'Commercial', '2021-03-15', 45000, 'active'),
('emp_007', 'EMP007', 'Thomas', 'Moreau', '<EMAIL>', '***********.95', 'Responsable Achats', 'Achats', '2021-06-01', 50000, 'active'),
('emp_008', 'EMP008', 'Julie', 'Roux', '<EMAIL>', '***********.96', 'Responsable Logistique', 'Logistique', '2021-09-15', 48000, 'active');

-- Congés
INSERT INTO leaves (id, employee_id, leave_type, start_date, end_date, days_count, status, reason, approved_by) VALUES
('leave_001', 'emp_002', 'vacation', '2024-07-15', '2024-07-29', 15, 'approved', 'Congés d''été', 'emp_001'),
('leave_002', 'emp_003', 'sick', '2024-06-10', '2024-06-12', 3, 'approved', 'Grippe', 'emp_001'),
('leave_003', 'emp_006', 'personal', '2024-08-05', '2024-08-05', 1, 'pending', 'Rendez-vous médical', NULL);

-- Évaluations
INSERT INTO evaluations (id, employee_id, evaluator_id, period_start, period_end, overall_score, goals_achievement, comments, status) VALUES
('eval_001', 'emp_002', 'emp_001', '2024-01-01', '2024-06-30', 4.2, 4.5, 'Excellente performance commerciale, objectifs dépassés', 'completed'),
('eval_002', 'emp_003', 'emp_001', '2024-01-01', '2024-06-30', 4.0, 4.0, 'Très bon travail en RH, gestion efficace des équipes', 'completed');

-- ===== AGENT SALES (VENTES) =====

-- Clients
INSERT INTO customers (id, customer_code, company_name, contact_person, email, phone, address, city, postal_code, country, industry, customer_type, credit_limit, sales_rep_id, status) VALUES
('cust_001', 'CUST001', 'TechCorp Solutions', 'Marc Dubois', '<EMAIL>', '***********.90', '123 Rue de la Tech', 'Paris', '75001', 'France', 'Technologie', 'client', 100000, 'emp_002', 'active'),
('cust_002', 'CUST002', 'InnovatePlus SARL', 'Claire Rousseau', '<EMAIL>', '***********.91', '456 Avenue Innovation', 'Lyon', '69000', 'France', 'Conseil', 'client', 75000, 'emp_006', 'active'),
('cust_003', 'CUST003', 'GlobalTrade Inc', 'David Wilson', '<EMAIL>', '+**********.78.92', '789 Boulevard Commerce', 'Marseille', '13000', 'France', 'Commerce', 'prospect', 50000, 'emp_002', 'active');

-- Opportunités
INSERT INTO opportunities (id, customer_id, title, description, value, probability, stage, expected_close_date, sales_rep_id, source) VALUES
('opp_001', 'cust_001', 'Projet ERP Complet', 'Implémentation complète du système ERP', 250000, 85, 'negotiation', '2024-08-15', 'emp_002', 'referral'),
('opp_002', 'cust_002', 'Module CRM', 'Déploiement du module CRM uniquement', 75000, 60, 'proposal', '2024-07-30', 'emp_006', 'website'),
('opp_003', 'cust_003', 'Consultation ERP', 'Audit et recommandations ERP', 25000, 40, 'qualification', '2024-09-15', 'emp_002', 'cold_call');

-- Commandes
INSERT INTO orders (id, order_number, customer_id, opportunity_id, order_date, delivery_date, status, total_amount, tax_amount, sales_rep_id) VALUES
('ord_001', 'ORD001', 'cust_001', 'opp_001', '2024-06-01', '2024-08-01', 'confirmed', 250000, 50000, 'emp_002'),
('ord_002', 'ORD002', 'cust_002', 'opp_002', '2024-06-15', '2024-07-15', 'shipped', 75000, 15000, 'emp_006');

-- ===== AGENT PURCHASE (ACHATS) =====

-- Fournisseurs
INSERT INTO suppliers (id, supplier_code, company_name, contact_person, email, phone, address, city, country, industry, payment_terms, status) VALUES
('supp_001', 'SUPP001', 'TechSupply Pro', 'Michel Lecomte', '<EMAIL>', '***********.01', '321 Rue Fournisseur', 'Paris', 'France', 'Technologie', 30, 'active'),
('supp_002', 'SUPP002', 'Office Solutions', 'Sylvie Moreau', '<EMAIL>', '***********.02', '654 Avenue Bureau', 'Lyon', 'France', 'Bureautique', 45, 'active'),
('supp_003', 'SUPP003', 'LogiTrans Express', 'Paul Girard', '<EMAIL>', '***********.03', '987 Boulevard Transport', 'Marseille', 'France', 'Transport', 15, 'active');

-- Demandes d'achat
INSERT INTO purchase_requests (id, request_number, requester_id, department, request_date, needed_date, priority, status, total_estimated_cost, justification) VALUES
('req_001', 'REQ001', 'emp_004', 'Informatique', '2024-06-01', '2024-07-01', 'high', 'approved', 15000, 'Renouvellement serveurs'),
('req_002', 'REQ002', 'emp_003', 'Ressources Humaines', '2024-06-05', '2024-06-20', 'medium', 'approved', 3000, 'Mobilier bureau'),
('req_003', 'REQ003', 'emp_008', 'Logistique', '2024-06-10', '2024-07-15', 'high', 'pending', 8000, 'Équipement entrepôt');

-- Bons de commande
INSERT INTO purchase_orders (id, po_number, supplier_id, request_id, order_date, expected_delivery_date, status, total_amount, tax_amount, buyer_id) VALUES
('po_001', 'PO001', 'supp_001', 'req_001', '2024-06-05', '2024-06-25', 'sent', 15000, 3000, 'emp_007'),
('po_002', 'PO002', 'supp_002', 'req_002', '2024-06-08', '2024-06-18', 'acknowledged', 3000, 600, 'emp_007');

-- ===== AGENT LOGISTICS (LOGISTIQUE) =====

-- Entrepôts
INSERT INTO warehouses (id, warehouse_code, name, address, city, country, manager_id, capacity, current_utilization, warehouse_type, status) VALUES
('wh_001', 'WH001', 'Entrepôt Principal Paris', '100 Zone Industrielle', 'Paris', 'France', 'emp_008', 10000, 65.5, 'main', 'active'),
('wh_002', 'WH002', 'Entrepôt Régional Lyon', '200 Zone Logistique', 'Lyon', 'France', 'emp_008', 5000, 45.2, 'regional', 'active'),
('wh_003', 'WH003', 'Centre Distribution Marseille', '300 Port Commerce', 'Marseille', 'France', 'emp_008', 7500, 38.8, 'distribution', 'active');

-- Expéditions
INSERT INTO shipments (id, shipment_number, order_id, warehouse_id, carrier, tracking_number, ship_date, expected_delivery_date, status, shipping_cost, weight) VALUES
('ship_001', 'SHIP001', 'ord_001', 'wh_001', 'DHL Express', 'DHL123456789', '2024-06-02', '2024-06-05', 'delivered', 150.50, 25.5),
('ship_002', 'SHIP002', 'ord_002', 'wh_002', 'Chronopost', 'CHR987654321', '2024-06-16', '2024-06-18', 'in_transit', 89.75, 12.3);

-- ===== AGENT STOCK (INVENTAIRE) =====

-- Produits
INSERT INTO products (id, product_code, name, description, category, brand, unit_of_measure, cost_price, selling_price, min_stock_level, reorder_point, supplier_id, status) VALUES
('prod_001', 'PROD001', 'Licence ERP Standard', 'Licence logiciel ERP module standard', 'Logiciel', 'ERP-HUB', 'unité', 500, 1000, 10, 20, 'supp_001', 'active'),
('prod_002', 'PROD002', 'Licence ERP Premium', 'Licence logiciel ERP module premium', 'Logiciel', 'ERP-HUB', 'unité', 800, 1500, 5, 10, 'supp_001', 'active'),
('prod_003', 'PROD003', 'Formation Utilisateur', 'Formation 2 jours utilisateur ERP', 'Service', 'ERP-HUB', 'jour', 300, 600, 0, 0, NULL, 'active'),
('prod_004', 'PROD004', 'Support Technique', 'Support technique annuel', 'Service', 'ERP-HUB', 'année', 1000, 2000, 0, 0, NULL, 'active');

-- Inventaire
INSERT INTO inventory (id, product_id, warehouse_id, quantity_on_hand, quantity_reserved, last_count_date, location) VALUES
('inv_001', 'prod_001', 'wh_001', 150, 25, '2024-06-01', 'A-01-01'),
('inv_002', 'prod_002', 'wh_001', 75, 15, '2024-06-01', 'A-01-02'),
('inv_003', 'prod_001', 'wh_002', 100, 10, '2024-06-01', 'B-01-01'),
('inv_004', 'prod_002', 'wh_002', 50, 5, '2024-06-01', 'B-01-02');

-- Mouvements de stock
INSERT INTO stock_movements (id, product_id, warehouse_id, movement_type, quantity, reference_type, reference_id, reason, cost_per_unit, total_cost, performed_by, movement_date) VALUES
('mov_001', 'prod_001', 'wh_001', 'in', 100, 'purchase_order', 'po_001', 'Réception commande', 500, 50000, 'emp_008', '2024-06-01 10:00:00'),
('mov_002', 'prod_001', 'wh_001', 'out', 25, 'order', 'ord_001', 'Expédition client', 500, 12500, 'emp_008', '2024-06-02 14:30:00'),
('mov_003', 'prod_002', 'wh_001', 'in', 50, 'purchase_order', 'po_001', 'Réception commande', 800, 40000, 'emp_008', '2024-06-01 10:30:00');

-- ===== AGENT ACCOUNTING (COMPTABILITÉ) =====

-- Plan comptable
INSERT INTO chart_of_accounts (id, account_code, account_name, account_type, level, is_active) VALUES
('acc_001', '101', 'Capital social', 'equity', 1, true),
('acc_002', '411', 'Clients', 'asset', 1, true),
('acc_003', '401', 'Fournisseurs', 'liability', 1, true),
('acc_004', '701', 'Ventes de produits', 'revenue', 1, true),
('acc_005', '607', 'Achats de marchandises', 'expense', 1, true),
('acc_006', '512', 'Banque', 'asset', 1, true),
('acc_007', '641', 'Rémunérations du personnel', 'expense', 1, true);

-- Écritures comptables
INSERT INTO journal_entries (id, entry_number, entry_date, description, reference, total_debit, total_credit, status, created_by) VALUES
('je_001', 'JE001', '2024-06-01', 'Vente client TechCorp', 'ORD001', 300000, 300000, 'posted', 'emp_005'),
('je_002', 'JE002', '2024-06-05', 'Achat fournisseur TechSupply', 'PO001', 18000, 18000, 'posted', 'emp_005'),
('je_003', 'JE003', '2024-06-15', 'Salaires juin 2024', 'PAY202406', 45000, 45000, 'posted', 'emp_005');

-- Lignes d'écriture
INSERT INTO journal_entry_lines (id, entry_id, account_id, description, debit_amount, credit_amount, line_number) VALUES
('jel_001', 'je_001', 'acc_002', 'Client TechCorp', 300000, 0, 1),
('jel_002', 'je_001', 'acc_004', 'Vente ERP', 0, 250000, 2),
('jel_003', 'je_001', 'acc_004', 'TVA sur vente', 0, 50000, 3),
('jel_004', 'je_002', 'acc_005', 'Achat matériel IT', 15000, 0, 1),
('jel_005', 'je_002', 'acc_005', 'TVA sur achat', 3000, 0, 2),
('jel_006', 'je_002', 'acc_003', 'Fournisseur TechSupply', 0, 18000, 3);

-- ===== AGENT CRM (GESTION CLIENT) =====

-- Contacts
INSERT INTO contacts (id, customer_id, first_name, last_name, title, email, phone, department, is_primary, is_decision_maker) VALUES
('cont_001', 'cust_001', 'Marc', 'Dubois', 'Directeur IT', '<EMAIL>', '***********.90', 'Informatique', true, true),
('cont_002', 'cust_001', 'Sarah', 'Lemoine', 'Chef de Projet', '<EMAIL>', '***********.91', 'Projets', false, false),
('cont_003', 'cust_002', 'Claire', 'Rousseau', 'PDG', '<EMAIL>', '***********.92', 'Direction', true, true),
('cont_004', 'cust_003', 'David', 'Wilson', 'Responsable Achats', '<EMAIL>', '***********.93', 'Achats', true, true);

-- Interactions
INSERT INTO interactions (id, customer_id, contact_id, interaction_type, subject, description, interaction_date, duration_minutes, outcome, assigned_to, status) VALUES
('int_001', 'cust_001', 'cont_001', 'meeting', 'Présentation ERP', 'Démonstration complète du système ERP', '2024-05-15 14:00:00', 120, 'Très intéressé', 'emp_002', 'completed'),
('int_002', 'cust_001', 'cont_002', 'call', 'Suivi technique', 'Questions techniques sur l''implémentation', '2024-05-20 10:30:00', 45, 'Questions résolues', 'emp_004', 'completed'),
('int_003', 'cust_002', 'cont_003', 'email', 'Proposition commerciale', 'Envoi de la proposition pour le module CRM', '2024-05-25 09:00:00', 0, 'En attente retour', 'emp_006', 'completed'),
('int_004', 'cust_003', 'cont_004', 'demo', 'Démonstration produit', 'Démo en ligne du système ERP', '2024-06-10 15:00:00', 90, 'Demande de proposition', 'emp_002', 'completed');

-- ===== AGENT BI (BUSINESS INTELLIGENCE) =====

-- Rapports
INSERT INTO reports (id, report_name, report_type, description, schedule_frequency, created_by, is_active) VALUES
('rep_001', 'Chiffre d''affaires mensuel', 'financial', 'Rapport mensuel du CA par commercial', 'monthly', 'emp_005', true),
('rep_002', 'Performance commerciale', 'sales', 'Analyse des opportunités et conversions', 'weekly', 'emp_002', true),
('rep_003', 'Rotation des stocks', 'inventory', 'Analyse de la rotation des produits', 'monthly', 'emp_008', true),
('rep_004', 'Satisfaction client', 'crm', 'Suivi des interactions et satisfaction', 'quarterly', 'emp_002', true);

-- KPIs
INSERT INTO kpis (id, kpi_name, kpi_type, description, target_value, current_value, unit, frequency, responsible_person, status) VALUES
('kpi_001', 'Chiffre d''affaires mensuel', 'financial', 'CA mensuel total', 500000, 425000, 'EUR', 'monthly', 'emp_005', 'active'),
('kpi_002', 'Taux de conversion', 'sales', 'Pourcentage d''opportunités converties', 25, 22.5, '%', 'monthly', 'emp_002', 'active'),
('kpi_003', 'Délai moyen de livraison', 'logistics', 'Temps moyen entre commande et livraison', 5, 4.2, 'jours', 'weekly', 'emp_008', 'active'),
('kpi_004', 'Satisfaction client', 'crm', 'Note moyenne de satisfaction', 4.5, 4.2, 'note', 'monthly', 'emp_002', 'active'),
('kpi_005', 'Rotation des stocks', 'inventory', 'Nombre de rotations par an', 12, 10.5, 'fois', 'monthly', 'emp_008', 'active');

-- Message de confirmation
SELECT 'Données de démonstration insérées pour tous les agents ERP !' as message;
