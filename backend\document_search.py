"""
🔍 MODULE DE RECHERCHE DOCUMENTAIRE POUR ERP HUB
Système avancé de recherche et gestion des documents financiers
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import re

class DocumentSearchEngine:
    """Moteur de recherche documentaire avancé"""
    
    def __init__(self, db_connection):
        """Initialiser le moteur de recherche"""
        self.db = db_connection
        
        # Configuration des types de documents
        self.document_types = {
            'invoices_clients': {
                'name': 'Factures Clients',
                'icon': '📄',
                'fields': ['invoice_number', 'client_name', 'amount_ttc', 'status', 'invoice_date']
            },
            'invoices_suppliers': {
                'name': 'Factures Fournisseurs', 
                'icon': '📋',
                'fields': ['invoice_number', 'supplier_name', 'amount_ttc', 'status', 'invoice_date']
            },
            'purchase_orders': {
                'name': '<PERSON><PERSON> de <PERSON>e',
                'icon': '📦',
                'fields': ['order_number', 'supplier_name', 'amount_ttc', 'status', 'order_date']
            },
            'quotes': {
                'name': '<PERSON><PERSON>',
                'icon': '💰',
                'fields': ['quote_number', 'client_name', 'amount_ttc', 'status', 'quote_date']
            },
            'delivery_notes': {
                'name': 'Bons de Livraison',
                'icon': '🚚',
                'fields': ['delivery_number', 'client_name', 'status', 'delivery_date']
            }
        }
    
    def search_documents(self, query: str, filters: Dict = None) -> Dict[str, Any]:
        """Recherche globale dans tous les documents"""
        try:
            results = {}
            total_found = 0
            
            # Rechercher dans chaque type de document
            for doc_type, config in self.document_types.items():
                docs = self._search_in_table(doc_type, query, filters)
                if docs:
                    results[doc_type] = {
                        'name': config['name'],
                        'icon': config['icon'],
                        'count': len(docs),
                        'documents': docs
                    }
                    total_found += len(docs)
            
            return {
                'success': True,
                'query': query,
                'total_found': total_found,
                'results': results,
                'search_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de la recherche: {str(e)}'
            }
    
    def _search_in_table(self, table_name: str, query: str, filters: Dict = None) -> List[Dict]:
        """Recherche dans une table spécifique"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Construction de la requête de base
            base_query = f"SELECT * FROM {table_name} WHERE 1=1"
            params = []
            
            # Recherche textuelle
            if query and query.strip():
                search_conditions = []
                query_lower = query.lower()
                
                # Recherche dans les champs texte principaux
                if table_name in ['invoices_clients', 'quotes', 'delivery_notes']:
                    search_conditions.extend([
                        f"LOWER(client_name) LIKE %s",
                        f"LOWER(description) LIKE %s"
                    ])
                    params.extend([f'%{query_lower}%', f'%{query_lower}%'])
                    
                    if table_name == 'invoices_clients':
                        search_conditions.append("LOWER(invoice_number) LIKE %s")
                        params.append(f'%{query_lower}%')
                    elif table_name == 'quotes':
                        search_conditions.append("LOWER(quote_number) LIKE %s")
                        params.append(f'%{query_lower}%')
                    elif table_name == 'delivery_notes':
                        search_conditions.append("LOWER(delivery_number) LIKE %s")
                        params.append(f'%{query_lower}%')
                
                elif table_name in ['invoices_suppliers', 'purchase_orders']:
                    search_conditions.extend([
                        f"LOWER(supplier_name) LIKE %s",
                        f"LOWER(description) LIKE %s"
                    ])
                    params.extend([f'%{query_lower}%', f'%{query_lower}%'])
                    
                    if table_name == 'invoices_suppliers':
                        search_conditions.append("LOWER(invoice_number) LIKE %s")
                        params.append(f'%{query_lower}%')
                    elif table_name == 'purchase_orders':
                        search_conditions.append("LOWER(order_number) LIKE %s")
                        params.append(f'%{query_lower}%')
                
                if search_conditions:
                    base_query += f" AND ({' OR '.join(search_conditions)})"
            
            # Filtres additionnels
            if filters:
                if 'date_from' in filters and filters['date_from']:
                    date_field = self._get_date_field(table_name)
                    base_query += f" AND {date_field} >= %s"
                    params.append(filters['date_from'])
                
                if 'date_to' in filters and filters['date_to']:
                    date_field = self._get_date_field(table_name)
                    base_query += f" AND {date_field} <= %s"
                    params.append(filters['date_to'])
                
                if 'amount_min' in filters and filters['amount_min']:
                    base_query += " AND amount_ttc >= %s"
                    params.append(filters['amount_min'])
                
                if 'amount_max' in filters and filters['amount_max']:
                    base_query += " AND amount_ttc <= %s"
                    params.append(filters['amount_max'])
                
                if 'status' in filters and filters['status']:
                    base_query += " AND status = %s"
                    params.append(filters['status'])
            
            # Tri par date décroissante
            date_field = self._get_date_field(table_name)
            base_query += f" ORDER BY {date_field} DESC LIMIT 50"
            
            cursor.execute(base_query, params)
            columns = [desc[0] for desc in cursor.description]
            results = []
            
            for row in cursor.fetchall():
                doc = dict(zip(columns, row))
                # Formatage des dates et montants
                doc = self._format_document(doc, table_name)
                results.append(doc)
            
            cursor.close()
            conn.close()
            
            return results
            
        except Exception as e:
            print(f"Erreur recherche dans {table_name}: {str(e)}")
            return []
    
    def _get_date_field(self, table_name: str) -> str:
        """Obtenir le champ de date principal pour une table"""
        date_fields = {
            'invoices_clients': 'invoice_date',
            'invoices_suppliers': 'invoice_date', 
            'purchase_orders': 'order_date',
            'quotes': 'quote_date',
            'delivery_notes': 'delivery_date'
        }
        return date_fields.get(table_name, 'created_date')
    
    def _format_document(self, doc: Dict, table_name: str) -> Dict:
        """Formater un document pour l'affichage"""
        # Formatage des montants
        for field in ['amount_ht', 'amount_tva', 'amount_ttc']:
            if field in doc and doc[field] is not None:
                doc[f'{field}_formatted'] = f"{float(doc[field]):,.2f} €"
        
        # Formatage des dates
        date_field = self._get_date_field(table_name)
        if date_field in doc and doc[date_field]:
            if isinstance(doc[date_field], str):
                doc[f'{date_field}_formatted'] = doc[date_field]
            else:
                doc[f'{date_field}_formatted'] = doc[date_field].strftime('%d/%m/%Y')
        
        # Ajout du type de document
        doc['document_type'] = table_name
        doc['document_type_name'] = self.document_types[table_name]['name']
        doc['document_icon'] = self.document_types[table_name]['icon']
        
        return doc
    
    def get_document_by_id(self, table_name: str, doc_id: str) -> Dict[str, Any]:
        """Récupérer un document spécifique par son ID"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (doc_id,))
            columns = [desc[0] for desc in cursor.description]
            row = cursor.fetchone()
            
            if row:
                doc = dict(zip(columns, row))
                doc = self._format_document(doc, table_name)
                cursor.close()
                conn.close()
                
                return {
                    'success': True,
                    'document': doc
                }
            else:
                cursor.close()
                conn.close()
                return {
                    'success': False,
                    'error': 'Document non trouvé'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de la récupération: {str(e)}'
            }
    
    def get_search_suggestions(self, partial_query: str) -> List[str]:
        """Obtenir des suggestions de recherche"""
        suggestions = []
        
        if len(partial_query) >= 2:
            # Suggestions basées sur les noms de clients/fournisseurs
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # Clients
                cursor.execute("""
                    SELECT DISTINCT client_name FROM invoices_clients 
                    WHERE LOWER(client_name) LIKE %s 
                    LIMIT 5
                """, (f'%{partial_query.lower()}%',))
                
                for row in cursor.fetchall():
                    suggestions.append(row[0])
                
                # Fournisseurs
                cursor.execute("""
                    SELECT DISTINCT supplier_name FROM invoices_suppliers 
                    WHERE LOWER(supplier_name) LIKE %s 
                    LIMIT 5
                """, (f'%{partial_query.lower()}%',))
                
                for row in cursor.fetchall():
                    suggestions.append(row[0])
                
                cursor.close()
                conn.close()
                
            except Exception as e:
                print(f"Erreur suggestions: {str(e)}")
        
        return list(set(suggestions))  # Supprimer les doublons
    
    def get_document_statistics(self) -> Dict[str, Any]:
        """Obtenir des statistiques sur les documents"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            stats = {}
            
            for table_name, config in self.document_types.items():
                if table_name == 'delivery_notes':
                    # Les bons de livraison n'ont pas de montant
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    total = 0
                else:
                    cursor.execute(f"SELECT COUNT(*), SUM(amount_ttc) FROM {table_name}")
                    count, total = cursor.fetchone()

                stats[table_name] = {
                    'name': config['name'],
                    'icon': config['icon'],
                    'count': count or 0,
                    'total_amount': float(total) if total else 0,
                    'total_amount_formatted': f"{float(total):,.2f} €" if total else "0,00 €"
                }
            
            cursor.close()
            conn.close()
            
            return {
                'success': True,
                'statistics': stats
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors du calcul des statistiques: {str(e)}'
            }
