# DEPLOIEMENT DES AMELIORATIONS SECURISEES ERP HUB (PowerShell)
# Script pour deployer toutes les ameliorations de securite et performance

param(
    [switch]$SkipTests,
    [switch]$QuickStart
)

# Configuration
$ErrorActionPreference = "Stop"

# Fonctions utilitaires
function Write-Info {
    param($Message)
    Write-Host "INFO: $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "SUCCESS: $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "WARNING: $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "ERROR: $Message" -ForegroundColor Red
}

# Verifier les prerequis
function Test-Prerequisites {
    Write-Info "Verification des prerequis..."

    # Verifier Docker
    try {
        docker --version | Out-Null
        Write-Success "Docker trouve"
    }
    catch {
        Write-Error "Docker n'est pas installe ou accessible"
        exit 1
    }

    # Verifier Docker Compose
    try {
        docker-compose --version | Out-Null
        Write-Success "Docker Compose trouve"
    }
    catch {
        Write-Error "Docker Compose n'est pas installe ou accessible"
        exit 1
    }

    # Verifier Python
    try {
        python --version | Out-Null
        Write-Success "Python trouve"
    }
    catch {
        Write-Warning "Python non trouve, certaines fonctionnalites peuvent ne pas fonctionner"
    }

    Write-Success "Prerequis verifies"
}

# Créer les répertoires nécessaires
function New-RequiredDirectories {
    Write-Info "Création des répertoires..."
    
    $directories = @(
        "ssl\certs",
        "ssl\private", 
        "redis",
        "backend\tests",
        "logs",
        "monitoring\grafana\dashboards",
        "monitoring\grafana\datasources",
        "monitoring\prometheus",
        "backups"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Success "Répertoires créés"
}

# Générer les certificats SSL (version Windows)
function New-SSLCertificates {
    Write-Info "Génération des certificats SSL..."
    
    if (!(Test-Path "ssl\private\erp-hub.key")) {
        # Vérifier si OpenSSL est disponible
        try {
            openssl version | Out-Null
            
            # Configuration OpenSSL
            $opensslConfig = @"
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=FR
ST=France
L=Paris
O=ERP HUB
OU=IT Department
emailAddress=<EMAIL>
CN=localhost

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = erp-hub.local
DNS.4 = *.erp-hub.local
IP.1 = 127.0.0.1
IP.2 = ::1
"@
            
            # Écrire la configuration
            $opensslConfig | Out-File -FilePath "ssl\openssl.cnf" -Encoding ASCII
            
            # Générer la clé privée
            openssl genrsa -out "ssl\private\erp-hub.key" 2048
            
            # Générer le CSR
            openssl req -new -key "ssl\private\erp-hub.key" -out "ssl\certs\erp-hub.csr" -config "ssl\openssl.cnf"
            
            # Générer le certificat
            openssl x509 -req -in "ssl\certs\erp-hub.csr" -signkey "ssl\private\erp-hub.key" -out "ssl\certs\erp-hub.crt" -days 365 -extensions v3_req -extfile "ssl\openssl.cnf"
            
            Write-Success "Certificats SSL générés"
        }
        catch {
            Write-Warning "OpenSSL non disponible, certificats SSL non générés"
            Write-Warning "Installez OpenSSL ou utilisez des certificats existants"
        }
    }
    else {
        Write-Warning "Certificats SSL déjà existants"
    }
}

# Configurer les variables d'environnement
function Set-EnvironmentConfiguration {
    Write-Info "Configuration des variables d'environnement..."
    
    if (!(Test-Path "backend\.env")) {
        # Générer des clés sécurisées
        $jwtSecret = [System.Web.Security.Membership]::GeneratePassword(64, 0)
        $secretKey = [System.Web.Security.Membership]::GeneratePassword(64, 0)
        
        $envContent = @"
# Configuration ERP HUB Sécurisé
JWT_SECRET_KEY=$jwtSecret
SECRET_KEY=$secretKey
FLASK_ENV=production
FLASK_DEBUG=False
API_PORT=5000

# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_NAME=erp_hub
DB_USER=erp_admin
DB_PASSWORD=erp_secure_2024

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=erp_redis_secure_2024

# Email (optionnel)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=
EMAIL_PASSWORD=
ALERT_RECIPIENTS=

# Sécurité
RATE_LIMIT_ENABLED=True
SECURITY_HEADERS_ENABLED=True
CSRF_PROTECTION_ENABLED=True

# Monitoring
MONITORING_ENABLED=True
LOG_LEVEL=INFO
"@
        
        $envContent | Out-File -FilePath "backend\.env" -Encoding UTF8
        Write-Success "Fichier .env créé avec des clés sécurisées"
    }
    else {
        Write-Warning "Fichier .env déjà existant"
    }
}

# Démarrer les services Docker
function Start-DockerServices {
    Write-Info "Démarrage des services Docker..."
    
    try {
        # Arrêter les services existants
        docker-compose -f docker-compose-postgresql.yml down 2>$null
        
        # Démarrer les services
        docker-compose -f docker-compose-postgresql.yml up -d --build
        
        Write-Info "Attente du démarrage des services..."
        Start-Sleep -Seconds 30
        
        # Vérifier PostgreSQL
        $postgresReady = $false
        for ($i = 1; $i -le 30; $i++) {
            try {
                docker exec erp_postgres pg_isready -U erp_admin -d erp_hub 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $postgresReady = $true
                    break
                }
            }
            catch { }
            Start-Sleep -Seconds 2
        }
        
        if ($postgresReady) {
            Write-Success "PostgreSQL est prêt"
        }
        else {
            Write-Warning "PostgreSQL pourrait ne pas être complètement prêt"
        }
        
        # Vérifier Redis
        $redisReady = $false
        for ($i = 1; $i -le 30; $i++) {
            try {
                docker exec erp_redis redis-cli -a erp_redis_secure_2024 ping 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $redisReady = $true
                    break
                }
            }
            catch { }
            Start-Sleep -Seconds 2
        }
        
        if ($redisReady) {
            Write-Success "Redis est prêt"
        }
        else {
            Write-Warning "Redis pourrait ne pas être complètement prêt"
        }
        
        Write-Success "Services Docker démarrés"
    }
    catch {
        Write-Error "Erreur lors du démarrage des services Docker: $_"
        throw
    }
}

# Exécuter les tests de sécurité
function Invoke-SecurityTests {
    if ($SkipTests) {
        Write-Warning "Tests de sécurité ignorés"
        return
    }
    
    Write-Info "Exécution des tests de sécurité..."
    
    try {
        # Attendre que l'API soit prête
        Start-Sleep -Seconds 10
        
        # Vérifier si Python est disponible
        python --version | Out-Null
        
        # Exécuter les tests
        $testResult = python "backend\tests\test_security.py"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Tests de sécurité réussis"
        }
        else {
            Write-Warning "Certains tests de sécurité ont échoué"
        }
    }
    catch {
        Write-Warning "Impossible d'exécuter les tests de sécurité: $_"
    }
}

# Démarrer le monitoring
function Start-Monitoring {
    Write-Info "Configuration du monitoring..."
    
    try {
        # Vérifier si Python est disponible
        python --version | Out-Null
        
        # Créer le fichier de démarrage du monitoring
        $monitoringScript = @"
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from monitoring import monitoring
monitoring.start_monitoring()

try:
    import time
    while True:
        time.sleep(60)
        health = monitoring.get_health_status()
        print(f"Status: {health['status']} - Alertes: {health['alerts']['total']}")
except KeyboardInterrupt:
    monitoring.stop_monitoring()
"@
        
        $monitoringScript | Out-File -FilePath "backend\start_monitoring.py" -Encoding UTF8
        
        # Démarrer le monitoring en arrière-plan
        Start-Process -FilePath "python" -ArgumentList "backend\start_monitoring.py" -WindowStyle Hidden
        
        Write-Success "Monitoring configuré"
    }
    catch {
        Write-Warning "Impossible de démarrer le monitoring: $_"
    }
}

# Afficher le résumé
function Show-Summary {
    Write-Host ""
    Write-Host "🎉 DÉPLOIEMENT DES AMÉLIORATIONS TERMINÉ !" -ForegroundColor Green
    Write-Host "==========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔒 Améliorations de sécurité déployées :" -ForegroundColor Cyan
    Write-Host "   ✅ Authentification avec httpOnly cookies"
    Write-Host "   ✅ Protection CSRF"
    Write-Host "   ✅ Rate limiting avancé"
    Write-Host "   ✅ Headers de sécurité HTTP"
    Write-Host "   ✅ Validation et sanitisation des entrées"
    Write-Host ""
    Write-Host "⚡ Améliorations de performance :" -ForegroundColor Cyan
    Write-Host "   ✅ Cache Redis intégré"
    Write-Host "   ✅ Optimisations de base de données"
    Write-Host "   ✅ Compression et cache"
    Write-Host ""
    Write-Host "📊 Monitoring et tests :" -ForegroundColor Cyan
    Write-Host "   ✅ Système de monitoring en temps réel"
    Write-Host "   ✅ Tests de sécurité automatisés"
    Write-Host "   ✅ Logs de sécurité détaillés"
    Write-Host ""
    Write-Host "🌐 Accès aux services :" -ForegroundColor Yellow
    Write-Host "   🔒 Application principale : http://localhost:3000"
    Write-Host "   📊 Monitoring Grafana : http://localhost:3000"
    Write-Host "   📈 Métriques Prometheus : http://localhost:9090"
    Write-Host "   🗄️  Base de données : localhost:5432"
    Write-Host "   🚀 Cache Redis : localhost:6379"
    Write-Host ""
    Write-Host "🔑 Identifiants par défaut :" -ForegroundColor Yellow
    Write-Host "   Application : admin / Admin123!"
    Write-Host "   Grafana : admin / admin_grafana_2024"
    Write-Host ""
    Write-Host "📋 Prochaines étapes recommandées :" -ForegroundColor Magenta
    Write-Host "   1. Tester l'application"
    Write-Host "   2. Changer les mots de passe par défaut"
    Write-Host "   3. Configurer les alertes email"
    Write-Host "   4. Vérifier les logs de sécurité"
    Write-Host ""
}

# Fonction principale
function Main {
    Write-Host "🚀 DÉPLOIEMENT DES AMÉLIORATIONS SÉCURISÉES ERP HUB" -ForegroundColor Green
    Write-Host "==================================================" -ForegroundColor Green
    Write-Host ""
    
    try {
        Test-Prerequisites
        New-RequiredDirectories
        
        if (!$QuickStart) {
            New-SSLCertificates
        }
        
        Set-EnvironmentConfiguration
        Start-DockerServices
        
        if (!$QuickStart) {
            Invoke-SecurityTests
            Start-Monitoring
        }
        
        Show-Summary
        
        Write-Success "Déploiement terminé avec succès !"
    }
    catch {
        Write-Error "Erreur lors du déploiement: $_"
        Write-Host "Vérifiez les logs et réessayez." -ForegroundColor Red
        exit 1
    }
}

# Exécution du script principal
Main
