# 🚀 GESTIONNAIRE DE CACHE REDIS POUR ERP HUB
# Cache intelligent pour optimiser les performances

import redis
import json
import hashlib
import pickle
from datetime import datetime, timedelta
from functools import wraps
import logging

class ERPRedisCache:
    """Gestionnaire de cache Redis pour ERP HUB"""
    
    def __init__(self, host='localhost', port=6379, db=0):
        try:
            self.redis_client = redis.Redis(
                host=host, 
                port=port, 
                db=db, 
                decode_responses=False,  # Pour supporter pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # Tester la connexion
            self.redis_client.ping()
            self.connected = True
            print("✅ Redis Cache connecté")
            
        except Exception as e:
            print(f"⚠️ Redis non disponible: {e}")
            self.redis_client = None
            self.connected = False
    
    def _generate_key(self, prefix, *args, **kwargs):
        """Générer une clé de cache unique"""
        # Créer une chaîne unique à partir des arguments
        key_data = f"{prefix}:{':'.join(map(str, args))}"
        if kwargs:
            key_data += f":{':'.join(f'{k}={v}' for k, v in sorted(kwargs.items()))}"
        
        # Hasher pour éviter les clés trop longues
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"erp_cache:{prefix}:{key_hash}"
    
    def get(self, key):
        """Récupérer une valeur du cache"""
        if not self.connected:
            return None
            
        try:
            data = self.redis_client.get(key)
            if data:
                return pickle.loads(data)
            return None
        except Exception as e:
            logging.error(f"Erreur lecture cache Redis: {e}")
            return None
    
    def set(self, key, value, expire_seconds=3600):
        """Stocker une valeur dans le cache"""
        if not self.connected:
            return False
            
        try:
            serialized_data = pickle.dumps(value)
            return self.redis_client.setex(key, expire_seconds, serialized_data)
        except Exception as e:
            logging.error(f"Erreur écriture cache Redis: {e}")
            return False
    
    def delete(self, key):
        """Supprimer une clé du cache"""
        if not self.connected:
            return False
            
        try:
            return self.redis_client.delete(key)
        except Exception as e:
            logging.error(f"Erreur suppression cache Redis: {e}")
            return False
    
    def clear_pattern(self, pattern):
        """Supprimer toutes les clés correspondant à un pattern"""
        if not self.connected:
            return False
            
        try:
            keys = self.redis_client.keys(f"erp_cache:{pattern}*")
            if keys:
                return self.redis_client.delete(*keys)
            return True
        except Exception as e:
            logging.error(f"Erreur suppression pattern cache Redis: {e}")
            return False
    
    def cache_result(self, prefix, expire_seconds=3600):
        """Décorateur pour mettre en cache le résultat d'une fonction"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Générer la clé de cache
                cache_key = self._generate_key(prefix, func.__name__, *args, **kwargs)
                
                # Essayer de récupérer depuis le cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    logging.info(f"Cache HIT: {cache_key}")
                    return cached_result
                
                # Exécuter la fonction et mettre en cache
                result = func(*args, **kwargs)
                if result is not None:
                    self.set(cache_key, result, expire_seconds)
                    logging.info(f"Cache SET: {cache_key}")
                
                return result
            return wrapper
        return decorator
    
    # ===== MÉTHODES SPÉCIFIQUES ERP =====
    
    def cache_dashboard_data(self, user_id, data, expire_minutes=15):
        """Cache des données de dashboard"""
        key = self._generate_key("dashboard", user_id)
        return self.set(key, data, expire_minutes * 60)
    
    def get_dashboard_data(self, user_id):
        """Récupérer les données de dashboard en cache"""
        key = self._generate_key("dashboard", user_id)
        return self.get(key)
    
    def cache_user_permissions(self, user_id, permissions, expire_hours=24):
        """Cache des permissions utilisateur"""
        key = self._generate_key("permissions", user_id)
        return self.set(key, permissions, expire_hours * 3600)
    
    def get_user_permissions(self, user_id):
        """Récupérer les permissions utilisateur en cache"""
        key = self._generate_key("permissions", user_id)
        return self.get(key)
    
    def cache_query_result(self, query_hash, result, expire_minutes=30):
        """Cache du résultat d'une requête SQL"""
        key = self._generate_key("query", query_hash)
        return self.set(key, result, expire_minutes * 60)
    
    def get_query_result(self, query_hash):
        """Récupérer le résultat d'une requête en cache"""
        key = self._generate_key("query", query_hash)
        return self.get(key)
    
    def invalidate_user_cache(self, user_id):
        """Invalider tout le cache d'un utilisateur"""
        patterns = [
            f"dashboard:{user_id}",
            f"permissions:{user_id}",
            f"user_data:{user_id}"
        ]
        
        for pattern in patterns:
            self.clear_pattern(pattern)
    
    def invalidate_module_cache(self, module_name):
        """Invalider le cache d'un module spécifique"""
        self.clear_pattern(f"{module_name}:*")
    
    # ===== STATISTIQUES ET MONITORING =====
    
    def get_cache_stats(self):
        """Obtenir les statistiques du cache"""
        if not self.connected:
            return {"connected": False}
            
        try:
            info = self.redis_client.info()
            
            # Compter les clés ERP
            erp_keys = len(self.redis_client.keys("erp_cache:*"))
            
            return {
                "connected": True,
                "total_keys": info.get('db0', {}).get('keys', 0),
                "erp_keys": erp_keys,
                "memory_used": info.get('used_memory_human', 'N/A'),
                "hits": info.get('keyspace_hits', 0),
                "misses": info.get('keyspace_misses', 0),
                "hit_rate": self._calculate_hit_rate(info.get('keyspace_hits', 0), info.get('keyspace_misses', 0))
            }
        except Exception as e:
            logging.error(f"Erreur stats cache Redis: {e}")
            return {"connected": False, "error": str(e)}
    
    def _calculate_hit_rate(self, hits, misses):
        """Calculer le taux de succès du cache"""
        total = hits + misses
        if total == 0:
            return 0
        return round((hits / total) * 100, 2)
    
    # ===== NETTOYAGE ET MAINTENANCE =====
    
    def cleanup_expired_keys(self):
        """Nettoyer les clés expirées (Redis le fait automatiquement, mais utile pour monitoring)"""
        if not self.connected:
            return False
            
        try:
            # Redis nettoie automatiquement, mais on peut forcer
            keys_before = len(self.redis_client.keys("erp_cache:*"))
            
            # Forcer la suppression des clés expirées
            self.redis_client.execute_command("EXPIRE", "erp_cache:*", 0)
            
            keys_after = len(self.redis_client.keys("erp_cache:*"))
            
            logging.info(f"Nettoyage cache: {keys_before - keys_after} clés supprimées")
            return True
            
        except Exception as e:
            logging.error(f"Erreur nettoyage cache: {e}")
            return False
    
    def flush_all_cache(self):
        """Vider tout le cache ERP (attention !)"""
        if not self.connected:
            return False
            
        try:
            keys = self.redis_client.keys("erp_cache:*")
            if keys:
                deleted = self.redis_client.delete(*keys)
                logging.warning(f"Cache ERP vidé: {deleted} clés supprimées")
                return True
            return True
        except Exception as e:
            logging.error(f"Erreur vidage cache: {e}")
            return False

# ===== INSTANCE GLOBALE =====

# Créer une instance globale du cache
cache = ERPRedisCache()

# ===== DÉCORATEURS UTILITAIRES =====

def cached(expire_seconds=3600, prefix="general"):
    """Décorateur simple pour mettre en cache une fonction"""
    return cache.cache_result(prefix, expire_seconds)

def cache_dashboard(expire_minutes=15):
    """Décorateur spécifique pour les données de dashboard"""
    return cache.cache_result("dashboard", expire_minutes * 60)

def cache_query(expire_minutes=30):
    """Décorateur spécifique pour les requêtes SQL"""
    return cache.cache_result("query", expire_minutes * 60)

def invalidate_cache_on_change(module_name):
    """Décorateur pour invalider le cache lors de modifications"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            # Invalider le cache du module après modification
            cache.invalidate_module_cache(module_name)
            return result
        return wrapper
    return decorator

# ===== FONCTIONS UTILITAIRES =====

def warm_up_cache():
    """Préchauffer le cache avec les données fréquemment utilisées"""
    if not cache.connected:
        return False
        
    try:
        # Ici vous pouvez ajouter la logique pour préchauffer le cache
        # avec les données les plus utilisées
        logging.info("Cache préchauffé")
        return True
    except Exception as e:
        logging.error(f"Erreur préchauffage cache: {e}")
        return False

def get_cache_health():
    """Vérifier la santé du cache"""
    stats = cache.get_cache_stats()
    
    if not stats.get("connected"):
        return {"status": "unhealthy", "reason": "Redis non connecté"}
    
    hit_rate = stats.get("hit_rate", 0)
    
    if hit_rate > 80:
        status = "excellent"
    elif hit_rate > 60:
        status = "good"
    elif hit_rate > 40:
        status = "fair"
    else:
        status = "poor"
    
    return {
        "status": "healthy",
        "performance": status,
        "hit_rate": hit_rate,
        "stats": stats
    }

# ===== LOGGING =====

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if cache.connected:
    logger.info("🚀 Cache Redis ERP HUB initialisé")
    logger.info("📊 Fonctionnalités disponibles:")
    logger.info("   - Cache des résultats de requêtes")
    logger.info("   - Cache des données de dashboard")
    logger.info("   - Cache des permissions utilisateur")
    logger.info("   - Invalidation intelligente")
    logger.info("   - Statistiques et monitoring")
else:
    logger.warning("⚠️ Cache Redis non disponible - Mode dégradé")

# Export des fonctions principales
__all__ = [
    'cache', 'cached', 'cache_dashboard', 'cache_query', 
    'invalidate_cache_on_change', 'warm_up_cache', 'get_cache_health'
]
