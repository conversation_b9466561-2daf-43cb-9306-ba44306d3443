#!/usr/bin/env python
"""
Script de test pour l'Agent BI
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_bi_models():
    """Test des modèles BI"""
    try:
        from agents.bi.models import (
            DataSource, Dataset, Report, Dashboard, DashboardReport, KPI,
            Alert, AnalysisJob, BIAnalytics, UserActivity
        )
        print("✅ Import des modèles BI réussi")
        
        # Test des choix
        print(f"✅ Types de sources de données: {len(DataSource.SOURCE_TYPES)} options")
        print(f"✅ Types de datasets: {len(Dataset.DATASET_TYPES)} options")
        print(f"✅ Types de rapports: {len(Report.REPORT_TYPES)} options")
        print(f"✅ Types de graphiques: {len(Report.CHART_TYPES)} options")
        print(f"✅ Types de tableaux de bord: {len(Dashboard.DASHBOARD_TYPES)} options")
        print(f"✅ Types de KPIs: {len(KPI.KPI_TYPES)} options")
        print(f"✅ Méthodes de calcul: {len(KPI.CALCULATION_METHODS)} options")
        print(f"✅ Types d'alertes: {len(Alert.ALERT_TYPES)} options")
        print(f"✅ Types de tâches: {len(AnalysisJob.JOB_TYPES)} options")
        print(f"✅ Types d'activités: {len(UserActivity.ACTIVITY_TYPES)} options")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de l'import des modèles: {e}")
        return False

def test_bi_services():
    """Test des services BI"""
    try:
        from agents.bi.services import BIService
        from core.models import Tenant
        
        # Créer un tenant de test
        tenant, created = Tenant.objects.get_or_create(
            name="Test Tenant BI",
            defaults={
                'slug': 'test-tenant-bi',
                'description': 'Tenant de test pour BI'
            }
        )
        
        # Initialiser le service BI
        bi_service = BIService(tenant)
        print("✅ Initialisation du service BI réussie")
        
        # Test de génération du dashboard
        dashboard_data = bi_service.get_bi_dashboard()
        print("✅ Génération du dashboard BI réussie")
        print(f"   - Sources de données: {dashboard_data['data_sources']['total']}")
        print(f"   - Datasets: {dashboard_data['datasets']['total']}")
        print(f"   - Rapports: {dashboard_data['reports']['total']}")
        print(f"   - Tableaux de bord: {dashboard_data['dashboards']['total']}")
        print(f"   - KPIs: {dashboard_data['kpis']['total']}")
        print(f"   - Alertes: {dashboard_data['alerts']['total']}")
        print(f"   - Santé système: {dashboard_data['system_health']['score']}%")
        
        # Test de génération d'insights
        insights = bi_service.generate_bi_insights()
        print(f"✅ Génération d'insights BI réussie: {len(insights)} insights")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des services: {e}")
        return False

def test_bi_serializers():
    """Test des serializers BI"""
    try:
        from agents.bi.serializers import (
            DataSourceSerializer, DatasetSerializer, ReportSerializer,
            DashboardSerializer, KPISerializer, AlertSerializer,
            AnalysisJobSerializer, BIAnalyticsSerializer
        )
        print("✅ Import des serializers BI réussi")
        
        # Test de validation des serializers
        kpi_data = {
            'dataset_id': '12345678-1234-5678-9012-123456789012',
            'name': 'Test KPI',
            'kpi_type': 'financial',
            'calculation_method': 'sum'
        }
        
        from agents.bi.serializers import KPICreateSerializer
        serializer = KPICreateSerializer(data=kpi_data)
        if serializer.is_valid():
            print("✅ Validation du serializer de KPI réussie")
        else:
            print(f"❌ Erreurs de validation: {serializer.errors}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des serializers: {e}")
        return False

def test_bi_views():
    """Test des vues BI"""
    try:
        from agents.bi.views import (
            bi_status, bi_dashboard, bi_insights,
            KPIListCreateView, AlertListCreateView, AnalysisJobListCreateView
        )
        print("✅ Import des vues BI réussi")
        
        # Vérifier que les vues sont bien configurées
        print("✅ Vues de statut et dashboard configurées")
        print("✅ Vues de gestion des KPIs configurées")
        print("✅ Vues de gestion des alertes configurées")
        print("✅ Vues de gestion des tâches d'analyse configurées")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des vues: {e}")
        return False

def test_bi_urls():
    """Test des URLs BI"""
    try:
        from agents.bi.urls import urlpatterns
        print("✅ Import des URLs BI réussi")
        print(f"✅ {len(urlpatterns)} routes configurées")
        
        # Vérifier les routes principales
        route_names = [pattern.name for pattern in urlpatterns if hasattr(pattern, 'name')]
        expected_routes = ['status', 'dashboard', 'kpi-list', 'alert-list', 'insights']
        
        for route in expected_routes:
            if route in route_names:
                print(f"✅ Route '{route}' configurée")
            else:
                print(f"❌ Route '{route}' manquante")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des URLs: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test de l'Agent BI")
    print("=" * 50)
    
    tests = [
        ("Modèles BI", test_bi_models),
        ("Services BI", test_bi_services),
        ("Serializers BI", test_bi_serializers),
        ("Vues BI", test_bi_views),
        ("URLs BI", test_bi_urls),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Résultats des tests:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Tous les tests sont passés ! L'Agent BI est prêt.")
        print("\n📈 Fonctionnalités disponibles:")
        print("   • Dashboard BI complet avec métriques temps réel")
        print("   • Gestion des KPIs avec calculs automatiques")
        print("   • Système d'alertes intelligent")
        print("   • Tâches d'analyse et jobs automatisés")
        print("   • Insights IA et recommandations")
        print("   • Sources de données et datasets")
        print("   • Rapports et tableaux de bord")
        print("   • Analytics et métriques de performance")
    else:
        print("\n⚠️  Certains tests ont échoué. Vérifiez la configuration.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
