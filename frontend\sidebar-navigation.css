/* 
 * Navigation verticale commune pour ERP HUB
 * À inclure dans tous les modules
 */

/* Layout principal avec sidebar */
body {
    margin: 0;
    display: flex;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation verticale */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.sidebar-logo h1 {
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0;
}

.sidebar-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 4px solid transparent;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #3b82f6;
    color: white;
}

.nav-item.active {
    background: rgba(59, 130, 246, 0.2);
    border-left-color: #3b82f6;
}

.nav-item-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-icon {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    font-weight: 500;
    font-size: 0.95rem;
}

.nav-description {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
    line-height: 1.2;
}

/* Contenu principal */
.main-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    background: #f8fafc;
}

/* Header pour les pages de modules */
.module-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.module-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.module-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.module-title h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.module-icon {
    font-size: 2rem;
    color: #3b82f6;
}

/* Menu mobile */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: #1e293b;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar-overlay.open {
        display: block;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .module-header {
        padding: 1rem;
        margin-top: 60px; /* Espace pour le bouton menu mobile */
    }
    
    .module-header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .module-title h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
    }
    
    .nav-item {
        padding: 0.75rem 1rem;
    }
    
    .nav-text {
        font-size: 0.9rem;
    }
    
    .nav-description {
        font-size: 0.7rem;
    }
}

/* Scrollbar personnalisée pour la sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Animation d'entrée */
.sidebar {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* États de focus pour l'accessibilité */
.nav-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

.mobile-menu-toggle:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
