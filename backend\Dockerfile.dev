FROM python:3.11-slim

# Variables d'environnement pour le développement
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Répertoire de travail
WORKDIR /app

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    postgresql-client \
    curl \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Copie et installation des dépendances Python
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Installation des outils de développement
RUN pip install --no-cache-dir \
    django-debug-toolbar \
    django-extensions \
    ipython \
    pytest \
    pytest-django \
    black \
    flake8

# Copie du code source
COPY . /app/

# Création des dossiers nécessaires
RUN mkdir -p /app/static /app/media /app/logs

# Permissions
RUN chmod +x /app/manage.py

# Port d'exposition
EXPOSE 8000

# Commande par défaut pour le développement
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
