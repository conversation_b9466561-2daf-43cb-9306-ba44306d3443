<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Purchase - Achats | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #f59e0b 30%, #d97706 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #f59e0b;
            color: white;
        }
        
        .btn-primary:hover {
            background: #d97706;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #f59e0b;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }

        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #f59e0b;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #f59e0b;
            background: #fffbeb;
        }

        .nav-tab.active {
            color: #f59e0b;
            border-bottom-color: #f59e0b;
            background: #fffbeb;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>
    <header class="header">
        <div class="logo">🛒 Agent Purchase - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion des Achats</h1>
            <p class="page-subtitle">Fournisseurs et bons de commande - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalSuppliers">0</div>
                <div class="stat-label">Total Fournisseurs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalPurchaseOrders">0</div>
                <div class="stat-label">Bons de Commande</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalPurchaseAmount">0€</div>
                <div class="stat-label">Montant Total Achats</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pendingOrders">0</div>
                <div class="stat-label">Commandes en Attente</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('suppliers')">
                <span class="material-icons">business</span>
                Fournisseurs
            </button>
            <button class="nav-tab" onclick="showTab('purchase-orders')">
                <span class="material-icons">receipt_long</span>
                Bons de Commande
            </button>
        </nav>

        <!-- Onglet Fournisseurs -->
        <div id="suppliers" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🏢 Fournisseurs</h2>
                    <button class="btn btn-primary" onclick="loadSuppliers()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Fournisseur</th>
                                    <th>Entreprise</th>
                                    <th>Contact</th>
                                    <th>Email</th>
                                    <th>Ville</th>
                                    <th>Secteur</th>
                                    <th>Délai Paiement</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des fournisseurs...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Bons de Commande -->
        <div id="purchase-orders" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📋 Bons de Commande</h2>
                    <button class="btn btn-primary" onclick="loadPurchaseOrders()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>N° Bon de Commande</th>
                                    <th>Fournisseur</th>
                                    <th>Date Commande</th>
                                    <th>Date Livraison Prévue</th>
                                    <th>Montant Total</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="purchaseOrdersTableBody">
                                <tr>
                                    <td colspan="6" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des bons de commande...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let suppliers = [];
        let purchaseOrders = [];
        let currentTab = 'suppliers';

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadSuppliers();
            await loadPurchaseOrders();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les fournisseurs depuis PostgreSQL
        async function loadSuppliers() {
            try {
                showAlert('Chargement des fournisseurs depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/suppliers`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        suppliers = data.data || [];
                        renderSuppliersTable();
                        showAlert(`${suppliers.length} fournisseurs chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement fournisseurs:', error);
                showAlert('Erreur lors du chargement des fournisseurs: ' + error.message, 'error');
                suppliers = [];
                renderSuppliersTable();
            }
        }

        // Charger les bons de commande depuis PostgreSQL
        async function loadPurchaseOrders() {
            try {
                const response = await fetch(`${API_BASE_URL}/purchase-orders`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        purchaseOrders = data.data || [];
                        renderPurchaseOrdersTable();
                        console.log(`${purchaseOrders.length} bons de commande chargés depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement bons de commande:', error);
                showAlert('Erreur lors du chargement des bons de commande: ' + error.message, 'error');
                purchaseOrders = [];
                renderPurchaseOrdersTable();
            }
        }

        // Afficher le tableau des fournisseurs
        function renderSuppliersTable() {
            const tbody = document.getElementById('suppliersTableBody');
            
            if (suppliers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun fournisseur trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = suppliers.map(supplier => `
                <tr>
                    <td><strong>${supplier.supplierCode}</strong></td>
                    <td>${supplier.companyName}</td>
                    <td>${supplier.contactPerson || 'N/A'}</td>
                    <td>${supplier.email || 'N/A'}</td>
                    <td>${supplier.city || 'N/A'}</td>
                    <td>${supplier.industry || 'N/A'}</td>
                    <td>${supplier.paymentTerms} jours</td>
                    <td>${getStatusBadge(supplier.status)}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des bons de commande
        function renderPurchaseOrdersTable() {
            const tbody = document.getElementById('purchaseOrdersTableBody');
            
            if (purchaseOrders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun bon de commande trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = purchaseOrders.map(po => `
                <tr>
                    <td><strong>${po.poNumber}</strong></td>
                    <td>${po.supplierName}</td>
                    <td>${formatDate(po.orderDate)}</td>
                    <td>${formatDate(po.expectedDeliveryDate)}</td>
                    <td>${po.totalAmount ? po.totalAmount.toLocaleString() + '€' : 'N/A'}</td>
                    <td>${getPurchaseOrderStatusBadge(po.status)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalSuppliers = suppliers.length;
            const totalPurchaseOrders = purchaseOrders.length;
            const totalPurchaseAmount = purchaseOrders.reduce((sum, po) => sum + (po.totalAmount || 0), 0);
            const pendingOrders = purchaseOrders.filter(po => po.status === 'pending').length;

            document.getElementById('totalSuppliers').textContent = totalSuppliers;
            document.getElementById('totalPurchaseOrders').textContent = totalPurchaseOrders;
            document.getElementById('totalPurchaseAmount').textContent = totalPurchaseAmount.toLocaleString() + '€';
            document.getElementById('pendingOrders').textContent = pendingOrders;
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');
            
            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'blocked': '<span class="badge badge-danger">Bloqué</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getPurchaseOrderStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge badge-warning">En attente</span>',
                'sent': '<span class="badge badge-info">Envoyé</span>',
                'acknowledged': '<span class="badge badge-info">Accusé réception</span>',
                'partial': '<span class="badge badge-warning">Partiel</span>',
                'completed': '<span class="badge badge-success">Terminé</span>',
                'cancelled': '<span class="badge badge-danger">Annulé</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
