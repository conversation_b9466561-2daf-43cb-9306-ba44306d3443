# 💾 SCRIPT DE SAUVEGARDE AUTOMATIQUE ERP HUB
# Crée une sauvegarde complète de la base de données PostgreSQL

Write-Host "🚀 Démarrage de la sauvegarde ERP HUB..." -ForegroundColor Green

# Créer le dossier de sauvegarde s'il n'existe pas
if (!(Test-Path "backups")) {
    New-Item -ItemType Directory -Path "backups" | Out-Null
    Write-Host "📁 Dossier 'backups' créé" -ForegroundColor Yellow
}

# Générer un nom de fichier avec la date et l'heure
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupFile = "backups\erp_hub_backup_$timestamp.sql"

Write-Host "📊 Sauvegarde en cours vers : $backupFile" -ForegroundColor Cyan

try {
    # Exécuter la sauvegarde avec pg_dump via Docker
    $result = docker exec erp_postgres pg_dump -U erp_admin -d erp_hub
    
    if ($LASTEXITCODE -eq 0) {
        # Sauvegarder le résultat dans le fichier
        $result | Out-File -FilePath $backupFile -Encoding UTF8
        
        Write-Host "✅ Sauvegarde réussie !" -ForegroundColor Green
        Write-Host "📁 Fichier créé : $backupFile" -ForegroundColor White
        
        # Afficher la taille du fichier
        $fileInfo = Get-Item $backupFile
        $sizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
        Write-Host "📏 Taille : $sizeKB KB" -ForegroundColor White
        
        # Compter le nombre de lignes
        $lineCount = (Get-Content $backupFile | Measure-Object -Line).Lines
        Write-Host "📄 Lignes : $lineCount" -ForegroundColor White
        
        Write-Host ""
        Write-Host "🎯 Votre base de données ERP HUB a été sauvegardée avec succès !" -ForegroundColor Green
        Write-Host "💡 Pour restaurer cette sauvegarde plus tard, utilisez :" -ForegroundColor Yellow
        Write-Host "   Get-Content $backupFile | docker exec -i erp_postgres psql -U erp_admin -d erp_hub" -ForegroundColor Gray
        
    } else {
        throw "Erreur lors de l'exécution de pg_dump"
    }
    
} catch {
    Write-Host "❌ Erreur lors de la sauvegarde : $_" -ForegroundColor Red
    Write-Host "🔍 Vérifiez que PostgreSQL fonctionne avec : docker ps" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "📋 Statistiques actuelles de la base :" -ForegroundColor Cyan

try {
    # Récupérer les statistiques
    $stats = docker exec erp_postgres psql -U erp_admin -d erp_hub -t -c "
        SELECT 'Budgets: ' || COUNT(*) FROM budgets 
        UNION ALL SELECT 'Employés: ' || COUNT(*) FROM employees 
        UNION ALL SELECT 'Contacts: ' || COUNT(*) FROM contacts 
        UNION ALL SELECT 'Comptes: ' || COUNT(*) FROM accounts;
    "
    
    $stats | ForEach-Object { 
        if ($_.Trim()) { 
            Write-Host "  $($_.Trim())" -ForegroundColor White 
        } 
    }
    
} catch {
    Write-Host "⚠️ Impossible de récupérer les statistiques" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🏁 Sauvegarde terminée avec succès !" -ForegroundColor Green
Write-Host "📂 Tous vos fichiers de sauvegarde sont dans le dossier backups" -ForegroundColor Cyan

# Lister les sauvegardes existantes
Write-Host ""
Write-Host "📚 Sauvegardes disponibles :" -ForegroundColor Cyan
Get-ChildItem "backups\*.sql" | Sort-Object LastWriteTime -Descending | ForEach-Object {
    $sizeKB = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  $($_.Name) - $sizeKB KB - $($_.LastWriteTime)" -ForegroundColor Gray
}
