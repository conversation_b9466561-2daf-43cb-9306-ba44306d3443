#!/usr/bin/env python
"""
Test simple du système ERP
"""
import sys
import os

def test_python_version():
    """Test de la version Python"""
    print(f"🐍 Python version: {sys.version}")
    if sys.version_info >= (3, 8):
        print("✅ Version Python compatible")
        return True
    else:
        print("❌ Version Python trop ancienne (3.8+ requis)")
        return False

def test_django_import():
    """Test d'import Django"""
    try:
        import django
        print(f"✅ Django version: {django.get_version()}")
        return True
    except ImportError:
        print("❌ Django non installé")
        return False

def test_project_structure():
    """Test de la structure du projet"""
    required_dirs = [
        'config',
        'core',
        'agents',
        'agents/manager',
        'agents/hr',
        'agents/sales',
        'agents/purchase',
        'agents/logistics',
        'agents/stock',
        'agents/accounting',
        'agents/finance',
        'agents/crm',
        'agents/bi'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} manquant")
            all_exist = False
    
    return all_exist

def test_settings():
    """Test des settings Django"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
        import django
        django.setup()
        
        from django.conf import settings
        print(f"✅ Settings chargés: {settings.DEBUG}")
        
        # Test des apps installées
        installed_apps = settings.INSTALLED_APPS
        agent_apps = [app for app in installed_apps if 'agents.' in app]
        print(f"✅ {len(agent_apps)} agents configurés")
        
        return True
    except Exception as e:
        print(f"❌ Erreur settings: {e}")
        return False

def test_models_import():
    """Test d'import des modèles"""
    try:
        # Test des modèles core
        from core.models import Tenant, User
        print("✅ Modèles core importés")
        
        # Test des modèles agents
        from agents.models import Agent
        print("✅ Modèles agents importés")
        
        # Test de quelques modèles spécifiques
        from agents.bi.models import KPI, Alert, Dashboard
        print("✅ Modèles BI importés")
        
        from agents.crm.models import Contact, Opportunity
        print("✅ Modèles CRM importés")
        
        return True
    except Exception as e:
        print(f"❌ Erreur import modèles: {e}")
        return False

def main():
    """Test principal"""
    print("🚀 Test du Système ERP Modulaire")
    print("=" * 50)
    
    tests = [
        ("Version Python", test_python_version),
        ("Import Django", test_django_import),
        ("Structure projet", test_project_structure),
        ("Configuration Django", test_settings),
        ("Import modèles", test_models_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Résultats des tests:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Tous les tests de base sont passés !")
        print("\n📈 Système ERP Modulaire:")
        print("   • 10 agents développés")
        print("   • Architecture modulaire")
        print("   • Intelligence artificielle intégrée")
        print("   • Interface moderne React")
        print("   • API REST complète")
    else:
        print("\n⚠️  Certains tests ont échoué.")
        print("   Vérifiez l'installation des dépendances.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
