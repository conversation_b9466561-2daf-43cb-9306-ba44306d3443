@echo off
REM 💾 SCRIPT DE SAUVEGARDE AUTOMATIQUE ERP HUB
REM Crée une sauvegarde complète de la base de données PostgreSQL

echo 🚀 Démarrage de la sauvegarde ERP HUB...

REM Créer le dossier de sauvegarde s'il n'existe pas
if not exist "backups" mkdir backups

REM Générer un nom de fichier avec la date et l'heure
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

set "backup_file=backups\erp_hub_backup_%datestamp%.sql"

echo 📊 Sauvegarde en cours vers : %backup_file%

REM Exécuter la sauvegarde avec pg_dump via Docker
docker exec erp_postgres pg_dump -U erp_admin -d erp_hub > %backup_file%

if %ERRORLEVEL% EQU 0 (
    echo ✅ Sauvegarde réussie !
    echo 📁 Fichier créé : %backup_file%
    
    REM Afficher la taille du fichier
    for %%A in (%backup_file%) do echo 📏 Taille : %%~zA octets
    
    REM Compter le nombre de lignes
    for /f %%C in ('find /c /v "" ^< %backup_file%') do echo 📄 Lignes : %%C
    
    echo.
    echo 🎯 Votre base de données ERP HUB a été sauvegardée avec succès !
    echo 💡 Pour restaurer cette sauvegarde plus tard, utilisez :
    echo    docker exec -i erp_postgres psql -U erp_admin -d erp_hub ^< %backup_file%
    
) else (
    echo ❌ Erreur lors de la sauvegarde !
    echo 🔍 Vérifiez que PostgreSQL fonctionne avec : docker ps
)

echo.
echo 📋 Statistiques actuelles de la base :
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "SELECT 'Budgets: ' || COUNT(*) FROM budgets UNION ALL SELECT 'Employés: ' || COUNT(*) FROM employees UNION ALL SELECT 'Contacts: ' || COUNT(*) FROM contacts UNION ALL SELECT 'Comptes: ' || COUNT(*) FROM accounts;"

echo.
echo 🏁 Sauvegarde terminée. Appuyez sur une touche pour continuer...
pause > nul
