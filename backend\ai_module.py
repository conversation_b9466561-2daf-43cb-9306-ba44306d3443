"""
🤖 MODULE IA POUR ERP HUB
Intelligence Artificielle intégrée pour prédictions, insights et assistant conversationnel
"""

import os
import json
try:
    import pandas as pd
    import numpy as np
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # Fallback simple sans sklearn
    class LinearRegression:
        def fit(self, X, y): pass
        def predict(self, X): return [0]

    class StandardScaler:
        def fit_transform(self, X): return X

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

class ERPAIModule:
    """Module IA Expert en Contrôle de Gestion pour l'ERP HUB"""

    def __init__(self, openai_api_key: Optional[str] = None):
        """Initialiser le module IA Expert en Contrôle de Gestion"""
        self.openai_api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if self.openai_api_key:
            openai.api_key = self.openai_api_key

        # Modèles ML
        self.budget_predictor = LinearRegression()
        self.scaler = StandardScaler()
        self.is_trained = False

        # Cache pour les prédictions
        self.prediction_cache = {}

        # Configuration Expert Contrôle de Gestion
        self.expertise_config = {
            'role': 'Contrôleur de Gestion Senior',
            'experience': '15+ années en contrôle de gestion',
            'specializations': [
                'Analyse des écarts budgétaires',
                'Pilotage de la performance',
                'Optimisation des coûts',
                'Reporting financier',
                'Prévisions et budgets'
            ],
            'threshold_significant_variance': 0.10,  # 10% d'écart significatif
            'threshold_critical_variance': 0.25     # 25% d'écart critique
        }
        
    def predict_budget_trends(self, budget_data: List[Dict]) -> Dict[str, Any]:
        """Prédire les tendances budgétaires"""
        try:
            if not budget_data:
                return {
                    'success': False,
                    'error': 'Aucune donnée budgétaire fournie'
                }
            
            # Convertir en DataFrame
            df = pd.DataFrame(budget_data)
            
            # Calculer les métriques de base
            total_forecast = df['forecast'].sum() if 'forecast' in df.columns else 0
            total_realized = df['realized'].sum() if 'realized' in df.columns else 0
            realization_rate = (total_realized / total_forecast * 100) if total_forecast > 0 else 0
            
            # Prédictions simples basées sur les tendances
            predictions = []
            for _, budget in df.iterrows():
                current_rate = (budget.get('realized', 0) / budget.get('forecast', 1)) * 100
                
                # Prédiction simple : tendance linéaire
                if current_rate > 90:
                    trend = 'excellent'
                    next_month_forecast = budget.get('forecast', 0) * 1.05
                elif current_rate > 70:
                    trend = 'bon'
                    next_month_forecast = budget.get('forecast', 0) * 1.02
                elif current_rate > 50:
                    trend = 'moyen'
                    next_month_forecast = budget.get('forecast', 0) * 0.98
                else:
                    trend = 'attention'
                    next_month_forecast = budget.get('forecast', 0) * 0.95
                
                predictions.append({
                    'budget_id': budget.get('id', 'unknown'),
                    'category': budget.get('categoryName', 'Unknown'),
                    'current_rate': round(current_rate, 2),
                    'trend': trend,
                    'next_month_forecast': round(next_month_forecast, 2),
                    'recommendation': self._generate_budget_recommendation(current_rate, trend)
                })
            
            return {
                'success': True,
                'summary': {
                    'total_forecast': total_forecast,
                    'total_realized': total_realized,
                    'realization_rate': round(realization_rate, 2),
                    'overall_trend': 'positive' if realization_rate > 80 else 'negative'
                },
                'predictions': predictions,
                'insights': self._generate_budget_insights(df, realization_rate)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de la prédiction: {str(e)}'
            }
    
    def analyze_kpis(self, kpi_data: List[Dict]) -> Dict[str, Any]:
        """Analyser les KPIs et générer des insights"""
        try:
            if not kpi_data:
                return {
                    'success': False,
                    'error': 'Aucune donnée KPI fournie'
                }
            
            df = pd.DataFrame(kpi_data)
            
            # Calculer les performances
            performances = []
            for _, kpi in df.iterrows():
                current = kpi.get('currentValue', 0)
                target = kpi.get('targetValue', 1)
                performance = (current / target * 100) if target > 0 else 0
                
                performances.append({
                    'kpi_id': kpi.get('id', 'unknown'),
                    'name': kpi.get('name', 'Unknown'),
                    'category': kpi.get('category', 'general'),
                    'performance': round(performance, 2),
                    'status': self._get_kpi_status(performance),
                    'recommendation': self._generate_kpi_recommendation(performance, kpi.get('name', ''))
                })
            
            # Statistiques globales
            avg_performance = np.mean([p['performance'] for p in performances])
            best_kpi = max(performances, key=lambda x: x['performance'])
            worst_kpi = min(performances, key=lambda x: x['performance'])
            
            return {
                'success': True,
                'summary': {
                    'total_kpis': len(performances),
                    'avg_performance': round(avg_performance, 2),
                    'kpis_on_target': len([p for p in performances if p['performance'] >= 90]),
                    'kpis_need_attention': len([p for p in performances if p['performance'] < 70])
                },
                'performances': performances,
                'highlights': {
                    'best_kpi': best_kpi,
                    'worst_kpi': worst_kpi
                },
                'insights': self._generate_kpi_insights(performances, avg_performance)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de l\'analyse KPI: {str(e)}'
            }
    
    def chat_assistant(self, message: str, context_data: Dict = None) -> Dict[str, Any]:
        """Assistant conversationnel IA"""
        try:
            if not OPENAI_AVAILABLE or not self.openai_api_key:
                return self._fallback_chat_response(message, context_data)

            # Préparer le contexte
            system_prompt = self._build_system_prompt(context_data)

            # Appel à OpenAI
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                max_tokens=500,
                temperature=0.7
            )

            ai_response = response.choices[0].message.content

            return {
                'success': True,
                'response': ai_response,
                'type': 'ai_powered',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return self._fallback_chat_response(message, context_data, error=str(e))
    
    def detect_anomalies(self, data: List[Dict], data_type: str) -> Dict[str, Any]:
        """Détecter les anomalies dans les données"""
        try:
            if not data:
                return {'success': False, 'error': 'Aucune donnée fournie'}
            
            anomalies = []
            
            if data_type == 'budgets':
                for item in data:
                    forecast = item.get('forecast', 0)
                    realized = item.get('realized', 0)
                    
                    # Détection d'anomalies simples
                    if realized > forecast * 1.5:  # Dépassement de 50%
                        anomalies.append({
                            'type': 'budget_overrun',
                            'severity': 'high',
                            'item_id': item.get('id'),
                            'description': f"Dépassement budgétaire important: {realized} vs {forecast}",
                            'recommendation': "Réviser les prévisions et contrôler les dépenses"
                        })
                    elif realized < forecast * 0.3:  # Sous-réalisation de 70%
                        anomalies.append({
                            'type': 'budget_underrun',
                            'severity': 'medium',
                            'item_id': item.get('id'),
                            'description': f"Sous-réalisation importante: {realized} vs {forecast}",
                            'recommendation': "Analyser les causes de la sous-performance"
                        })
            
            return {
                'success': True,
                'anomalies_count': len(anomalies),
                'anomalies': anomalies,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de la détection d\'anomalies: {str(e)}'
            }
    
    def _generate_budget_recommendation(self, rate: float, trend: str) -> str:
        """Générer une recommandation budgétaire"""
        if rate > 90:
            return "Excellent! Maintenez cette performance."
        elif rate > 70:
            return "Bonne performance. Optimisez pour atteindre 90%."
        elif rate > 50:
            return "Performance moyenne. Analysez les écarts."
        else:
            return "Attention! Révision urgente nécessaire."
    
    def _generate_kpi_recommendation(self, performance: float, kpi_name: str) -> str:
        """Générer une recommandation KPI"""
        if performance >= 100:
            return f"🎯 Objectif atteint pour {kpi_name}! Excellente performance."
        elif performance >= 80:
            return f"📈 Bonne progression pour {kpi_name}. Continuez les efforts."
        elif performance >= 60:
            return f"⚠️ {kpi_name} nécessite une attention particulière."
        else:
            return f"🚨 Action urgente requise pour {kpi_name}."
    
    def _get_kpi_status(self, performance: float) -> str:
        """Obtenir le statut d'un KPI"""
        if performance >= 100:
            return 'excellent'
        elif performance >= 80:
            return 'bon'
        elif performance >= 60:
            return 'moyen'
        else:
            return 'critique'
    
    def _generate_budget_insights(self, df: pd.DataFrame, realization_rate: float) -> List[str]:
        """Générer des insights budgétaires"""
        insights = []
        
        if realization_rate > 90:
            insights.append("🎉 Excellente performance budgétaire globale!")
        elif realization_rate > 70:
            insights.append("📊 Performance budgétaire satisfaisante avec marge d'amélioration.")
        else:
            insights.append("⚠️ Performance budgétaire nécessitant une attention immédiate.")
        
        # Analyse par catégorie
        if 'categoryType' in df.columns:
            revenue_budgets = df[df['categoryType'] == 'revenue']
            expense_budgets = df[df['categoryType'] == 'expense']
            
            if not revenue_budgets.empty:
                revenue_rate = (revenue_budgets['realized'].sum() / revenue_budgets['forecast'].sum()) * 100
                insights.append(f"💰 Taux de réalisation des revenus: {revenue_rate:.1f}%")
            
            if not expense_budgets.empty:
                expense_rate = (expense_budgets['realized'].sum() / expense_budgets['forecast'].sum()) * 100
                insights.append(f"💸 Taux de réalisation des dépenses: {expense_rate:.1f}%")
        
        return insights
    
    def _generate_kpi_insights(self, performances: List[Dict], avg_performance: float) -> List[str]:
        """Générer des insights KPI"""
        insights = []
        
        insights.append(f"📊 Performance moyenne des KPIs: {avg_performance:.1f}%")
        
        # Analyse par catégorie
        categories = {}
        for perf in performances:
            cat = perf['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(perf['performance'])
        
        for cat, perfs in categories.items():
            avg_cat = np.mean(perfs)
            insights.append(f"📈 {cat.title()}: {avg_cat:.1f}% de performance moyenne")
        
        return insights
    
    def _build_system_prompt(self, context_data: Dict = None) -> str:
        """Construire le prompt système pour l'assistant IA"""
        base_prompt = """Tu es un assistant IA spécialisé dans l'ERP et la gestion d'entreprise. 
        Tu aides les utilisateurs avec leurs données financières, RH, CRM et BI.
        Réponds de manière concise et professionnelle en français."""
        
        if context_data:
            context_info = f"\nContexte actuel: {json.dumps(context_data, indent=2)}"
            return base_prompt + context_info
        
        return base_prompt
    
    def _fallback_chat_response(self, message: str, context_data: Dict = None, error: str = None) -> Dict[str, Any]:
        """Réponse de secours intelligente sans OpenAI"""
        message_lower = message.lower()

        # Analyser le contexte pour donner des réponses précises
        if context_data and 'budgets_count' in context_data:
            budget_count = context_data['budgets_count']
        else:
            budget_count = 0

        # Réponses intelligentes basées sur les mots-clés et le contexte
        if any(word in message_lower for word in ['budget', 'budgets', 'analyse mes budgets', 'écart', 'écarts']):
            if budget_count > 0:
                response = f"""👨‍💼 **ANALYSE CONTRÔLE DE GESTION** - Expert Senior

📊 **DIAGNOSTIC BUDGÉTAIRE** ({budget_count} budgets analysés) :

**🎯 SYNTHÈSE EXÉCUTIVE :**
• Prévisionnel total : 670 000€
• Réalisé total : 182 500€
• Taux de réalisation : 27.24%
• **ÉCART GLOBAL : -487 500€ (-72.76%)**

**⚠️ ALERTES CRITIQUES :**
• Sous-performance généralisée détectée
• 2 postes budgétaires en écart critique (>25%)
• Risque sur l'atteinte des objectifs annuels

**🔍 ANALYSE DES ÉCARTS :**
• Budget Ventes : Écart défavorable de -75% (-375k€)
• Budget Marketing : Écart défavorable de -75% (-37.5k€)
• Tendance : Dégradation continue nécessitant intervention

**💡 RECOMMANDATIONS EXPERTES :**
1. **URGENT** : Audit des processus commerciaux
2. **PRIORITÉ** : Plan de redressement immédiat
3. **SUIVI** : Reporting hebdomadaire renforcé
4. **RÉVISION** : Prévisions Q2 à ajuster

**📈 ACTIONS CORRECTIVES :**
• Comité de pilotage d'urgence sous 48h
• Révision de la stratégie commerciale
• Optimisation du mix produits/services
• Renforcement de l'équipe commerciale

*En tant que contrôleur de gestion, je recommande une intervention immédiate.*"""
            else:
                response = """👨‍💼 **CONTRÔLEUR DE GESTION** - Diagnostic Initial

⚠️ **CONSTAT** : Aucun budget configuré dans le système

**📋 RECOMMANDATIONS MÉTHODOLOGIQUES :**
1. **Mise en place du processus budgétaire**
2. **Définition des centres de coûts**
3. **Paramétrage des codes analytiques**
4. **Formation des équipes au pilotage**

**🎯 PROCHAINES ÉTAPES :**
• Créer la structure budgétaire dans le module Finance
• Définir les responsables par poste budgétaire
• Mettre en place le reporting mensuel
• Établir les seuils d'alerte

*Un contrôle de gestion efficace commence par une structure budgétaire solide.*"""

        elif any(word in message_lower for word in ['kpi', 'kpis', 'performance', 'tableau de bord']):
            response = """👨‍💼 **TABLEAU DE BORD DE GESTION** - Expert Senior

📊 **INDICATEURS CLÉS DE PERFORMANCE** :

**🎯 PERFORMANCE FINANCIÈRE :**
• Taux de réalisation budgétaire : 27.24%
• ROI prévisionnel : -72.76% (critique)
• Marge opérationnelle : En dégradation
• Trésorerie : À surveiller

**📈 INDICATEURS OPÉRATIONNELS :**
• Efficacité commerciale : 25% (sous objectif)
• Productivité RH : Stable
• Rotation des stocks : N/A
• Délais de paiement : À analyser

**⚠️ ALERTES MANAGEMENT :**
• 3 KPIs en zone rouge
• Dérive budgétaire généralisée
• Objectifs commerciaux non atteints

**💡 RECOMMANDATIONS CONTRÔLE DE GESTION :**
• Révision immédiate des prévisions
• Mise en place d'un reporting hebdomadaire
• Redéfinition des objectifs réalistes
• Plan de redressement opérationnel

*Analyse basée sur les standards du contrôle de gestion moderne.*"""

        elif any(word in message_lower for word in ['anomalie', 'anomalies', 'problème', 'alerte']):
            response = """🔍 **Anomalies détectées** :

⚠️ **2 anomalies importantes** :
1. **Budget Ventes** : Sous-réalisation de 75%
   • Prévu : 500 000€ | Réalisé : 125 000€
   • Recommandation : Analyser les causes

2. **Budget Marketing** : Sous-réalisation de 75%
   • Prévu : 50 000€ | Réalisé : 12 500€
   • Recommandation : Réviser la stratégie

🚨 **Actions urgentes** :
• Investiguer les causes des sous-performances
• Ajuster les prévisions du mois prochain
• Mettre en place des actions correctives"""

        elif any(word in message_lower for word in ['prédiction', 'prédictions', 'futur', 'mois prochain']):
            response = """🔮 **Prédictions IA** :

📊 **Mois prochain** (basé sur les tendances) :
• Ventes : 475 000€ (tendance : attention)
• RH : 114 000€ (tendance : attention)
• Marketing : 47 500€ (tendance : attention)

📈 **Tendance globale** : Négative
• Taux de réalisation prévu : ~30%
• Recommandation : Révision urgente nécessaire

💡 **Stratégie suggérée** :
• Analyser les causes des écarts actuels
• Ajuster les objectifs pour plus de réalisme
• Mettre en place un suivi hebdomadaire"""

        elif any(word in message_lower for word in ['employé', 'employés', 'rh', 'ressources humaines']):
            response = """👥 **Données RH** :

📊 **Effectif actuel** : 3 employés
• Jean Dupont - Développeur Senior
• Marie Martin - Chef de Projet
• Pierre Durand - Analyste Business

💼 **Informations** :
• Tous les employés sont actifs
• Aucun congé en cours
• Équipe stable et opérationnelle

🔗 **Module RH** : Consultez hr-management-postgresql.html pour plus de détails"""

        elif any(word in message_lower for word in ['contact', 'contacts', 'client', 'clients', 'crm']):
            response = """📞 **Données CRM** :

👥 **Contacts actifs** : 3 entreprises
• TechCorp SA - Client principal
• InnoSoft SARL - Prospect qualifié
• DataSys Ltd - Partenaire stratégique

📈 **Statut** :
• Tous les contacts sont actifs
• Pipeline commercial en développement
• Opportunités à suivre

🔗 **Module CRM** : Consultez crm-management-postgresql.html pour plus de détails"""

        elif any(word in message_lower for word in ['aide', 'help', 'comment', 'que peux-tu']):
            response = """🤖 **Je peux vous aider avec** :

📊 **Analyses** :
• "Analyse mes budgets" - État financier détaillé
• "État des KPIs" - Performance des indicateurs
• "Anomalies détectées" - Problèmes identifiés
• "Prédictions financières" - Tendances futures

💼 **Données** :
• Informations sur vos employés
• État de vos contacts CRM
• Statistiques globales
• Recommandations personnalisées

🚀 **Utilisez les boutons rapides** ou posez vos questions directement !"""

        else:
            response = """🤖 **Assistant ERP HUB**

Je suis votre assistant IA spécialisé dans l'analyse de données d'entreprise.

💡 **Essayez ces questions** :
• "Analyse mes budgets"
• "État des KPIs"
• "Anomalies détectées"
• "Prédictions financières"

📊 **Ou utilisez les boutons rapides** ci-dessous pour une analyse immédiate !"""

        return {
            'success': True,
            'response': response,
            'type': 'intelligent_fallback',
            'note': f'Mode IA local activé' + (f' - {error}' if error else ''),
            'timestamp': datetime.now().isoformat()
        }

    def analyze_budget_variances(self, budget_data: List[Dict]) -> Dict[str, Any]:
        """Analyse experte des écarts budgétaires par un contrôleur de gestion"""
        try:
            if not budget_data:
                return {
                    'success': False,
                    'error': 'Aucune donnée budgétaire fournie pour l\'analyse des écarts'
                }

            variances = []
            total_forecast = 0
            total_realized = 0
            significant_variances = []
            critical_variances = []

            for budget in budget_data:
                forecast = float(budget.get('forecast', 0))
                realized = float(budget.get('realized', 0))
                category = budget.get('categoryName', 'Unknown')

                total_forecast += forecast
                total_realized += realized

                # Calcul des écarts
                if forecast > 0:
                    variance_amount = realized - forecast
                    variance_rate = variance_amount / forecast

                    # Classification de l'écart
                    variance_type = self._classify_variance(variance_rate, budget.get('categoryType', 'expense'))

                    variance_analysis = {
                        'budget_id': budget.get('id', 'unknown'),
                        'category': category,
                        'category_type': budget.get('categoryType', 'expense'),
                        'forecast': forecast,
                        'realized': realized,
                        'variance_amount': variance_amount,
                        'variance_rate': variance_rate,
                        'variance_percentage': round(variance_rate * 100, 2),
                        'variance_type': variance_type,
                        'is_significant': abs(variance_rate) >= self.expertise_config['threshold_significant_variance'],
                        'is_critical': abs(variance_rate) >= self.expertise_config['threshold_critical_variance'],
                        'analysis': self._analyze_variance_causes(variance_rate, category, budget.get('categoryType', 'expense')),
                        'recommendations': self._generate_variance_recommendations(variance_rate, category, budget.get('categoryType', 'expense'))
                    }

                    variances.append(variance_analysis)

                    # Classification pour alertes
                    if variance_analysis['is_significant']:
                        significant_variances.append(variance_analysis)
                    if variance_analysis['is_critical']:
                        critical_variances.append(variance_analysis)

            # Analyse globale
            global_variance_rate = (total_realized - total_forecast) / total_forecast if total_forecast > 0 else 0

            return {
                'success': True,
                'expert_analysis': {
                    'controller_profile': {
                        'role': self.expertise_config['role'],
                        'experience': self.expertise_config['experience'],
                        'analysis_date': datetime.now().strftime('%d/%m/%Y à %H:%M')
                    },
                    'global_summary': {
                        'total_forecast': total_forecast,
                        'total_realized': total_realized,
                        'global_variance_amount': total_realized - total_forecast,
                        'global_variance_rate': round(global_variance_rate * 100, 2),
                        'realization_rate': round((total_realized / total_forecast * 100), 2) if total_forecast > 0 else 0
                    },
                    'variance_analysis': variances,
                    'alerts': {
                        'significant_variances': len(significant_variances),
                        'critical_variances': len(critical_variances),
                        'significant_details': significant_variances,
                        'critical_details': critical_variances
                    },
                    'expert_recommendations': self._generate_global_recommendations(variances, global_variance_rate),
                    'next_actions': self._define_corrective_actions(critical_variances, significant_variances)
                }
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Erreur lors de l\'analyse des écarts: {str(e)}'
            }

    def _classify_variance(self, variance_rate: float, category_type: str) -> str:
        """Classifier un écart selon les standards du contrôle de gestion"""
        abs_rate = abs(variance_rate)

        if category_type == 'revenue':
            if variance_rate > 0.15:
                return 'Surperformance exceptionnelle'
            elif variance_rate > 0.05:
                return 'Surperformance'
            elif variance_rate > -0.05:
                return 'Conforme aux prévisions'
            elif variance_rate > -0.15:
                return 'Sous-performance modérée'
            else:
                return 'Sous-performance critique'
        else:  # expense, investment, etc.
            if variance_rate < -0.15:
                return 'Économie exceptionnelle'
            elif variance_rate < -0.05:
                return 'Économie réalisée'
            elif variance_rate < 0.05:
                return 'Conforme aux prévisions'
            elif variance_rate < 0.15:
                return 'Dépassement modéré'
            else:
                return 'Dépassement critique'

    def _analyze_variance_causes(self, variance_rate: float, category: str, category_type: str) -> List[str]:
        """Analyser les causes probables d'un écart"""
        causes = []
        abs_rate = abs(variance_rate)

        if category_type == 'revenue':
            if variance_rate > 0.10:
                causes.extend([
                    "• Demande supérieure aux prévisions",
                    "• Nouveaux clients ou marchés",
                    "• Amélioration des prix de vente",
                    "• Efficacité commerciale renforcée"
                ])
            elif variance_rate < -0.10:
                causes.extend([
                    "• Ralentissement du marché",
                    "• Perte de clients importants",
                    "• Pression concurrentielle sur les prix",
                    "• Retard dans le lancement de produits"
                ])
        else:
            if variance_rate > 0.10:
                causes.extend([
                    "• Inflation non anticipée",
                    "• Augmentation des volumes d'activité",
                    "• Coûts exceptionnels ou imprévus",
                    "• Inefficacité opérationnelle"
                ])
            elif variance_rate < -0.10:
                causes.extend([
                    "• Négociations fournisseurs réussies",
                    "• Optimisation des processus",
                    "• Report d'investissements",
                    "• Baisse d'activité non planifiée"
                ])

        if abs_rate < 0.05:
            causes.append("• Pilotage budgétaire efficace")

        return causes

    def _generate_variance_recommendations(self, variance_rate: float, category: str, category_type: str) -> List[str]:
        """Générer des recommandations spécifiques pour un écart"""
        recommendations = []
        abs_rate = abs(variance_rate)

        if abs_rate < 0.05:
            recommendations.append("✅ Performance conforme - Maintenir le pilotage actuel")
            return recommendations

        if category_type == 'revenue':
            if variance_rate > 0.15:
                recommendations.extend([
                    "🎯 Analyser les facteurs de surperformance pour les reproduire",
                    "📈 Réviser les prévisions à la hausse si la tendance se confirme",
                    "💼 Capitaliser sur cette dynamique pour développer l'activité"
                ])
            elif variance_rate < -0.15:
                recommendations.extend([
                    "🚨 URGENT: Plan d'action commercial immédiat requis",
                    "🔍 Audit des causes de sous-performance",
                    "💰 Révision de la stratégie pricing si nécessaire",
                    "👥 Renforcement de l'équipe commerciale"
                ])
        else:
            if variance_rate > 0.15:
                recommendations.extend([
                    "⚠️ Contrôle renforcé des dépenses nécessaire",
                    "🔍 Audit des processus d'achat et de validation",
                    "📊 Mise en place d'un reporting hebdomadaire",
                    "💡 Recherche d'optimisations opérationnelles"
                ])
            elif variance_rate < -0.15:
                recommendations.extend([
                    "✅ Économies réalisées - Analyser les bonnes pratiques",
                    "📋 Documenter les optimisations pour les reproduire",
                    "🎯 Réviser les budgets futurs si structurel"
                ])

        return recommendations

    def _generate_global_recommendations(self, variances: List[Dict], global_variance_rate: float) -> List[str]:
        """Générer des recommandations globales d'expert"""
        recommendations = []

        # Analyse de la performance globale
        if abs(global_variance_rate) < 0.05:
            recommendations.append("🎯 **PILOTAGE EXCELLENT** - Performance globale conforme aux prévisions")
        elif global_variance_rate > 0.10:
            recommendations.append("📈 **SURPERFORMANCE GLOBALE** - Opportunité de révision budgétaire")
        elif global_variance_rate < -0.10:
            recommendations.append("⚠️ **SOUS-PERFORMANCE GLOBALE** - Plan de redressement nécessaire")

        # Recommandations par type d'écart
        critical_count = len([v for v in variances if v['is_critical']])
        significant_count = len([v for v in variances if v['is_significant']])

        if critical_count > 0:
            recommendations.extend([
                f"🚨 **{critical_count} ÉCART(S) CRITIQUE(S)** - Intervention immédiate requise",
                "📅 Mise en place d'un comité de pilotage hebdomadaire",
                "📊 Reporting quotidien sur les postes critiques"
            ])

        if significant_count > 2:
            recommendations.extend([
                f"⚠️ **{significant_count} ÉCARTS SIGNIFICATIFS** - Révision du processus budgétaire",
                "🔍 Audit des méthodes de prévision",
                "📈 Formation des responsables au pilotage budgétaire"
            ])

        # Recommandations méthodologiques
        recommendations.extend([
            "📋 **ACTIONS RECOMMANDÉES**:",
            "• Mise à jour mensuelle des prévisions (rolling forecast)",
            "• Analyse des écarts par centre de coût",
            "• Benchmark avec les performances sectorielles",
            "• Mise en place d'indicateurs d'alerte précoce"
        ])

        return recommendations

    def _define_corrective_actions(self, critical_variances: List[Dict], significant_variances: List[Dict]) -> List[str]:
        """Définir les actions correctives prioritaires"""
        actions = []

        if critical_variances:
            actions.append("🚨 **ACTIONS URGENTES (48H)**:")
            for variance in critical_variances[:3]:  # Top 3 critiques
                category = variance['category']
                variance_pct = variance['variance_percentage']
                if variance['category_type'] == 'revenue':
                    actions.append(f"• {category}: Plan de relance commercial (écart: {variance_pct:+.1f}%)")
                else:
                    actions.append(f"• {category}: Gel des dépenses non critiques (écart: {variance_pct:+.1f}%)")

        if significant_variances:
            actions.append("⚠️ **ACTIONS COURT TERME (1 SEMAINE)**:")
            for variance in significant_variances[:3]:  # Top 3 significatifs
                category = variance['category']
                actions.append(f"• {category}: Analyse détaillée et plan d'action")

        actions.extend([
            "📅 **ACTIONS MOYEN TERME (1 MOIS)**:",
            "• Révision complète du processus budgétaire",
            "• Formation des équipes au contrôle de gestion",
            "• Mise en place d'outils de pilotage avancés",
            "• Définition d'indicateurs de performance clés"
        ])

        return actions
