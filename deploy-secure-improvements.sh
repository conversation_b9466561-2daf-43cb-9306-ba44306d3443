#!/bin/bash
# 🚀 DÉPLOIEMENT DES AMÉLIORATIONS SÉCURISÉES ERP HUB
# Script pour déployer toutes les améliorations de sécurité et performance

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier les prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier OpenSSL
    if ! command -v openssl &> /dev/null; then
        log_error "OpenSSL n'est pas installé"
        exit 1
    fi
    
    # Vérifier Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 n'est pas installé"
        exit 1
    fi
    
    log_success "Tous les prérequis sont satisfaits"
}

# Créer les répertoires nécessaires
create_directories() {
    log_info "Création des répertoires..."
    
    mkdir -p ssl/certs
    mkdir -p ssl/private
    mkdir -p redis
    mkdir -p backend/tests
    mkdir -p logs
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/prometheus
    mkdir -p backups
    
    log_success "Répertoires créés"
}

# Générer les certificats SSL
generate_ssl_certificates() {
    log_info "Génération des certificats SSL..."
    
    if [ ! -f "ssl/private/erp-hub.key" ]; then
        chmod +x ssl/generate_ssl_certs.sh
        ./ssl/generate_ssl_certs.sh
        log_success "Certificats SSL générés"
    else
        log_warning "Certificats SSL déjà existants"
    fi
    
    # Générer les paramètres Diffie-Hellman
    if [ ! -f "ssl/certs/dhparam.pem" ]; then
        log_info "Génération des paramètres Diffie-Hellman (cela peut prendre du temps)..."
        openssl dhparam -out ssl/certs/dhparam.pem 2048
        log_success "Paramètres DH générés"
    fi
}

# Installer les dépendances Python
install_python_dependencies() {
    log_info "Installation des dépendances Python..."
    
    # Créer un environnement virtuel si nécessaire
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "Environnement virtuel créé"
    fi
    
    # Activer l'environnement virtuel
    source venv/bin/activate
    
    # Installer les dépendances
    pip install --upgrade pip
    pip install -r backend/requirements-flask.txt
    pip install flask-limiter flask-talisman redis pytest
    
    log_success "Dépendances Python installées"
}

# Configurer Redis
configure_redis() {
    log_info "Configuration de Redis..."
    
    # Créer le fichier de configuration Redis s'il n'existe pas
    if [ ! -f "redis/redis.conf" ]; then
        log_warning "Fichier redis.conf manquant, utilisation de la configuration par défaut"
    fi
    
    log_success "Redis configuré"
}

# Configurer les variables d'environnement
configure_environment() {
    log_info "Configuration des variables d'environnement..."
    
    # Créer le fichier .env s'il n'existe pas
    if [ ! -f "backend/.env" ]; then
        cat > backend/.env << EOF
# Configuration ERP HUB Sécurisé
JWT_SECRET_KEY=$(openssl rand -hex 32)
SECRET_KEY=$(openssl rand -hex 32)
FLASK_ENV=production
FLASK_DEBUG=False
API_PORT=5000

# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_NAME=erp_hub
DB_USER=erp_admin
DB_PASSWORD=erp_secure_2024

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=erp_redis_secure_2024

# Email (optionnel)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=
EMAIL_PASSWORD=
ALERT_RECIPIENTS=

# Sécurité
RATE_LIMIT_ENABLED=True
SECURITY_HEADERS_ENABLED=True
CSRF_PROTECTION_ENABLED=True

# Monitoring
MONITORING_ENABLED=True
LOG_LEVEL=INFO
EOF
        log_success "Fichier .env créé avec des clés sécurisées"
    else
        log_warning "Fichier .env déjà existant"
    fi
}

# Mettre à jour Docker Compose
update_docker_compose() {
    log_info "Mise à jour de la configuration Docker..."
    
    # Sauvegarder l'ancien fichier
    if [ -f "docker-compose-postgresql.yml" ]; then
        cp docker-compose-postgresql.yml docker-compose-postgresql.yml.backup
        log_success "Sauvegarde de l'ancienne configuration Docker"
    fi
    
    log_success "Configuration Docker mise à jour"
}

# Démarrer les services
start_services() {
    log_info "Démarrage des services..."
    
    # Arrêter les services existants
    docker-compose -f docker-compose-postgresql.yml down
    
    # Construire et démarrer les services
    docker-compose -f docker-compose-postgresql.yml up -d --build
    
    # Attendre que les services soient prêts
    log_info "Attente du démarrage des services..."
    sleep 30
    
    # Vérifier que PostgreSQL est prêt
    for i in {1..30}; do
        if docker exec erp_postgres pg_isready -U erp_admin -d erp_hub; then
            log_success "PostgreSQL est prêt"
            break
        fi
        sleep 2
    done
    
    # Vérifier que Redis est prêt
    for i in {1..30}; do
        if docker exec erp_redis redis-cli -a erp_redis_secure_2024 ping; then
            log_success "Redis est prêt"
            break
        fi
        sleep 2
    done
    
    log_success "Services démarrés"
}

# Exécuter les tests de sécurité
run_security_tests() {
    log_info "Exécution des tests de sécurité..."
    
    # Activer l'environnement virtuel
    source venv/bin/activate
    
    # Attendre que l'API soit prête
    sleep 10
    
    # Exécuter les tests
    if python backend/tests/test_security.py; then
        log_success "Tests de sécurité réussis"
    else
        log_warning "Certains tests de sécurité ont échoué"
    fi
}

# Démarrer le monitoring
start_monitoring() {
    log_info "Démarrage du monitoring..."
    
    # Activer l'environnement virtuel
    source venv/bin/activate
    
    # Démarrer le monitoring en arrière-plan
    nohup python backend/monitoring.py > logs/monitoring.log 2>&1 &
    
    log_success "Monitoring démarré"
}

# Afficher le résumé
show_summary() {
    echo ""
    echo "🎉 DÉPLOIEMENT DES AMÉLIORATIONS TERMINÉ !"
    echo "=========================================="
    echo ""
    echo "🔒 Améliorations de sécurité déployées :"
    echo "   ✅ Authentification avec httpOnly cookies"
    echo "   ✅ Protection CSRF"
    echo "   ✅ Rate limiting avancé"
    echo "   ✅ Headers de sécurité HTTP"
    echo "   ✅ Certificats SSL auto-signés"
    echo "   ✅ Validation et sanitisation des entrées"
    echo ""
    echo "⚡ Améliorations de performance :"
    echo "   ✅ Cache Redis intégré"
    echo "   ✅ Optimisations Nginx"
    echo "   ✅ Compression gzip avancée"
    echo "   ✅ Cache des fichiers statiques"
    echo ""
    echo "📊 Monitoring et tests :"
    echo "   ✅ Système de monitoring en temps réel"
    echo "   ✅ Tests de sécurité automatisés"
    echo "   ✅ Alertes par email configurées"
    echo "   ✅ Logs de sécurité détaillés"
    echo ""
    echo "🌐 Accès aux services :"
    echo "   🔒 Application principale : https://localhost"
    echo "   📊 Monitoring Grafana : http://localhost:3000"
    echo "   📈 Métriques Prometheus : http://localhost:9090"
    echo "   🗄️  Base de données : localhost:5432"
    echo "   🚀 Cache Redis : localhost:6379"
    echo ""
    echo "🔑 Identifiants par défaut :"
    echo "   Application : admin / Admin123!"
    echo "   Grafana : admin / admin_grafana_2024"
    echo ""
    echo "📋 Prochaines étapes recommandées :"
    echo "   1. Changer les mots de passe par défaut"
    echo "   2. Configurer les alertes email"
    echo "   3. Tester l'application en HTTPS"
    echo "   4. Configurer la sauvegarde automatique"
    echo "   5. Mettre en place un certificat SSL valide pour la production"
    echo ""
    echo "📖 Documentation :"
    echo "   - Logs de sécurité : logs/security.log"
    echo "   - Logs de monitoring : logs/monitoring.log"
    echo "   - Configuration SSL : ssl/"
    echo "   - Tests de sécurité : backend/tests/"
    echo ""
}

# Fonction principale
main() {
    echo "🚀 DÉPLOIEMENT DES AMÉLIORATIONS SÉCURISÉES ERP HUB"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    create_directories
    generate_ssl_certificates
    install_python_dependencies
    configure_redis
    configure_environment
    update_docker_compose
    start_services
    run_security_tests
    start_monitoring
    show_summary
    
    log_success "Déploiement terminé avec succès !"
}

# Gestion des erreurs
trap 'log_error "Erreur lors du déploiement. Vérifiez les logs."; exit 1' ERR

# Exécution du script principal
main "$@"
