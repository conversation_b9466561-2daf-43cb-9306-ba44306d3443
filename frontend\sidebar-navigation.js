/**
 * Navigation verticale commune pour ERP HUB
 * Fonctions JavaScript pour la gestion de la sidebar
 */

// Configuration des modules
const MODULES_CONFIG = {
    'index.html': { name: 'Accueil', icon: 'home' },
    'dashboard-global-postgresql.html': { name: 'Dashboard', icon: 'dashboard' },
    'finance-management.html': { name: 'Finance', icon: 'account_balance' },
    'hr-management-postgresql.html': { name: 'RH', icon: 'people' },
    'crm-management-postgresql.html': { name: 'CRM', icon: 'contacts' },
    'sales-management-postgresql.html': { name: 'Ventes', icon: 'trending_up' },
    'purchase-management-postgresql.html': { name: 'Achats', icon: 'shopping_cart' },
    'stock-management-postgresql.html': { name: 'Stocks', icon: 'inventory_2' },
    'logistics-management-postgresql.html': { name: 'Logistique', icon: 'local_shipping' },
    'bi-management-postgresql.html': { name: 'B<PERSON>', icon: 'analytics' },
    'ai-assistant.html': { name: 'Assistant IA', icon: 'smart_toy' }
};

/**
 * <PERSON>énère le HTML de la navigation verticale
 */
function generateSidebarHTML() {
    return `
        <!-- Bouton menu mobile -->
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <span class="material-icons">menu</span>
        </button>
        
        <!-- Overlay pour mobile -->
        <div class="sidebar-overlay" onclick="closeMobileMenu()"></div>
        
        <!-- Navigation verticale -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <span style="font-size: 2rem;">🏢</span>
                    <div>
                        <h1>ERP HUB</h1>
                        <p class="sidebar-subtitle">Système de Gestion Intégré</p>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-nav">
                <a href="index.html" class="nav-item" data-page="index.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">home</span>
                        <div>
                            <div class="nav-text">Accueil</div>
                            <div class="nav-description">Page d'accueil</div>
                        </div>
                    </div>
                </a>
                
                <a href="dashboard-global-postgresql.html" class="nav-item" data-page="dashboard-global-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">dashboard</span>
                        <div>
                            <div class="nav-text">Dashboard</div>
                            <div class="nav-description">Vue d'ensemble globale</div>
                        </div>
                    </div>
                </a>
                
                <a href="finance-management.html" class="nav-item" data-page="finance-management.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">account_balance</span>
                        <div>
                            <div class="nav-text">Finance</div>
                            <div class="nav-description">Budget & Comptabilité</div>
                        </div>
                    </div>
                </a>
                
                <a href="hr-management-postgresql.html" class="nav-item" data-page="hr-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">people</span>
                        <div>
                            <div class="nav-text">Ressources Humaines</div>
                            <div class="nav-description">Employés & Paie</div>
                        </div>
                    </div>
                </a>
                
                <a href="crm-management-postgresql.html" class="nav-item" data-page="crm-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">contacts</span>
                        <div>
                            <div class="nav-text">CRM</div>
                            <div class="nav-description">Relation Client</div>
                        </div>
                    </div>
                </a>
                
                <a href="sales-management-postgresql.html" class="nav-item" data-page="sales-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">trending_up</span>
                        <div>
                            <div class="nav-text">Ventes</div>
                            <div class="nav-description">Opportunités & Commandes</div>
                        </div>
                    </div>
                </a>
                
                <a href="purchase-management-postgresql.html" class="nav-item" data-page="purchase-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">shopping_cart</span>
                        <div>
                            <div class="nav-text">Achats</div>
                            <div class="nav-description">Fournisseurs & Commandes</div>
                        </div>
                    </div>
                </a>
                
                <a href="stock-management-postgresql.html" class="nav-item" data-page="stock-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">inventory_2</span>
                        <div>
                            <div class="nav-text">Stocks</div>
                            <div class="nav-description">Inventaire & Produits</div>
                        </div>
                    </div>
                </a>
                
                <a href="logistics-management-postgresql.html" class="nav-item" data-page="logistics-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">local_shipping</span>
                        <div>
                            <div class="nav-text">Logistique</div>
                            <div class="nav-description">Expéditions & Transport</div>
                        </div>
                    </div>
                </a>
                
                <a href="bi-management-postgresql.html" class="nav-item" data-page="bi-management-postgresql.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">analytics</span>
                        <div>
                            <div class="nav-text">Business Intelligence</div>
                            <div class="nav-description">Rapports & Analyses</div>
                        </div>
                    </div>
                </a>
                
                <a href="ai-assistant.html" class="nav-item" data-page="ai-assistant.html">
                    <div class="nav-item-content">
                        <span class="nav-icon material-icons">smart_toy</span>
                        <div>
                            <div class="nav-text">Assistant IA</div>
                            <div class="nav-description">Intelligence Artificielle</div>
                        </div>
                    </div>
                </a>
            </div>
        </nav>
    `;
}

/**
 * Gestion du menu mobile
 */
function toggleMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar && overlay) {
        sidebar.classList.toggle('open');
        overlay.classList.toggle('open');
    }
}

function closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar && overlay) {
        sidebar.classList.remove('open');
        overlay.classList.remove('open');
    }
}

/**
 * Marquer l'élément actuel dans la navigation
 */
function setActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        const page = item.getAttribute('data-page') || item.getAttribute('href');
        if (page === currentPage) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

/**
 * Initialiser la navigation
 */
function initSidebar() {
    // Injecter le HTML de la sidebar si elle n'existe pas
    if (!document.getElementById('sidebar')) {
        document.body.insertAdjacentHTML('afterbegin', generateSidebarHTML());
    }
    
    // Marquer l'élément actif
    setActiveNavItem();
    
    // Fermer le menu mobile lors du clic sur un lien
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', closeMobileMenu);
    });
    
    // Gestion du clavier pour l'accessibilité
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });
}

/**
 * Obtenir le titre de la page actuelle
 */
function getCurrentPageTitle() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const config = MODULES_CONFIG[currentPage];
    return config ? config.name : 'ERP HUB';
}

/**
 * Obtenir l'icône de la page actuelle
 */
function getCurrentPageIcon() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const config = MODULES_CONFIG[currentPage];
    return config ? config.icon : 'business';
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', initSidebar);

// Export pour utilisation dans d'autres scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initSidebar,
        toggleMobileMenu,
        closeMobileMenu,
        setActiveNavItem,
        getCurrentPageTitle,
        getCurrentPageIcon
    };
}
