@echo off
echo ========================================
echo    DIAGNOSTIC ERP HUB
echo ========================================
echo.

echo 1. Version Node.js:
node --version
echo.

echo 2. Version npm:
npm --version
echo.

echo 3. Dossier actuel:
cd
echo.

echo 4. Contenu du dossier:
dir
echo.

echo 5. Verification dossier frontend:
if exist "frontend" (
    echo ✅ Dossier frontend existe
    cd frontend
    echo.
    echo 6. Contenu du dossier frontend:
    dir
    echo.
    echo 7. Verification package.json:
    if exist "package.json" (
        echo ✅ package.json existe
    ) else (
        echo ❌ package.json manquant
    )
    echo.
    echo 8. Verification node_modules:
    if exist "node_modules" (
        echo ✅ node_modules existe
    ) else (
        echo ❌ node_modules manquant - installation nécessaire
    )
) else (
    echo ❌ Dossier frontend manquant
)

echo.
echo ========================================
echo    FIN DU DIAGNOSTIC
echo ========================================
pause
