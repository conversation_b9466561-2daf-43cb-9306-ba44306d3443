#!/usr/bin/env python
"""
Script de test pour l'Agent CRM
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_crm_models():
    """Test des modèles CRM"""
    try:
        from agents.crm.models import Contact, Opportunity, Campaign, SupportTicket, Interaction
        print("✅ Import des modèles CRM réussi")
        
        # Test des choix
        print(f"✅ Types de contacts: {len(Contact.CONTACT_TYPES)} options")
        print(f"✅ Statuts de contacts: {len(Contact.CONTACT_STATUS)} options")
        print(f"✅ Sources de leads: {len(Contact.LEAD_SOURCES)} options")
        print(f"✅ Étapes d'opportunités: {len(Opportunity.OPPORTUNITY_STAGES)} options")
        print(f"✅ Types de campagnes: {len(Campaign.CAMPAIGN_TYPES)} options")
        print(f"✅ Types de tickets: {len(SupportTicket.TICKET_TYPES)} options")
        print(f"✅ Types d'interactions: {len(Interaction.INTERACTION_TYPES)} options")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de l'import des modèles: {e}")
        return False

def test_crm_services():
    """Test des services CRM"""
    try:
        from agents.crm.services import CRMService
        from core.models import Tenant
        
        # Créer un tenant de test
        tenant, created = Tenant.objects.get_or_create(
            name="Test Tenant",
            defaults={
                'slug': 'test-tenant',
                'description': 'Tenant de test pour CRM'
            }
        )
        
        # Initialiser le service CRM
        crm_service = CRMService(tenant)
        print("✅ Initialisation du service CRM réussie")
        
        # Test de génération du dashboard
        dashboard_data = crm_service.get_crm_dashboard()
        print("✅ Génération du dashboard CRM réussie")
        print(f"   - Contacts: {dashboard_data['contacts']['total']}")
        print(f"   - Opportunités: {dashboard_data['opportunities']['total']}")
        print(f"   - Campagnes: {dashboard_data['campaigns']['total']}")
        print(f"   - Tickets: {dashboard_data['support']['total_tickets']}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des services: {e}")
        return False

def test_crm_serializers():
    """Test des serializers CRM"""
    try:
        from agents.crm.serializers import (
            ContactSerializer, OpportunitySerializer, CampaignSerializer,
            SupportTicketSerializer, InteractionSerializer
        )
        print("✅ Import des serializers CRM réussi")
        
        # Test de validation des serializers
        contact_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'contact_type': 'prospect',
            'email': '<EMAIL>'
        }
        
        from agents.crm.serializers import ContactCreateSerializer
        serializer = ContactCreateSerializer(data=contact_data)
        if serializer.is_valid():
            print("✅ Validation du serializer de contact réussie")
        else:
            print(f"❌ Erreurs de validation: {serializer.errors}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des serializers: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test de l'Agent CRM")
    print("=" * 50)
    
    tests = [
        ("Modèles CRM", test_crm_models),
        ("Services CRM", test_crm_services),
        ("Serializers CRM", test_crm_serializers),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Résultats des tests:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Tous les tests sont passés ! L'Agent CRM est prêt.")
    else:
        print("\n⚠️  Certains tests ont échoué. Vérifiez la configuration.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
