<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ressources Humaines - ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f1f5f9;
            color: #1e293b;
            line-height: 1.6;
        }

        .main-content {
            padding: 2rem;
            max-width: 1600px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .header-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header-left p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }

        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .btn-outline:hover {
            background: rgba(255,255,255,0.1);
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, #3b82f6);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stat-icon {
            padding: 0.75rem;
            border-radius: 0.75rem;
            background: var(--bg-color, rgba(59, 130, 246, 0.1));
            color: var(--accent-color, #3b82f6);
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-trend {
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }

        .trend-up {
            color: #059669;
        }

        .trend-down {
            color: #dc2626;
        }

        .tabs-container {
            margin-bottom: 2rem;
        }

        .tabs-nav {
            display: flex;
            background: white;
            border-radius: 0.75rem;
            padding: 0.25rem;
            border: 1px solid #e2e8f0;
            margin-bottom: 1rem;
            overflow-x: auto;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            color: #64748b;
            cursor: pointer;
            border-radius: 0.5rem;
            transition: all 0.3s;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .tab-btn:hover {
            background: #f1f5f9;
            color: #3b82f6;
        }

        .tab-btn.active {
            background: #3b82f6;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-actions {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .section-content {
            padding: 1.5rem;
        }

        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            min-width: 120px;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            line-height: 1;
            cursor: pointer;
            transition: all 0.3s;
        }

        .badge:hover {
            transform: scale(1.05);
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background: white;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.3s;
        }

        .close-btn:hover {
            background: #f1f5f9;
            color: #ef4444;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }

        .alert-info {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quick-action-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .quick-action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #3b82f6;
        }

        .quick-action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #3b82f6;
        }

        .employee-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .employee-photo:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .notification-badge {
            position: relative;
        }

        .notification-badge::after {
            content: attr(data-count);
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .context-menu {
            display: none;
            position: absolute;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 150px;
        }

        .context-menu.show {
            display: block;
        }

        .context-menu-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #374151;
            font-size: 0.875rem;
        }

        .context-menu-item:hover {
            background: #f3f4f6;
        }

        .context-menu-item:first-child {
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .context-menu-item:last-child {
            border-radius: 0 0 0.5rem 0.5rem;
        }

        .progress-container {
            background: #f1f5f9;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s;
            border-radius: 4px;
        }

        .progress-text {
            font-size: 0.875rem;
            color: #64748b;
            display: flex;
            justify-content: space-between;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .search-filters {
                flex-direction: column;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>

    <main class="main-content">
        <!-- Header -->
        <div class="page-header">
            <div class="header-left">
                <h1>🏢 Ressources Humaines</h1>
                <p>Gestion complète des employés, congés, paie et évaluations</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline" onclick="exportData()">
                    <span class="material-icons">download</span>
                    Exporter
                </button>
                <button class="btn btn-success" onclick="openAddEmployeeModal()">
                    <span class="material-icons">person_add</span>
                    Nouvel Employé
                </button>
            </div>
        </div>

        <!-- Statistiques Dashboard -->
        <div class="stats-grid">
            <div class="stat-card" style="--accent-color: #3b82f6; --bg-color: rgba(59, 130, 246, 0.1);" onclick="showEmployeeDetails()">
                <div class="stat-header">
                    <div>
                        <div class="stat-value" id="totalEmployees">127</div>
                        <div class="stat-label">Total Employés</div>
                        <div class="stat-trend trend-up">↗ +5 ce mois</div>
                    </div>
                    <div class="stat-icon">
                        <span class="material-icons">people</span>
                    </div>
                </div>
            </div>

            <div class="stat-card" style="--accent-color: #10b981; --bg-color: rgba(16, 185, 129, 0.1);" onclick="showActiveEmployees()">
                <div class="stat-header">
                    <div>
                        <div class="stat-value" id="activeEmployees">119</div>
                        <div class="stat-label">Employés Actifs</div>
                        <div class="stat-trend trend-up">↗ 93.7%</div>
                    </div>
                    <div class="stat-icon">
                        <span class="material-icons">work</span>
                    </div>
                </div>
            </div>

            <div class="stat-card notification-badge" data-count="12" style="--accent-color: #f59e0b; --bg-color: rgba(245, 158, 11, 0.1);" onclick="showPendingLeaves()">
                <div class="stat-header">
                    <div>
                        <div class="stat-value" id="pendingLeaves">12</div>
                        <div class="stat-label">Congés en Attente</div>
                        <div class="stat-trend trend-down">↘ -3 cette semaine</div>
                    </div>
                    <div class="stat-icon">
                        <span class="material-icons">pending</span>
                    </div>
                </div>
            </div>

            <div class="stat-card" style="--accent-color: #8b5cf6; --bg-color: rgba(139, 92, 246, 0.1);" onclick="showPayrollSummary()">
                <div class="stat-header">
                    <div>
                        <div class="stat-value">€2.1M</div>
                        <div class="stat-label">Masse Salariale</div>
                        <div class="stat-trend trend-up">↗ +2.1% annuel</div>
                    </div>
                    <div class="stat-icon">
                        <span class="material-icons">euro</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Rapides -->
        <div class="quick-actions">
            <div class="quick-action-card" onclick="openAddEmployeeModal()">
                <div class="quick-action-icon">👤</div>
                <h3>Ajouter Employé</h3>
                <p>Recruter un nouveau collaborateur</p>
            </div>
            <div class="quick-action-card" onclick="showTab('leaves'); showPendingLeaves()">
                <div class="quick-action-icon">🏖️</div>
                <h3>Demande de Congé</h3>
                <p>Traiter les demandes de congés</p>
            </div>
            <div class="quick-action-card" onclick="showTab('payroll'); startPayrollProcess()">
                <div class="quick-action-icon">💰</div>
                <h3>Paie du Mois</h3>
                <p>Lancer le processus de paie</p>
            </div>
            <div class="quick-action-card" onclick="showTab('evaluations'); scheduleEvaluations()">
                <div class="quick-action-icon">📊</div>
                <h3>Évaluations</h3>
                <p>Programmer les entretiens annuels</p>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Onglets -->
        <div class="tabs-container">
            <div class="tabs-nav">
                <button class="tab-btn active" onclick="showTab('employees')">
                    <span class="material-icons">people</span>
                    Employés
                </button>
                <button class="tab-btn" onclick="showTab('leaves')">
                    <span class="material-icons">beach_access</span>
                    Congés
                </button>
                <button class="tab-btn" onclick="showTab('payroll')">
                    <span class="material-icons">account_balance_wallet</span>
                    Paie
                </button>
                <button class="tab-btn" onclick="showTab('evaluations')">
                    <span class="material-icons">assessment</span>
                    Évaluations
                </button>
                <button class="tab-btn" onclick="showTab('recruitment')">
                    <span class="material-icons">person_search</span>
                    Recrutement
                </button>
                <button class="tab-btn" onclick="showTab('training')">
                    <span class="material-icons">school</span>
                    Formation
                </button>
                <button class="tab-btn" onclick="showTab('relations')">
                    <span class="material-icons">hub</span>
                    Relations
                </button>
            </div>

            <!-- Onglet Employés -->
            <div id="employees" class="tab-content active">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="material-icons">people</span>
                            Gestion des Employés
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-primary btn-sm" onclick="toggleBulkActions()">
                                <span class="material-icons">checklist</span>
                                Actions en lot
                            </button>
                            <button class="btn btn-success btn-sm" onclick="openAddEmployeeModal()">
                                <span class="material-icons">person_add</span>
                                Ajouter
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="search-filters">
                            <input type="text" class="search-input" placeholder="🔍 Rechercher un employé..." id="employeeSearch" oninput="filterEmployees()">
                            <select class="filter-select" id="departmentFilter" onchange="filterEmployees()">
                                <option value="">Tous les départements</option>
                                <option value="IT">Informatique</option>
                                <option value="RH">Ressources Humaines</option>
                                <option value="Finance">Finance</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Ventes">Ventes</option>
                            </select>
                            <select class="filter-select" id="statusFilter" onchange="filterEmployees()">
                                <option value="">Tous les statuts</option>
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                                <option value="onleave">En congé</option>
                            </select>
                        </div>
                        
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                        <th>Photo</th>
                                        <th>Employé</th>
                                        <th>Poste</th>
                                        <th>Département</th>
                                        <th>Embauche</th>
                                        <th>Salaire</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="employeesTableBody">
                                    <tr data-employee-id="1">
                                        <td><input type="checkbox" class="employee-checkbox" onchange="updateBulkActions()"></td>
                                        <td><div class="employee-photo" style="background: #3b82f6;" onclick="showEmployeeProfile(1)">JD</div></td>
                                        <td onclick="showEmployeeDetails(1)" style="cursor: pointer;">
                                            <div>
                                                <strong>Jean Dupont</strong><br>
                                                <small style="color: #64748b;"><EMAIL></small>
                                            </div>
                                        </td>
                                        <td><span class="badge badge-primary">Développeur Senior</span></td>
                                        <td><span class="badge badge-info">IT</span></td>
                                        <td>15/03/2020</td>
                                        <td>€65,000</td>
                                        <td><span class="status-badge status-active">Actif</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-icon" onclick="showEmployeeDetails(1)" title="Voir détails">
                                                    <span class="material-icons">visibility</span>
                                                </button>
                                                <button class="btn-icon" onclick="editEmployee(1)" title="Modifier">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="btn-icon btn-danger" onclick="deleteEmployee(1)" title="Supprimer">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Autres employés seront chargés dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Relations Inter-modules -->
            <div id="relations" class="tab-content">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="material-icons">hub</span>
                            Relations Inter-modules
                        </h2>
                    </div>
                    <div class="section-content">
                        <div class="relations-grid">
                            <!-- Relation avec les Ventes -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">trending_up</span>
                                    <h3>Équipe Commerciale</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="salesTeamCount">12</span>
                                        <span class="stat-label">Commerciaux</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="salesPerformance">€2.3M</span>
                                        <span class="stat-label">CA généré</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewSalesTeam()">
                                    Voir l'équipe
                                </button>
                            </div>

                            <!-- Relation avec les Achats -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">shopping_cart</span>
                                    <h3>Équipe Achats</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="purchaseTeamCount">5</span>
                                        <span class="stat-label">Acheteurs</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="purchaseVolume">€1.8M</span>
                                        <span class="stat-label">Volume achats</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewPurchaseTeam()">
                                    Voir l'équipe
                                </button>
                            </div>

                            <!-- Relation avec la Logistique -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">local_shipping</span>
                                    <h3>Équipe Logistique</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="logisticsTeamCount">8</span>
                                        <span class="stat-label">Logisticiens</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="warehouseManagers">3</span>
                                        <span class="stat-label">Responsables entrepôts</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewLogisticsTeam()">
                                    Voir l'équipe
                                </button>
                            </div>

                            <!-- Relation avec le Stock -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">inventory</span>
                                    <h3>Gestionnaires Stock</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="stockTeamCount">6</span>
                                        <span class="stat-label">Gestionnaires</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="inventoryValue">€850K</span>
                                        <span class="stat-label">Valeur stock</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewStockTeam()">
                                    Voir l'équipe
                                </button>
                            </div>

                            <!-- Relation avec le CRM -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">people</span>
                                    <h3>Équipe CRM</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="crmTeamCount">4</span>
                                        <span class="stat-label">Chargés relation client</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="customerSatisfaction">94%</span>
                                        <span class="stat-label">Satisfaction client</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewCRMTeam()">
                                    Voir l'équipe
                                </button>
                            </div>

                            <!-- Relation avec la Finance -->
                            <div class="relation-card">
                                <div class="relation-header">
                                    <span class="material-icons">account_balance</span>
                                    <h3>Équipe Finance</h3>
                                </div>
                                <div class="relation-stats">
                                    <div class="stat-item">
                                        <span class="stat-number" id="financeTeamCount">7</span>
                                        <span class="stat-label">Financiers</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number" id="budgetManaged">€5.2M</span>
                                        <span class="stat-label">Budget géré</span>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="viewFinanceTeam()">
                                    Voir l'équipe
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Variables globales
        let employees = [];
        let filteredEmployees = [];
        let currentTab = 'employees';

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployees();
            loadRelationsData();
            showAlert('info', 'Module RH chargé avec succès !');
        });

        // Fonction pour charger les employés
        async function loadEmployees() {
            try {
                const response = await fetch('/api/employees');
                if (response.ok) {
                    employees = await response.json();
                    filteredEmployees = [...employees];
                    displayEmployees();
                    updateStats();
                } else {
                    // Données de démonstration si l'API n'est pas disponible
                    employees = getDemoEmployees();
                    filteredEmployees = [...employees];
                    displayEmployees();
                    updateStats();
                }
            } catch (error) {
                console.error('Erreur lors du chargement des employés:', error);
                // Utiliser les données de démonstration
                employees = getDemoEmployees();
                filteredEmployees = [...employees];
                displayEmployees();
                updateStats();
            }
        }

        // Données de démonstration
        function getDemoEmployees() {
            return [
                {
                    id: '1',
                    first_name: 'Jean',
                    last_name: 'Dupont',
                    email: '<EMAIL>',
                    position: 'Développeur Senior',
                    department: 'IT',
                    hire_date: '2020-03-15',
                    salary: 65000,
                    status: 'active'
                },
                {
                    id: '2',
                    first_name: 'Marie',
                    last_name: 'Martin',
                    email: '<EMAIL>',
                    position: 'Responsable Commercial',
                    department: 'Ventes',
                    hire_date: '2019-01-10',
                    salary: 70000,
                    status: 'active'
                },
                {
                    id: '3',
                    first_name: 'Pierre',
                    last_name: 'Durand',
                    email: '<EMAIL>',
                    position: 'Acheteur Senior',
                    department: 'Achats',
                    hire_date: '2021-06-01',
                    salary: 55000,
                    status: 'active'
                }
            ];
        }

        // Fonction pour afficher les employés
        function displayEmployees() {
            const tbody = document.getElementById('employeesTableBody');
            tbody.innerHTML = '';

            filteredEmployees.forEach(employee => {
                const row = document.createElement('tr');
                row.setAttribute('data-employee-id', employee.id);

                row.innerHTML = `
                    <td><input type="checkbox" class="employee-checkbox" onchange="updateBulkActions()"></td>
                    <td><div class="employee-photo" style="background: #3b82f6;" onclick="showEmployeeProfile('${employee.id}')">${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}</div></td>
                    <td onclick="showEmployeeDetails('${employee.id}')" style="cursor: pointer;">
                        <div>
                            <strong>${employee.first_name} ${employee.last_name}</strong><br>
                            <small style="color: #64748b;">${employee.email}</small>
                        </div>
                    </td>
                    <td><span class="badge badge-primary">${employee.position}</span></td>
                    <td><span class="badge badge-info">${employee.department}</span></td>
                    <td>${new Date(employee.hire_date).toLocaleDateString('fr-FR')}</td>
                    <td>€${employee.salary.toLocaleString('fr-FR')}</td>
                    <td><span class="status-badge status-${employee.status}">${employee.status === 'active' ? 'Actif' : 'Inactif'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon" onclick="showEmployeeDetails('${employee.id}')" title="Voir détails">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="btn-icon" onclick="editEmployee('${employee.id}')" title="Modifier">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="btn-icon btn-danger" onclick="deleteEmployee('${employee.id}')" title="Supprimer">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Fonction pour charger les données des relations
        async function loadRelationsData() {
            try {
                // Charger les données des équipes par département
                const salesTeam = await fetch('/api/employees?department=Ventes');
                const purchaseTeam = await fetch('/api/employees?department=Achats');
                const logisticsTeam = await fetch('/api/employees?department=Logistique');
                const stockTeam = await fetch('/api/employees?department=Stock');
                const crmTeam = await fetch('/api/employees?department=CRM');
                const financeTeam = await fetch('/api/employees?department=Finance');

                // Mettre à jour les compteurs (utiliser des données de démonstration si l'API n'est pas disponible)
                updateRelationStats();
            } catch (error) {
                console.error('Erreur lors du chargement des relations:', error);
                updateRelationStats();
            }
        }

        // Mettre à jour les statistiques des relations
        function updateRelationStats() {
            // Données de démonstration pour les relations
            document.getElementById('salesTeamCount').textContent = '12';
            document.getElementById('salesPerformance').textContent = '€2.3M';
            document.getElementById('purchaseTeamCount').textContent = '5';
            document.getElementById('purchaseVolume').textContent = '€1.8M';
            document.getElementById('logisticsTeamCount').textContent = '8';
            document.getElementById('warehouseManagers').textContent = '3';
            document.getElementById('stockTeamCount').textContent = '6';
            document.getElementById('inventoryValue').textContent = '€850K';
            document.getElementById('crmTeamCount').textContent = '4';
            document.getElementById('customerSatisfaction').textContent = '94%';
            document.getElementById('financeTeamCount').textContent = '7';
            document.getElementById('budgetManaged').textContent = '€5.2M';
        }

        // Fonctions de navigation vers les autres modules
        function viewSalesTeam() {
            window.location.href = 'sales-management-postgresql.html';
        }

        function viewPurchaseTeam() {
            window.location.href = 'purchase-management-postgresql.html';
        }

        function viewLogisticsTeam() {
            window.location.href = 'logistics-management-postgresql.html';
        }

        function viewStockTeam() {
            window.location.href = 'stock-management-postgresql.html';
        }

        function viewCRMTeam() {
            window.location.href = 'crm-management-postgresql.html';
        }

        function viewFinanceTeam() {
            window.location.href = 'finance-management.html';
        }

        // Fonction pour afficher les alertes
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <span class="material-icons">info</span>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2em; cursor: pointer;">&times;</button>
            `;
            alertContainer.appendChild(alert);

            // Supprimer automatiquement après 5 secondes
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 5000);
        }

        // Autres fonctions utilitaires
        function updateStats() {
            const totalEmployees = employees.length;
            const activeEmployees = employees.filter(emp => emp.status === 'active').length;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('activeEmployees').textContent = activeEmployees;
        }

        function showTab(tabName) {
            // Masquer tous les onglets
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Désactiver tous les boutons d'onglet
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Afficher l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');

            // Activer le bouton correspondant
            event.target.classList.add('active');

            currentTab = tabName;
        }

        // Fonctions pour les actions sur les employés
        function showEmployeeDetails(employeeId) {
            showAlert('info', `Affichage des détails de l'employé ${employeeId}`);
        }

        function editEmployee(employeeId) {
            showAlert('info', `Modification de l'employé ${employeeId}`);
        }

        function deleteEmployee(employeeId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet employé ?')) {
                showAlert('success', `Employé ${employeeId} supprimé avec succès`);
            }
        }

        function filterEmployees() {
            const searchTerm = document.getElementById('employeeSearch').value.toLowerCase();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredEmployees = employees.filter(employee => {
                const matchesSearch = employee.first_name.toLowerCase().includes(searchTerm) ||
                                    employee.last_name.toLowerCase().includes(searchTerm) ||
                                    employee.email.toLowerCase().includes(searchTerm);
                const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
                const matchesStatus = !statusFilter || employee.status === statusFilter;

                return matchesSearch && matchesDepartment && matchesStatus;
            });

            displayEmployees();
        }
    </script>

    <style>
        /* Styles supplémentaires pour les nouvelles fonctionnalités */
        .relations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .relation-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .relation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .relation-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .relation-header .material-icons {
            font-size: 2rem;
            color: #3b82f6;
        }

        .relation-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }

        .relation-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-primary {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-info {
            background: #e0f2fe;
            color: #0277bd;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fef2f2;
            color: #dc2626;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-icon {
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-icon:hover {
            background: #f1f5f9;
        }

        .btn-icon.btn-danger:hover {
            background: #fef2f2;
            color: #dc2626;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-info {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }

        .alert-success {
            background: #dcfce7;
            border: 1px solid #86efac;
            color: #166534;
        }

        .employee-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }

        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .search-input, .filter-select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
        }

        .filter-select {
            min-width: 150px;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }

        /* Ajout de l'onglet Relations dans la navigation */
        .tabs-nav {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #e5e7eb;
            overflow-x: auto;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            color: #64748b;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
        }

        .tab-btn:hover {
            color: #3b82f6;
        }

        .tab-btn.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</body>
</html>