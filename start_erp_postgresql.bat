@echo off
chcp 65001 >nul
title ERP HUB PostgreSQL - Démarrage Automatique

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 ERP HUB POSTGRESQL                     ║
echo ║                   Démarrage Automatique                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Vérification des prérequis
echo 🔍 Vérification des prérequis...
echo.

REM Vérifier si Docker est installé
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker n'est pas installé ou n'est pas dans le PATH
    echo 💡 Veuillez installer Docker Desktop depuis https://docker.com
    echo.
    pause
    exit /b 1
)
echo ✅ Docker détecté

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou n'est pas dans le PATH
    echo 💡 Veuillez installer Python depuis https://python.org
    echo.
    pause
    exit /b 1
)
echo ✅ Python détecté

REM Vérifier les fichiers essentiels
if not exist "postgresql_api_server.py" (
    echo ❌ Fichier postgresql_api_server.py manquant
    echo 💡 Assurez-vous d'être dans le bon répertoire
    echo.
    pause
    exit /b 1
)
echo ✅ Fichiers API détectés

if not exist "docker-compose-postgresql.yml" (
    echo ❌ Fichier docker-compose-postgresql.yml manquant
    echo.
    pause
    exit /b 1
)
echo ✅ Configuration Docker détectée

echo.
echo ═══════════════════════════════════════════════════════════════
echo 📊 ÉTAPE 1 : Démarrage de PostgreSQL
echo ═══════════════════════════════════════════════════════════════

REM Arrêter les anciens conteneurs si ils existent
echo 🧹 Nettoyage des anciens conteneurs...
docker-compose -f docker-compose-postgresql.yml down >nul 2>&1

REM Démarrer PostgreSQL avec Docker Compose
echo 🐳 Démarrage de PostgreSQL avec Docker...
docker-compose -f docker-compose-postgresql.yml up -d

if errorlevel 1 (
    echo ❌ Erreur lors du démarrage de PostgreSQL
    echo 💡 Vérifiez que Docker Desktop est démarré
    echo.
    pause
    exit /b 1
)

echo ✅ PostgreSQL démarré avec succès
echo.

echo ⏳ Attente de l'initialisation de PostgreSQL...
echo    (Cela peut prendre 30-60 secondes la première fois)

REM Attendre que PostgreSQL soit prêt avec vérification
set /a counter=0
:wait_postgres
timeout /t 5 /nobreak >nul
docker exec erp_postgres pg_isready -U erp_admin >nul 2>&1
if errorlevel 1 (
    set /a counter+=1
    if %counter% lss 12 (
        echo    ⏳ Attente... (%counter%/12)
        goto wait_postgres
    ) else (
        echo ❌ PostgreSQL ne répond pas après 60 secondes
        echo 💡 Vérifiez les logs : docker logs erp_postgres
        pause
        exit /b 1
    )
)

echo ✅ PostgreSQL est prêt !

echo.

echo ═══════════════════════════════════════════════════════════════
echo 🧪 ÉTAPE 2 : Test de la base de données
echo ═══════════════════════════════════════════════════════════════

REM Tester la connexion à la base de données
echo 🔍 Test de connexion à la base de données...
docker exec erp_postgres psql -U erp_admin -d erp_hub -c "SELECT COUNT(*) FROM employees;" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Tables non trouvées, création en cours...

    REM Créer les tables si elles n'existent pas
    if exist "create_all_erp_tables.sql" (
        echo 📊 Création des tables...
        docker exec -i erp_postgres psql -U erp_admin -d erp_hub < create_all_erp_tables.sql >nul 2>&1
    )

    REM Insérer les données de démonstration
    if exist "insert_demo_data_all_agents.sql" (
        echo 📋 Insertion des données de démonstration...
        docker exec -i erp_postgres psql -U erp_admin -d erp_hub < insert_demo_data_all_agents.sql >nul 2>&1
    )
)

echo ✅ Base de données opérationnelle
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🌐 ÉTAPE 3 : Démarrage de l'API ERP
echo ═══════════════════════════════════════════════════════════════

REM Vérifier les dépendances Python
echo 🐍 Vérification des dépendances Python...
python -c "import flask, psycopg2, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Installation des dépendances manquantes...
    pip install flask flask-cors psycopg2-binary requests python-dotenv >nul 2>&1
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        echo 💡 Exécutez manuellement : pip install flask flask-cors psycopg2-binary requests
        pause
        exit /b 1
    )
)
echo ✅ Dépendances Python OK

echo.
echo 🚀 Démarrage de l'API ERP...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 SYSTÈME PRÊT !                      ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  📱 Ouvrez votre navigateur et accédez à :                  ║
echo ║                                                              ║
echo ║  🏠 Dashboard Global :                                       ║
echo ║     file:///%CD%/frontend/dashboard-global-postgresql.html  ║
echo ║                                                              ║
echo ║  📊 Modules disponibles :                                    ║
echo ║     • HR (Ressources Humaines)                              ║
echo ║     • Sales (Ventes)                                        ║
echo ║     • Purchase (Achats)                                     ║
echo ║     • Stock (Inventaire)                                    ║
echo ║     • Logistics (Logistique)                                ║
echo ║     • CRM (Relation Client)                                 ║
echo ║     • BI (Business Intelligence)                            ║
echo ║                                                              ║
echo ║  🛑 Pour arrêter : Ctrl+C puis docker-compose down          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Démarrer l'API Flask
python postgresql_api_server.py

echo.
echo 🛑 API arrêtée. Arrêt de PostgreSQL...
docker-compose -f docker-compose-postgresql.yml down >nul 2>&1
echo ✅ Système arrêté proprement.
echo.
pause
