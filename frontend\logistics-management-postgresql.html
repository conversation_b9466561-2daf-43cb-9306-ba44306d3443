<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Logistics - Logistique | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #06b6d4 30%, #0891b2 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #06b6d4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0891b2;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #06b6d4;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }

        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #06b6d4;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #06b6d4;
            background: #ecfeff;
        }

        .nav-tab.active {
            color: #06b6d4;
            border-bottom-color: #06b6d4;
            background: #ecfeff;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .utilization-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .utilization-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s;
        }

        .utilization-low {
            background: #10b981;
        }

        .utilization-medium {
            background: #f59e0b;
        }

        .utilization-high {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>
    <header class="header">
        <div class="logo">🚚 Agent Logistics - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion Logistique</h1>
            <p class="page-subtitle">Entrepôts et expéditions - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalWarehouses">0</div>
                <div class="stat-label">Total Entrepôts</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalShipments">0</div>
                <div class="stat-label">Expéditions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="averageUtilization">0%</div>
                <div class="stat-label">Utilisation Moyenne</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="shipmentsInTransit">0</div>
                <div class="stat-label">En Transit</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('warehouses')">
                <span class="material-icons">warehouse</span>
                Entrepôts
            </button>
            <button class="nav-tab" onclick="showTab('shipments')">
                <span class="material-icons">local_shipping</span>
                Expéditions
            </button>
        </nav>

        <!-- Onglet Entrepôts -->
        <div id="warehouses" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">🏭 Entrepôts</h2>
                    <button class="btn btn-primary" onclick="loadWarehouses()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Code Entrepôt</th>
                                    <th>Nom</th>
                                    <th>Ville</th>
                                    <th>Type</th>
                                    <th>Capacité</th>
                                    <th>Utilisation</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="warehousesTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des entrepôts...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Expéditions -->
        <div id="shipments" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">📦 Expéditions</h2>
                    <button class="btn btn-primary" onclick="loadShipments()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>N° Expédition</th>
                                    <th>Entrepôt</th>
                                    <th>Transporteur</th>
                                    <th>N° Suivi</th>
                                    <th>Date Expédition</th>
                                    <th>Date Livraison Prévue</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="shipmentsTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des expéditions...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let warehouses = [];
        let shipments = [];
        let currentTab = 'warehouses';

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadWarehouses();
            await loadShipments();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les entrepôts depuis PostgreSQL
        async function loadWarehouses() {
            try {
                showAlert('Chargement des entrepôts depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/warehouses`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        warehouses = data.data || [];
                        renderWarehousesTable();
                        showAlert(`${warehouses.length} entrepôts chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement entrepôts:', error);
                showAlert('Erreur lors du chargement des entrepôts: ' + error.message, 'error');
                warehouses = [];
                renderWarehousesTable();
            }
        }

        // Charger les expéditions depuis PostgreSQL
        async function loadShipments() {
            try {
                const response = await fetch(`${API_BASE_URL}/shipments`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        shipments = data.data || [];
                        renderShipmentsTable();
                        console.log(`${shipments.length} expéditions chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement expéditions:', error);
                showAlert('Erreur lors du chargement des expéditions: ' + error.message, 'error');
                shipments = [];
                renderShipmentsTable();
            }
        }

        // Afficher le tableau des entrepôts
        function renderWarehousesTable() {
            const tbody = document.getElementById('warehousesTableBody');
            
            if (warehouses.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun entrepôt trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = warehouses.map(warehouse => `
                <tr>
                    <td><strong>${warehouse.warehouseCode}</strong></td>
                    <td>${warehouse.name}</td>
                    <td>${warehouse.city || 'N/A'}</td>
                    <td>${getWarehouseTypeBadge(warehouse.warehouseType)}</td>
                    <td>${warehouse.capacity ? warehouse.capacity.toLocaleString() + ' m²' : 'N/A'}</td>
                    <td>${getUtilizationDisplay(warehouse.currentUtilization)}</td>
                    <td>${getStatusBadge(warehouse.status)}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des expéditions
        function renderShipmentsTable() {
            const tbody = document.getElementById('shipmentsTableBody');
            
            if (shipments.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune expédition trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = shipments.map(shipment => `
                <tr>
                    <td><strong>${shipment.shipmentNumber}</strong></td>
                    <td>${shipment.warehouseName}</td>
                    <td>${shipment.carrier || 'N/A'}</td>
                    <td>${shipment.trackingNumber || 'N/A'}</td>
                    <td>${formatDate(shipment.shipDate)}</td>
                    <td>${formatDate(shipment.expectedDeliveryDate)}</td>
                    <td>${getShipmentStatusBadge(shipment.status)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalWarehouses = warehouses.length;
            const totalShipments = shipments.length;
            const averageUtilization = warehouses.length > 0 
                ? warehouses.reduce((sum, w) => sum + (w.currentUtilization || 0), 0) / warehouses.length 
                : 0;
            const shipmentsInTransit = shipments.filter(s => s.status === 'in_transit').length;

            document.getElementById('totalWarehouses').textContent = totalWarehouses;
            document.getElementById('totalShipments').textContent = totalShipments;
            document.getElementById('averageUtilization').textContent = averageUtilization.toFixed(1) + '%';
            document.getElementById('shipmentsInTransit').textContent = shipmentsInTransit;
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');
            
            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // Fonctions utilitaires
        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="badge badge-success">Actif</span>',
                'inactive': '<span class="badge badge-warning">Inactif</span>',
                'maintenance': '<span class="badge badge-danger">Maintenance</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getWarehouseTypeBadge(type) {
            const badges = {
                'main': '<span class="badge badge-info">Principal</span>',
                'regional': '<span class="badge badge-success">Régional</span>',
                'distribution': '<span class="badge badge-warning">Distribution</span>',
                'transit': '<span class="badge badge-info">Transit</span>'
            };
            return badges[type] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getShipmentStatusBadge(status) {
            const badges = {
                'preparing': '<span class="badge badge-warning">Préparation</span>',
                'shipped': '<span class="badge badge-info">Expédié</span>',
                'in_transit': '<span class="badge badge-warning">En transit</span>',
                'delivered': '<span class="badge badge-success">Livré</span>',
                'returned': '<span class="badge badge-danger">Retourné</span>',
                'lost': '<span class="badge badge-danger">Perdu</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function getUtilizationDisplay(utilization) {
            if (!utilization) return 'N/A';
            
            let barClass;
            if (utilization < 50) {
                barClass = 'utilization-low';
            } else if (utilization < 80) {
                barClass = 'utilization-medium';
            } else {
                barClass = 'utilization-high';
            }

            return `
                <div>
                    <span>${utilization.toFixed(1)}%</span>
                    <div class="utilization-bar">
                        <div class="utilization-fill ${barClass}" style="width: ${utilization}%"></div>
                    </div>
                </div>
            `;
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
