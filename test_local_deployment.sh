#!/bin/bash

# Test du déploiement local ERP HUB
echo "🧪 TEST DU DÉPLOIEMENT LOCAL ERP HUB"
echo "===================================="

# Fonction de test d'URL
test_url() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo -n "🔍 Test $name ($url)... "
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ OK"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ ÉCHEC (après ${max_attempts} tentatives)"
    return 1
}

# Vérifier que Docker Compose est en cours
echo "📊 État des services Docker:"
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "🔍 Tests de connectivité:"

# Tests des services
test_url "http://localhost:8000/api/health/" "Backend API"
test_url "http://localhost:3000" "Frontend React"
test_url "http://localhost:80" "Nginx Proxy"
test_url "http://localhost:9090" "Prometheus"
test_url "http://localhost:3001" "Grafana"

echo ""
echo "📋 Résumé des services:"
echo "   🌐 Frontend:     http://localhost:3000"
echo "   🔧 Backend API:  http://localhost:8000/api"
echo "   👑 Admin:        http://localhost:8000/admin"
echo "   📚 Docs API:     http://localhost:8000/docs"
echo "   📊 Grafana:      http://localhost:3001 (admin/admin)"
echo "   📈 Prometheus:   http://localhost:9090"
echo ""
echo "🎉 Test terminé!"
