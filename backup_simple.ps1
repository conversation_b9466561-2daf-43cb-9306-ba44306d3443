# Script de sauvegarde ERP HUB
Write-Host "Demarrage de la sauvegarde ERP HUB..." -ForegroundColor Green

# Creer le dossier de sauvegarde
if (!(Test-Path "backups")) {
    New-Item -ItemType Directory -Path "backups" | Out-Null
    Write-Host "Dossier backups cree" -ForegroundColor Yellow
}

# Generer un nom de fichier avec la date
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupFile = "backups\erp_hub_backup_$timestamp.sql"

Write-Host "Sauvegarde en cours vers : $backupFile" -ForegroundColor Cyan

try {
    # Executer la sauvegarde
    docker exec erp_postgres pg_dump -U erp_admin -d erp_hub | Out-File -FilePath $backupFile -Encoding UTF8
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Sauvegarde reussie !" -ForegroundColor Green
        Write-Host "Fichier cree : $backupFile" -ForegroundColor White
        
        # Taille du fichier
        $fileInfo = Get-Item $backupFile
        $sizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
        Write-Host "Taille : $sizeKB KB" -ForegroundColor White
        
    } else {
        throw "Erreur lors de la sauvegarde"
    }
    
} catch {
    Write-Host "Erreur : $_" -ForegroundColor Red
    Write-Host "Verifiez que PostgreSQL fonctionne avec : docker ps" -ForegroundColor Yellow
}

Write-Host "Sauvegarde terminee !" -ForegroundColor Green
