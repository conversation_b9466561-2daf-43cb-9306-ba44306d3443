# 🧪 TESTS DE SÉCURITÉ ERP HUB
# Tests unitaires et d'intégration pour la sécurité

import unittest
import requests
import json
import time
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from secure_api_server import app
from redis_cache import cache

class SecurityTestCase(unittest.TestCase):
    """Tests de sécurité pour l'API ERP HUB"""
    
    @classmethod
    def setUpClass(cls):
        """Configuration initiale des tests"""
        cls.app = app.test_client()
        cls.app.testing = True
        cls.base_url = 'http://localhost:5000/api'
        
        # Données de test
        cls.test_user = {
            'username': 'test_user',
            'password': 'TestPassword123!'
        }
        
        cls.admin_user = {
            'username': 'admin',
            'password': 'Admin123!'
        }
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.session = requests.Session()
    
    def tearDown(self):
        """Nettoyage après chaque test"""
        self.session.close()
    
    # ===== TESTS D'AUTHENTIFICATION =====
    
    def test_login_with_valid_credentials(self):
        """Test connexion avec identifiants valides"""
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=self.admin_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('user', data)
        
        # Vérifier que les cookies sont définis
        self.assertIn('access_token_cookie', response.cookies)
    
    def test_login_with_invalid_credentials(self):
        """Test connexion avec identifiants invalides"""
        invalid_user = {
            'username': 'invalid_user',
            'password': 'wrong_password'
        }
        
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=invalid_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(response.status_code, 401)
        data = response.json()
        self.assertFalse(data['success'])
    
    def test_login_without_csrf_token(self):
        """Test connexion sans token CSRF"""
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=self.admin_user
        )
        
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertFalse(data['success'])
        self.assertIn('CSRF', data['error'])
    
    def test_login_with_invalid_csrf_token(self):
        """Test connexion avec token CSRF invalide"""
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=self.admin_user,
            headers={'X-CSRF-Token': 'invalid_token'}
        )
        
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertFalse(data['success'])
    
    def test_brute_force_protection(self):
        """Test protection contre les attaques par force brute"""
        invalid_user = {
            'username': 'test_brute_force',
            'password': 'wrong_password'
        }
        
        # Faire plusieurs tentatives échouées
        for i in range(6):
            response = self.session.post(
                f'{self.base_url}/auth/secure-login',
                json=invalid_user,
                headers={'X-CSRF-Token': 'test_csrf_token'}
            )
        
        # La 6ème tentative devrait être bloquée
        self.assertEqual(response.status_code, 429)
        data = response.json()
        self.assertIn('tentatives', data['error'])
    
    # ===== TESTS DE VALIDATION DES ENTRÉES =====
    
    def test_sql_injection_protection(self):
        """Test protection contre l'injection SQL"""
        malicious_user = {
            'username': "admin'; DROP TABLE users; --",
            'password': 'password'
        }
        
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=malicious_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        # La requête ne devrait pas causer d'erreur serveur
        self.assertNotEqual(response.status_code, 500)
    
    def test_xss_protection(self):
        """Test protection contre XSS"""
        malicious_user = {
            'username': '<script>alert("xss")</script>',
            'password': 'password'
        }
        
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=malicious_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        # Les caractères dangereux devraient être filtrés
        self.assertNotEqual(response.status_code, 500)
    
    def test_input_length_validation(self):
        """Test validation de la longueur des entrées"""
        long_username = 'a' * 1000
        user_with_long_input = {
            'username': long_username,
            'password': 'password'
        }
        
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=user_with_long_input,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(response.status_code, 400)
    
    # ===== TESTS DE RATE LIMITING =====
    
    def test_rate_limiting(self):
        """Test limitation du taux de requêtes"""
        # Faire beaucoup de requêtes rapidement
        responses = []
        for i in range(10):
            response = self.session.get(f'{self.base_url}/health')
            responses.append(response.status_code)
        
        # Certaines requêtes devraient être limitées
        rate_limited = any(status == 429 for status in responses)
        # Note: Ce test peut échouer en développement si les limites sont élevées
    
    # ===== TESTS DE GESTION DES SESSIONS =====
    
    def test_session_expiration(self):
        """Test expiration des sessions"""
        # Se connecter
        login_response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=self.admin_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(login_response.status_code, 200)
        
        # Vérifier le statut immédiatement
        status_response = self.session.get(f'{self.base_url}/auth/status')
        self.assertEqual(status_response.status_code, 200)
        
        # Note: Test d'expiration réelle nécessiterait d'attendre ou de manipuler l'horloge
    
    def test_logout_clears_session(self):
        """Test que la déconnexion nettoie la session"""
        # Se connecter
        login_response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json=self.admin_user,
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(login_response.status_code, 200)
        
        # Se déconnecter
        logout_response = self.session.post(
            f'{self.base_url}/auth/secure-logout',
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        self.assertEqual(logout_response.status_code, 200)
        
        # Vérifier que la session est invalidée
        status_response = self.session.get(f'{self.base_url}/auth/status')
        data = status_response.json()
        self.assertFalse(data.get('authenticated', True))
    
    # ===== TESTS DE HEADERS DE SÉCURITÉ =====
    
    def test_security_headers(self):
        """Test présence des headers de sécurité"""
        response = self.session.get(f'{self.base_url}/health')
        
        # Vérifier les headers de sécurité
        headers = response.headers
        
        # Content Security Policy
        self.assertIn('Content-Security-Policy', headers)
        
        # X-Frame-Options
        self.assertIn('X-Frame-Options', headers)
        
        # X-Content-Type-Options
        self.assertIn('X-Content-Type-Options', headers)
    
    # ===== TESTS DE CACHE SÉCURISÉ =====
    
    def test_cache_isolation(self):
        """Test isolation du cache entre utilisateurs"""
        if not cache.connected:
            self.skipTest("Redis non disponible")
        
        # Tester que les données d'un utilisateur ne sont pas accessibles à un autre
        user1_data = {'sensitive': 'data1'}
        user2_data = {'sensitive': 'data2'}
        
        cache.cache_user_permissions('user1', user1_data)
        cache.cache_user_permissions('user2', user2_data)
        
        # Vérifier l'isolation
        retrieved_user1 = cache.get_user_permissions('user1')
        retrieved_user2 = cache.get_user_permissions('user2')
        
        self.assertEqual(retrieved_user1, user1_data)
        self.assertEqual(retrieved_user2, user2_data)
        self.assertNotEqual(retrieved_user1, retrieved_user2)
    
    # ===== TESTS DE LOGGING DE SÉCURITÉ =====
    
    def test_security_event_logging(self):
        """Test logging des événements de sécurité"""
        # Faire une tentative de connexion échouée
        response = self.session.post(
            f'{self.base_url}/auth/secure-login',
            json={'username': 'test', 'password': 'wrong'},
            headers={'X-CSRF-Token': 'test_csrf_token'}
        )
        
        # Vérifier que l'événement est loggé
        # Note: En pratique, vous vérifieriez les logs ou une base de données d'audit
        self.assertEqual(response.status_code, 401)

class PerformanceTestCase(unittest.TestCase):
    """Tests de performance pour l'API ERP HUB"""
    
    def setUp(self):
        self.session = requests.Session()
        self.base_url = 'http://localhost:5000/api'
    
    def test_response_time(self):
        """Test temps de réponse des endpoints critiques"""
        start_time = time.time()
        response = self.session.get(f'{self.base_url}/health')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Le health check devrait répondre en moins de 1 seconde
        self.assertLess(response_time, 1.0)
        self.assertEqual(response.status_code, 200)
    
    def test_concurrent_requests(self):
        """Test gestion des requêtes concurrentes"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = self.session.get(f'{self.base_url}/health')
                results.put(response.status_code)
            except Exception as e:
                results.put(str(e))
        
        # Lancer 10 requêtes concurrentes
        threads = []
        for i in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Attendre que tous les threads se terminent
        for thread in threads:
            thread.join()
        
        # Vérifier les résultats
        success_count = 0
        while not results.empty():
            result = results.get()
            if result == 200:
                success_count += 1
        
        # Au moins 80% des requêtes devraient réussir
        self.assertGreaterEqual(success_count, 8)

def run_security_tests():
    """Exécuter tous les tests de sécurité"""
    print("🧪 Lancement des tests de sécurité ERP HUB...")
    print("=" * 60)
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests de sécurité
    suite.addTests(loader.loadTestsFromTestCase(SecurityTestCase))
    suite.addTests(loader.loadTestsFromTestCase(PerformanceTestCase))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS DES TESTS DE SÉCURITÉ")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success = total_tests - failures - errors
    
    print(f"✅ Tests réussis: {success}/{total_tests}")
    print(f"❌ Échecs: {failures}")
    print(f"🔥 Erreurs: {errors}")
    
    if failures == 0 and errors == 0:
        print("🎉 TOUS LES TESTS DE SÉCURITÉ SONT PASSÉS !")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la sécurité.")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_security_tests()
    sys.exit(0 if success else 1)
