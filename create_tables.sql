-- 🗄️ CRÉATION DES TABLES POSTGRESQL POUR ERP HUB
-- Script SQL pour initialiser la base de données

-- Table des budgets
CREATE TABLE IF NOT EXISTS budgets (
    id VARCHAR(255) PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_type VARCHAR(20) NOT NULL CHECK (category_type IN ('revenue', 'expense', 'investment', 'financial', 'exceptional')),
    cost_center VARCHAR(50),
    cost_center_name VARCHAR(100),
    analytic_code VARCHAR(50),
    analytic_code_name VARCHAR(100),
    responsible VARCHAR(100),
    department VARCHAR(50),
    notes TEXT,
    forecast DECIMAL(15,2) DEFAULT 0,
    realized DECIMAL(15,2) DEFAULT 0,
    monthly_data JSONB,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des mouvements de trésorerie
CREATE TABLE IF NOT EXISTS movements (
    id VARCHAR(255) PRIMARY KEY,
    account_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('debit', 'credit')),
    category VARCHAR(50),
    reference VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des comptes bancaires
CREATE TABLE IF NOT EXISTS accounts (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    bank VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0,
    alert_threshold DECIMAL(15,2) DEFAULT 0,
    iban VARCHAR(34),
    currency VARCHAR(3) DEFAULT 'EUR',
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table de l'historique des actions
CREATE TABLE IF NOT EXISTS action_history (
    id SERIAL PRIMARY KEY,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id VARCHAR(255),
    old_data JSONB,
    new_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_budgets_category_type ON budgets(category_type);
CREATE INDEX IF NOT EXISTS idx_budgets_cost_center ON budgets(cost_center);
CREATE INDEX IF NOT EXISTS idx_budgets_created_date ON budgets(created_date);
CREATE INDEX IF NOT EXISTS idx_movements_date ON movements(date);
CREATE INDEX IF NOT EXISTS idx_movements_account ON movements(account_id);
CREATE INDEX IF NOT EXISTS idx_history_created ON action_history(created_date);

-- Insérer des données de démonstration
INSERT INTO budgets (id, category_name, category_type, cost_center, cost_center_name, analytic_code, analytic_code_name, responsible, department, forecast, realized, monthly_data) VALUES
('budget_001', 'Marketing Digital', 'expense', 'CC001', 'Marketing', 'AC001', 'Publicité', 'Jean Dupont', 'Marketing', 50000, 12500, '{"janvier": {"forecast": 4167, "realized": 4200}, "février": {"forecast": 4167, "realized": 3800}, "mars": {"forecast": 4167, "realized": 4500}}'),
('budget_002', 'Ventes Produits', 'revenue', 'CC002', 'Commercial', 'AC002', 'Ventes', 'Marie Martin', 'Commercial', 150000, 45000, '{"janvier": {"forecast": 12500, "realized": 15000}, "février": {"forecast": 12500, "realized": 14000}, "mars": {"forecast": 12500, "realized": 16000}}'),
('budget_003', 'Formation Personnel', 'expense', 'CC003', 'RH', 'AC003', 'Formation', 'Pierre Durand', 'Ressources Humaines', 25000, 8500, '{"janvier": {"forecast": 2083, "realized": 2500}, "février": {"forecast": 2083, "realized": 3000}, "mars": {"forecast": 2083, "realized": 3000}}'),
('budget_004', 'Investissement IT', 'investment', 'CC004', 'IT', 'AC004', 'Matériel', 'Sophie Leroy', 'Informatique', 75000, 25000, '{"janvier": {"forecast": 6250, "realized": 8000}, "février": {"forecast": 6250, "realized": 7000}, "mars": {"forecast": 6250, "realized": 10000}}'),
('budget_005', 'Charges Financières', 'financial', 'CC005', 'Finance', 'AC005', 'Intérêts', 'Luc Bernard', 'Finance', 15000, 3750, '{"janvier": {"forecast": 1250, "realized": 1200}, "février": {"forecast": 1250, "realized": 1300}, "mars": {"forecast": 1250, "realized": 1250}}')
ON CONFLICT (id) DO NOTHING;

-- Table des factures clients
CREATE TABLE IF NOT EXISTS invoices_clients (
    id VARCHAR(255) PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    client_id VARCHAR(255),
    client_name VARCHAR(255) NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    amount_ht DECIMAL(15,2) NOT NULL,
    amount_tva DECIMAL(15,2) DEFAULT 0,
    amount_ttc DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    payment_method VARCHAR(50),
    description TEXT,
    file_path VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des factures fournisseurs
CREATE TABLE IF NOT EXISTS invoices_suppliers (
    id VARCHAR(255) PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL,
    supplier_id VARCHAR(255),
    supplier_name VARCHAR(255) NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    amount_ht DECIMAL(15,2) NOT NULL,
    amount_tva DECIMAL(15,2) DEFAULT 0,
    amount_ttc DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'received' CHECK (status IN ('received', 'validated', 'paid', 'disputed')),
    payment_method VARCHAR(50),
    description TEXT,
    file_path VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des bons de commande
CREATE TABLE IF NOT EXISTS purchase_orders (
    id VARCHAR(255) PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id VARCHAR(255),
    supplier_name VARCHAR(255) NOT NULL,
    order_date DATE NOT NULL,
    expected_delivery DATE,
    amount_ht DECIMAL(15,2) NOT NULL,
    amount_tva DECIMAL(15,2) DEFAULT 0,
    amount_ttc DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('draft', 'sent', 'confirmed', 'delivered', 'invoiced', 'cancelled')),
    description TEXT,
    file_path VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des devis
CREATE TABLE IF NOT EXISTS quotes (
    id VARCHAR(255) PRIMARY KEY,
    quote_number VARCHAR(50) UNIQUE NOT NULL,
    client_id VARCHAR(255),
    client_name VARCHAR(255) NOT NULL,
    quote_date DATE NOT NULL,
    validity_date DATE,
    amount_ht DECIMAL(15,2) NOT NULL,
    amount_tva DECIMAL(15,2) DEFAULT 0,
    amount_ttc DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'accepted', 'rejected', 'expired')),
    description TEXT,
    file_path VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des bons de livraison
CREATE TABLE IF NOT EXISTS delivery_notes (
    id VARCHAR(255) PRIMARY KEY,
    delivery_number VARCHAR(50) UNIQUE NOT NULL,
    client_id VARCHAR(255),
    client_name VARCHAR(255) NOT NULL,
    delivery_date DATE NOT NULL,
    order_reference VARCHAR(50),
    description TEXT,
    file_path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'delivered' CHECK (status IN ('prepared', 'shipped', 'delivered', 'returned')),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les recherches documentaires
CREATE INDEX IF NOT EXISTS idx_invoices_clients_date ON invoices_clients(invoice_date);
CREATE INDEX IF NOT EXISTS idx_invoices_clients_number ON invoices_clients(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_clients_client ON invoices_clients(client_name);
CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_date ON invoices_suppliers(invoice_date);
CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_number ON invoices_suppliers(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_suppliers_supplier ON invoices_suppliers(supplier_name);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_number ON purchase_orders(order_number);
CREATE INDEX IF NOT EXISTS idx_quotes_date ON quotes(quote_date);
CREATE INDEX IF NOT EXISTS idx_quotes_number ON quotes(quote_number);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_date ON delivery_notes(delivery_date);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_number ON delivery_notes(delivery_number);

-- Insérer des données de démonstration pour les documents
INSERT INTO invoices_clients (id, invoice_number, client_name, invoice_date, due_date, amount_ht, amount_tva, amount_ttc, status, description) VALUES
('inv_client_001', 'FC-2024-001', 'TechCorp SA', '2024-01-15', '2024-02-15', 25000.00, 5000.00, 30000.00, 'paid', 'Développement application mobile'),
('inv_client_002', 'FC-2024-002', 'InnoSoft SARL', '2024-02-10', '2024-03-10', 15000.00, 3000.00, 18000.00, 'sent', 'Consultation stratégique IT'),
('inv_client_003', 'FC-2024-003', 'DataSys Ltd', '2024-03-05', '2024-04-05', 8500.00, 1700.00, 10200.00, 'pending', 'Formation équipe technique')
ON CONFLICT (id) DO NOTHING;

INSERT INTO invoices_suppliers (id, invoice_number, supplier_name, invoice_date, due_date, amount_ht, amount_tva, amount_ttc, status, description) VALUES
('inv_supplier_001', 'FS-2024-001', 'Microsoft France', '2024-01-20', '2024-02-20', 2500.00, 500.00, 3000.00, 'paid', 'Licences Office 365'),
('inv_supplier_002', 'FS-2024-002', 'Amazon Web Services', '2024-02-15', '2024-03-15', 1200.00, 240.00, 1440.00, 'validated', 'Hébergement cloud mensuel'),
('inv_supplier_003', 'FS-2024-003', 'Dell Technologies', '2024-03-01', '2024-04-01', 4500.00, 900.00, 5400.00, 'received', 'Serveurs et équipements')
ON CONFLICT (id) DO NOTHING;

INSERT INTO purchase_orders (id, order_number, supplier_name, order_date, expected_delivery, amount_ht, amount_tva, amount_ttc, status, description) VALUES
('po_001', 'BC-2024-001', 'HP Enterprise', '2024-01-10', '2024-01-25', 12000.00, 2400.00, 14400.00, 'delivered', 'Matériel informatique'),
('po_002', 'BC-2024-002', 'Oracle Corporation', '2024-02-05', '2024-02-20', 8000.00, 1600.00, 9600.00, 'confirmed', 'Licences base de données'),
('po_003', 'BC-2024-003', 'Cisco Systems', '2024-03-10', '2024-03-25', 6500.00, 1300.00, 7800.00, 'sent', 'Équipements réseau')
ON CONFLICT (id) DO NOTHING;

INSERT INTO quotes (id, quote_number, client_name, quote_date, validity_date, amount_ht, amount_tva, amount_ttc, status, description) VALUES
('quote_001', 'DEV-2024-001', 'StartupTech SARL', '2024-01-05', '2024-02-05', 35000.00, 7000.00, 42000.00, 'sent', 'Développement plateforme e-commerce'),
('quote_002', 'DEV-2024-002', 'RetailCorp SA', '2024-02-12', '2024-03-12', 22000.00, 4400.00, 26400.00, 'accepted', 'Migration système ERP'),
('quote_003', 'DEV-2024-003', 'FinanceGroup Ltd', '2024-03-08', '2024-04-08', 18500.00, 3700.00, 22200.00, 'draft', 'Audit sécurité informatique')
ON CONFLICT (id) DO NOTHING;

-- Message de confirmation
SELECT 'Tables créées avec documents et données de démonstration insérées avec succès !' as message;
