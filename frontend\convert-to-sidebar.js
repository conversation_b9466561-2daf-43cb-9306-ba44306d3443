/**
 * Script pour convertir automatiquement tous les modules ERP HUB
 * vers la nouvelle navigation verticale
 */

const fs = require('fs');
const path = require('path');

// Liste des fichiers à convertir
const MODULE_FILES = [
    'hr-management-postgresql.html',
    'crm-management-postgresql.html',
    'sales-management-postgresql.html',
    'purchase-management-postgresql.html',
    'stock-management-postgresql.html',
    'logistics-management-postgresql.html',
    'finance-management.html',
    'bi-management-postgresql.html',
    'ai-assistant.html'
];

// Configuration des modules
const MODULE_CONFIG = {
    'hr-management-postgresql.html': {
        title: 'Ressources Humaines',
        icon: 'people',
        color: '#10b981'
    },
    'crm-management-postgresql.html': {
        title: 'CRM',
        icon: 'contacts',
        color: '#ec4899'
    },
    'sales-management-postgresql.html': {
        title: 'Ventes',
        icon: 'trending_up',
        color: '#3b82f6'
    },
    'purchase-management-postgresql.html': {
        title: 'Achats',
        icon: 'shopping_cart',
        color: '#8b5cf6'
    },
    'stock-management-postgresql.html': {
        title: 'Stocks',
        icon: 'inventory_2',
        color: '#06b6d4'
    },
    'logistics-management-postgresql.html': {
        title: 'Logistique',
        icon: 'local_shipping',
        color: '#ef4444'
    },
    'finance-management.html': {
        title: 'Finance',
        icon: 'account_balance',
        color: '#f59e0b'
    },
    'bi-management-postgresql.html': {
        title: 'Business Intelligence',
        icon: 'analytics',
        color: '#7c3aed'
    },
    'ai-assistant.html': {
        title: 'Assistant IA',
        icon: 'smart_toy',
        color: '#6366f1'
    }
};

/**
 * Convertir un fichier vers la navigation verticale
 */
function convertModuleFile(filename) {
    const filePath = path.join(__dirname, filename);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ Fichier non trouvé: ${filename}`);
        return false;
    }
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const config = MODULE_CONFIG[filename];
        
        if (!config) {
            console.log(`❌ Configuration manquante pour: ${filename}`);
            return false;
        }
        
        console.log(`🔄 Conversion de ${filename}...`);
        
        // 1. Ajouter le lien vers le CSS de navigation
        if (!content.includes('sidebar-navigation.css')) {
            content = content.replace(
                /<link href="https:\/\/fonts\.googleapis\.com\/icon\?family=Material\+Icons" rel="stylesheet">/,
                `<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="sidebar-navigation.css" rel="stylesheet">`
            );
        }
        
        // 2. Modifier le body pour utiliser la structure sidebar
        content = content.replace(
            /body\s*{[^}]*}/,
            `body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }`
        );
        
        // 3. Supprimer l'ancien header et le remplacer
        content = content.replace(
            /<header class="header">[\s\S]*?<\/header>/,
            ''
        );
        
        // 4. Remplacer la structure du body
        content = content.replace(
            /<body>[\s\S]*?<main class="main-content">/,
            `<body>
    <!-- La navigation sera injectée automatiquement par sidebar-navigation.js -->
    
    <main class="main-content">
        <div class="module-header">
            <div class="module-header-content">
                <div class="module-title">
                    <span class="module-icon material-icons">${config.icon}</span>
                    <h1>${config.title}</h1>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="connection-status">
                        <div class="status-indicator" id="connectionStatus"></div>
                        <span id="connectionText">Connexion...</span>
                    </div>
                    <button class="btn btn-primary" onclick="refreshData ? refreshData() : location.reload()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Actualiser
                    </button>
                </div>
            </div>
        </div>
        
        <div class="module-content">`
        );
        
        // 5. Fermer correctement les divs avant le script
        content = content.replace(
            /<\/main>\s*<script>/,
            `        </div>
    </main>

    <script src="sidebar-navigation.js"></script>
    <script>`
        );
        
        // 6. Mettre à jour les styles pour la connection-status
        content = content.replace(
            /\.connection-status\s*{[^}]*}/,
            `.connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            background: rgba(59, 130, 246, 0.1);
            color: #1e293b;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }`
        );
        
        // 7. Ajouter les styles pour module-content si nécessaire
        if (!content.includes('.module-content')) {
            content = content.replace(
                /\.main-content\s*{[^}]*}/,
                `.main-content {
            flex: 1;
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .module-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }`
            );
        }
        
        // 8. Sauvegarder le fichier modifié
        fs.writeFileSync(filePath, content, 'utf8');
        
        console.log(`✅ ${filename} converti avec succès`);
        return true;
        
    } catch (error) {
        console.error(`❌ Erreur lors de la conversion de ${filename}:`, error.message);
        return false;
    }
}

/**
 * Convertir tous les modules
 */
function convertAllModules() {
    console.log('🚀 Début de la conversion vers la navigation verticale...\n');
    
    let successCount = 0;
    let totalCount = MODULE_FILES.length;
    
    MODULE_FILES.forEach(filename => {
        if (convertModuleFile(filename)) {
            successCount++;
        }
    });
    
    console.log(`\n📊 Résultats de la conversion:`);
    console.log(`✅ Succès: ${successCount}/${totalCount}`);
    console.log(`❌ Échecs: ${totalCount - successCount}/${totalCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 Tous les modules ont été convertis avec succès !');
        console.log('📝 N\'oubliez pas de vérifier que les fichiers sidebar-navigation.css et sidebar-navigation.js sont présents.');
    } else {
        console.log('\n⚠️ Certains modules n\'ont pas pu être convertis. Vérifiez les erreurs ci-dessus.');
    }
}

/**
 * Créer un fichier de sauvegarde
 */
function createBackup() {
    const backupDir = path.join(__dirname, 'backup-before-sidebar');
    
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir);
    }
    
    MODULE_FILES.forEach(filename => {
        const sourcePath = path.join(__dirname, filename);
        const backupPath = path.join(backupDir, filename);
        
        if (fs.existsSync(sourcePath)) {
            fs.copyFileSync(sourcePath, backupPath);
        }
    });
    
    console.log(`💾 Sauvegarde créée dans: ${backupDir}`);
}

// Exécution du script
if (require.main === module) {
    console.log('🔧 Conversion des modules ERP HUB vers la navigation verticale');
    console.log('=' .repeat(60));
    
    // Créer une sauvegarde
    createBackup();
    
    // Convertir tous les modules
    convertAllModules();
}

module.exports = {
    convertModuleFile,
    convertAllModules,
    createBackup,
    MODULE_FILES,
    MODULE_CONFIG
};
