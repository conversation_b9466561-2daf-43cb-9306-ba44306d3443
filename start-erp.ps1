# ERP HUB - Script de lancement PowerShell
# Exécution: .\start-erp.ps1

param(
    [switch]$Force,
    [int]$Port = 5173,
    [switch]$SkipInstall
)

# Configuration des couleurs
$Host.UI.RawUI.WindowTitle = "ERP HUB - Lancement"

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) { Write-ColorOutput Green "✅ $message" }
function Write-Error($message) { Write-ColorOutput Red "❌ $message" }
function Write-Warning($message) { Write-ColorOutput Yellow "⚠️  $message" }
function Write-Info($message) { Write-ColorOutput Cyan "ℹ️  $message" }
function Write-Progress($message) { Write-ColorOutput Blue "🔄 $message" }

# Banner
Write-Host ""
Write-ColorOutput Magenta "========================================"
Write-ColorOutput Magenta "    🚀 ERP HUB - LANCEMENT AUTOMATIQUE"
Write-ColorOutput Magenta "========================================"
Write-Host ""

try {
    # 1. Vérification de Node.js
    Write-Progress "Vérification de Node.js..."
    try {
        $nodeVersion = node --version
        Write-Success "Node.js détecté: $nodeVersion"
    }
    catch {
        Write-Error "Node.js n'est pas installé ou non accessible"
        Write-Warning "Veuillez installer Node.js depuis https://nodejs.org"
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # 2. Vérification de npm
    Write-Progress "Vérification de npm..."
    try {
        $npmVersion = npm --version
        Write-Success "npm détecté: $npmVersion"
    }
    catch {
        Write-Error "npm n'est pas accessible"
        exit 1
    }

    # 3. Vérification du dossier frontend
    Write-Progress "Vérification du projet..."
    if (-not (Test-Path "frontend")) {
        Write-Error "Dossier frontend introuvable"
        Write-Warning "Assurez-vous d'être dans le dossier racine du projet ERP HUB"
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    Set-Location frontend
    Write-Success "Dossier frontend trouvé"

    # 4. Vérification du package.json
    if (-not (Test-Path "package.json")) {
        Write-Error "package.json introuvable"
        exit 1
    }
    Write-Success "package.json trouvé"

    # 5. Configuration PowerShell
    Write-Progress "Configuration de la politique d'exécution PowerShell..."
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Success "Politique PowerShell configurée"
    }
    catch {
        Write-Warning "Impossible de modifier la politique PowerShell (permissions insuffisantes)"
    }

    # 6. Vérification des dépendances
    if (-not $SkipInstall) {
        Write-Progress "Vérification des dépendances..."
        if (-not (Test-Path "node_modules") -or $Force) {
            Write-Progress "Installation des dépendances..."
            Write-Warning "Cela peut prendre quelques minutes..."
            
            # Nettoyage du cache
            Write-Progress "Nettoyage du cache npm..."
            npm cache clean --force | Out-Null
            
            # Installation
            $installResult = npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Installation standard échouée, tentative avec --legacy-peer-deps..."
                $installResult = npm install --legacy-peer-deps
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Installation des dépendances échouée"
                    Read-Host "Appuyez sur Entrée pour continuer"
                    exit 1
                }
            }
            Write-Success "Dépendances installées avec succès"
        } else {
            Write-Success "Dépendances déjà installées"
        }
    }

    # 7. Vérification des ports
    Write-Progress "Vérification des ports disponibles..."
    $portInUse = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Warning "Port $Port déjà utilisé"
        $Port = 3000
        Write-Info "Utilisation du port $Port"
    }

    $appUrl = "http://localhost:$Port"

    # 8. Création du fichier de configuration Vite si nécessaire
    Write-Progress "Configuration du serveur de développement..."
    
    # 9. Affichage des informations de lancement
    Write-Host ""
    Write-ColorOutput Green "========================================"
    Write-ColorOutput Green "    🎉 LANCEMENT DE ERP HUB"
    Write-ColorOutput Green "========================================"
    Write-Success "URL de l'application: $appUrl"
    Write-Info "Fonctionnalités disponibles:"
    Write-Host "   👑 Agent Manager - Supervision système"
    Write-Host "   👥 Agent HR - Ressources humaines"
    Write-Host "   💼 Agent Sales - Gestion commerciale"
    Write-Host "   🛒 Agent Purchase - Gestion achats"
    Write-Host "   🚚 Agent Logistics - Transport et livraisons"
    Write-Host "   📦 Agent Stock - Gestion inventaire"
    Write-Host "   📊 Agent Accounting - Comptabilité"
    Write-Host "   💰 Agent Finance - Gestion financière"
    Write-Host "   🤝 Agent CRM - Relation client"
    Write-Host "   📈 Agent BI - Business Intelligence"
    Write-Host ""
    Write-Host "   🎨 Arrière-plans personnalisés"
    Write-Host "   🌙 Mode sombre intelligent"
    Write-Host "   🔔 Notifications temps réel"
    Write-Host "   📊 Export de données avancé"
    Write-Host "   🧩 Widgets déplaçables"
    Write-Host "   🧪 Suite de tests utilisateur"
    Write-Host ""
    Write-Warning "Appuyez sur Ctrl+C pour arrêter le serveur"
    Write-Host ""

    # 10. Ouverture automatique du navigateur après 5 secondes
    Start-Job -ScriptBlock {
        Start-Sleep 5
        Start-Process $using:appUrl
    } | Out-Null

    # 11. Lancement du serveur Vite
    Write-Progress "Démarrage du serveur Vite..."
    
    if ($Port -ne 5173) {
        npm run dev -- --port $Port --host 0.0.0.0
    } else {
        npm run dev -- --host 0.0.0.0
    }
}
catch {
    Write-Error "Erreur inattendue: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}
finally {
    Write-Host ""
    Write-ColorOutput Yellow "👋 Serveur ERP HUB arrêté"
    Read-Host "Appuyez sur Entrée pour fermer"
}
