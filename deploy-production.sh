#!/bin/bash

# Script de déploiement en production pour ERP HUB
# Usage: ./deploy-production.sh [environment]

set -e  # Arrêter en cas d'erreur

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="erp-hub"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Vérification des prérequis
check_prerequisites() {
    log "🔍 Vérification des prérequis..."
    
    # Docker
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé"
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose n'est pas installé"
    fi
    
    # Git
    if ! command -v git &> /dev/null; then
        error "Git n'est pas installé"
    fi
    
    # Fichier .env
    if [ ! -f ".env.production" ]; then
        error "Fichier .env.production manquant"
    fi
    
    log "✅ Prérequis vérifiés"
}

# Sauvegarde avant déploiement
backup_current_deployment() {
    log "💾 Sauvegarde avant déploiement..."
    
    # Créer le répertoire de sauvegarde
    mkdir -p "$BACKUP_DIR"
    
    # Timestamp pour la sauvegarde
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_NAME="backup_${TIMESTAMP}"
    
    # Sauvegarde de la base de données
    if docker-compose -f docker-compose.production.yml ps postgres | grep -q "Up"; then
        log "📊 Sauvegarde de la base de données..."
        docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U erp_user erp_hub_prod > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"
    fi
    
    # Sauvegarde des volumes
    log "📁 Sauvegarde des volumes..."
    docker run --rm -v erp-hub_static_files:/data -v "$PWD/$BACKUP_DIR":/backup alpine tar czf "/backup/${BACKUP_NAME}_static.tar.gz" -C /data .
    docker run --rm -v erp-hub_media_files:/data -v "$PWD/$BACKUP_DIR":/backup alpine tar czf "/backup/${BACKUP_NAME}_media.tar.gz" -C /data .
    
    log "✅ Sauvegarde terminée: $BACKUP_NAME"
}

# Mise à jour du code
update_code() {
    log "📥 Mise à jour du code..."
    
    # Vérifier si on est dans un repo git
    if [ -d ".git" ]; then
        # Sauvegarder les changements locaux
        git stash push -m "Deployment stash $(date)"
        
        # Récupérer les dernières modifications
        git fetch origin
        git checkout main
        git pull origin main
        
        log "✅ Code mis à jour"
    else
        warning "Pas de repository Git détecté, mise à jour manuelle nécessaire"
    fi
}

# Build des images Docker
build_images() {
    log "🏗️  Build des images Docker..."
    
    # Copier le fichier d'environnement
    cp .env.production .env
    
    # Build avec cache
    docker-compose -f docker-compose.production.yml build --parallel
    
    log "✅ Images construites"
}

# Déploiement des services
deploy_services() {
    log "🚀 Déploiement des services..."
    
    # Arrêter les anciens services (sauf la base de données)
    docker-compose -f docker-compose.production.yml stop backend frontend nginx celery_worker celery_beat
    
    # Démarrer la base de données et Redis en premier
    docker-compose -f docker-compose.production.yml up -d postgres redis
    
    # Attendre que la base de données soit prête
    log "⏳ Attente de la base de données..."
    sleep 30
    
    # Migrations de la base de données
    log "🔄 Exécution des migrations..."
    docker-compose -f docker-compose.production.yml run --rm backend python manage.py migrate
    
    # Collecte des fichiers statiques
    log "📁 Collecte des fichiers statiques..."
    docker-compose -f docker-compose.production.yml run --rm backend python manage.py collectstatic --noinput
    
    # Démarrer tous les services
    docker-compose -f docker-compose.production.yml up -d
    
    log "✅ Services déployés"
}

# Vérification de la santé des services
health_check() {
    log "🏥 Vérification de la santé des services..."
    
    # Attendre que les services démarrent
    sleep 60
    
    # Vérifier chaque service
    services=("postgres" "redis" "backend" "frontend" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.production.yml ps "$service" | grep -q "Up"; then
            log "✅ $service: OK"
        else
            error "❌ $service: ÉCHEC"
        fi
    done
    
    # Test de l'API
    log "🔍 Test de l'API..."
    if curl -f http://localhost/api/health/ > /dev/null 2>&1; then
        log "✅ API: OK"
    else
        error "❌ API: ÉCHEC"
    fi
    
    # Test du frontend
    log "🔍 Test du frontend..."
    if curl -f http://localhost/ > /dev/null 2>&1; then
        log "✅ Frontend: OK"
    else
        error "❌ Frontend: ÉCHEC"
    fi
}

# Nettoyage post-déploiement
cleanup() {
    log "🧹 Nettoyage post-déploiement..."
    
    # Supprimer les images non utilisées
    docker image prune -f
    
    # Supprimer les volumes non utilisés
    docker volume prune -f
    
    # Supprimer les réseaux non utilisés
    docker network prune -f
    
    log "✅ Nettoyage terminé"
}

# Configuration du monitoring
setup_monitoring() {
    log "📊 Configuration du monitoring..."
    
    # Démarrer Prometheus et Grafana
    docker-compose -f docker-compose.production.yml up -d prometheus grafana
    
    # Attendre que Grafana soit prêt
    sleep 30
    
    # Importer les dashboards (si configuré)
    if [ -d "./monitoring/grafana/dashboards" ]; then
        log "📈 Import des dashboards Grafana..."
        # Script d'import des dashboards
    fi
    
    log "✅ Monitoring configuré"
}

# Configuration SSL (optionnel)
setup_ssl() {
    if [ "$ENABLE_SSL" = "true" ]; then
        log "🔒 Configuration SSL..."
        
        # Générer ou renouveler les certificats Let's Encrypt
        if [ ! -d "./ssl" ]; then
            mkdir -p ./ssl
        fi
        
        # Utiliser certbot pour générer les certificats
        # docker run --rm -v "$PWD/ssl:/etc/letsencrypt" certbot/certbot certonly --standalone -d $DOMAIN_NAME
        
        log "✅ SSL configuré"
    fi
}

# Notification de déploiement
send_notification() {
    log "📢 Envoi de notification..."
    
    # Webhook Slack/Discord/Teams (si configuré)
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 ERP HUB déployé avec succès en $ENVIRONMENT\"}" \
            "$WEBHOOK_URL"
    fi
    
    # Email (si configuré)
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        echo "ERP HUB déployé avec succès en $ENVIRONMENT à $(date)" | \
            mail -s "Déploiement ERP HUB" "$NOTIFICATION_EMAIL"
    fi
}

# Fonction principale
main() {
    log "🚀 Début du déploiement ERP HUB en $ENVIRONMENT"
    log "=================================================="
    
    # Créer le répertoire de logs
    mkdir -p ./logs
    
    # Exécuter les étapes de déploiement
    check_prerequisites
    backup_current_deployment
    update_code
    build_images
    deploy_services
    health_check
    setup_monitoring
    setup_ssl
    cleanup
    send_notification
    
    log "=================================================="
    log "🎉 Déploiement terminé avec succès!"
    log "📊 Monitoring: http://localhost:3001 (Grafana)"
    log "🔍 Logs: http://localhost:5601 (Kibana)"
    log "🌐 Application: http://localhost"
    log "=================================================="
}

# Gestion des signaux
trap 'error "Déploiement interrompu"' INT TERM

# Vérifier les arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [environment]"
    echo "Environments: production, staging"
    echo "Options:"
    echo "  --help, -h    Afficher cette aide"
    exit 0
fi

# Exécuter le déploiement
main "$@"
