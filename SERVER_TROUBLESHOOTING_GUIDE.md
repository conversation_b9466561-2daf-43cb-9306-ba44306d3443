# 🔧 **GUIDE DE RÉSOLUTION DES PROBLÈMES SERVEUR**

## **🎯 PROBLÈMES IDENTIFIÉS ET SOLUTIONS**

### **⚡ SOLUTION RAPIDE - DÉMARRAGE IMMÉDIAT**

#### **🚀 Méthode 1 : Script PowerShell Automatique**
```powershell
# Exécuter le script de configuration
.\setup-environment.ps1

# Puis démarrer avec :
.\start-erp-node.bat
```

#### **🌐 Méthode 2 : Serveur Python Simple**
```bash
# Dans le dossier frontend
cd frontend
python -m http.server 8080

# Ouvrir : http://localhost:8080
```

#### **📡 Méthode 3 : Node.js http-server**
```bash
# Installation globale
npm install -g http-server

# Démarrage
cd frontend
http-server -p 8080 -o
```

## **🔧 RÉSOLUTION DÉTAILLÉE PAR PROBLÈME**

### **1. 🛡️ PROBLÈME POWERSHELL - EXECUTION POLICY**

#### **Symptôme :**
```
Execution of scripts is disabled on this system
```

#### **Solution :**
```powershell
# Ouvrir PowerShell en tant qu'administrateur
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser

# Ou pour tous les utilisateurs (admin requis)
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine

# Vérifier la configuration
Get-ExecutionPolicy -List
```

#### **Alternative :**
```powershell
# Exécution ponctuelle sans changer la politique
powershell -ExecutionPolicy Bypass -File setup-environment.ps1
```

### **2. 📦 PROBLÈME NODE.JS - INSTALLATION/PERMISSIONS**

#### **Symptôme :**
```
'node' is not recognized as an internal or external command
npm ERR! EACCES: permission denied
```

#### **Solution A : Réinstallation avec Permissions Admin**
1. **Désinstaller Node.js** via Panneau de configuration
2. **Télécharger** la dernière version LTS : https://nodejs.org/
3. **Installer en tant qu'administrateur** (clic droit → "Exécuter en tant qu'administrateur")
4. **Redémarrer** l'invite de commande

#### **Solution B : Utiliser un Gestionnaire de Versions**
```bash
# Installer nvm-windows
# Télécharger depuis : https://github.com/coreybutler/nvm-windows

# Installer Node.js via nvm
nvm install 20.10.0
nvm use 20.10.0
```

#### **Solution C : Configuration npm**
```bash
# Configurer le dossier global npm
npm config set prefix "C:\Users\<USER>\AppData\Roaming\npm"

# Ajouter au PATH si nécessaire
# %USERPROFILE%\AppData\Roaming\npm
```

### **3. 🐧 SOLUTION WSL (WINDOWS SUBSYSTEM FOR LINUX)**

#### **Installation WSL :**
```powershell
# Activer WSL (PowerShell Admin)
wsl --install

# Ou installer Ubuntu spécifiquement
wsl --install -d Ubuntu
```

#### **Configuration dans WSL :**
```bash
# Mettre à jour le système
sudo apt update && sudo apt upgrade -y

# Installer Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Installer les outils
npm install -g http-server live-server serve

# Naviguer vers le projet
cd /mnt/c/Users/<USER>/Documents/augment-projects/ERP_HUB

# Démarrer le serveur
cd frontend
http-server -p 8080 -o
```

### **4. 🐳 SOLUTION DOCKER - CONTAINERISATION**

#### **Installation Docker Desktop :**
1. **Télécharger** : https://www.docker.com/products/docker-desktop/
2. **Installer** avec les paramètres par défaut
3. **Redémarrer** l'ordinateur

#### **Utilisation Docker :**
```bash
# Construire l'image frontend
docker build -f Dockerfile.frontend -t erp-hub-frontend .

# Démarrer le conteneur
docker run -p 8080:8080 erp-hub-frontend

# Ou utiliser Docker Compose
docker-compose up frontend
```

#### **Docker Compose Complet :**
```bash
# Démarrer tous les services
docker-compose up

# Démarrer seulement le frontend
docker-compose up frontend

# Mode développement
docker-compose --profile dev up
```

## **🚀 RECOMMANDATIONS TECHNOLOGIQUES**

### **✅ STACK RECOMMANDÉE POUR DÉVELOPPEMENT**

#### **Frontend :**
```bash
# React avec Vite (moderne et rapide)
npm create vite@latest erp-hub-react -- --template react
cd erp-hub-react
npm install
npm run dev
```

#### **Backend :**
```bash
# Django REST Framework
pip install django djangorestframework
django-admin startproject erp_backend
cd erp_backend
python manage.py startapp api
```

#### **Base de Données :**
```bash
# PostgreSQL avec Docker
docker run --name erp-postgres -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres:15
```

### **📁 STRUCTURE PROJET RECOMMANDÉE**
```
ERP_HUB/
├── frontend/                 # Fichiers HTML actuels (backup)
├── erp-hub-react/           # Application React moderne
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/                 # API Django
│   ├── erp_backend/
│   ├── apps/
│   └── requirements.txt
├── docker-compose.yml       # Configuration Docker
├── setup-environment.ps1   # Script de configuration
└── README.md
```

## **🔄 MIGRATION PROGRESSIVE**

### **Phase 1 : Stabilisation Actuelle**
1. ✅ **Utiliser les scripts créés** (`start-erp-node.bat`)
2. ✅ **Garder les fichiers HTML** comme backup fonctionnel
3. ✅ **Tester toutes les fonctionnalités** existantes

### **Phase 2 : Modernisation Frontend**
1. 🔄 **Créer l'application React** avec Vite
2. 🔄 **Migrer les composants** un par un
3. 🔄 **Intégrer les données** existantes
4. 🔄 **Tester la compatibilité**

### **Phase 3 : Backend API**
1. 🔄 **Développer l'API Django** REST
2. 🔄 **Connecter la base de données** PostgreSQL
3. 🔄 **Implémenter l'authentification**
4. 🔄 **Migrer les données**

### **Phase 4 : Production**
1. 🔄 **Déployer avec Docker**
2. 🔄 **Configurer le monitoring**
3. 🔄 **Tests utilisateurs**
4. 🔄 **Mise en production**

## **📋 COMMANDES DE DÉPANNAGE**

### **🔍 Diagnostic Système :**
```bash
# Vérifier les versions
node --version
npm --version
python --version
docker --version

# Vérifier les ports utilisés
netstat -an | findstr :8080
netstat -an | findstr :3000

# Tester la connectivité
curl http://localhost:8080
```

### **🧹 Nettoyage :**
```bash
# Nettoyer le cache npm
npm cache clean --force

# Supprimer node_modules et réinstaller
rm -rf node_modules
npm install

# Nettoyer Docker
docker system prune -a
```

### **🔄 Redémarrage Services :**
```bash
# Arrêter tous les processus Node.js
taskkill /f /im node.exe

# Redémarrer Docker Desktop
# Via l'interface graphique ou :
net stop com.docker.service
net start com.docker.service
```

## **📞 SUPPORT ET RESSOURCES**

### **🆘 En Cas de Problème Persistant :**
1. **Vérifier les logs** dans la console
2. **Tester avec un autre port** (8081, 3000, 5000)
3. **Désactiver temporairement** l'antivirus/firewall
4. **Utiliser le mode incognito** du navigateur
5. **Redémarrer l'ordinateur**

### **📚 Documentation Utile :**
- **Node.js** : https://nodejs.org/docs/
- **Docker** : https://docs.docker.com/
- **React + Vite** : https://vitejs.dev/guide/
- **Django** : https://docs.djangoproject.com/

### **🎯 Objectif Final :**
**Application ERP moderne, scalable et maintenable avec React frontend, Django backend, et déploiement Docker.**

## **✅ CHECKLIST DE VÉRIFICATION**

- [ ] PowerShell configuré (RemoteSigned)
- [ ] Node.js installé et fonctionnel
- [ ] npm/npx disponibles
- [ ] Scripts de démarrage créés
- [ ] Port 8080 libre
- [ ] Navigateur testé
- [ ] Fichiers HTML accessibles
- [ ] Docker installé (optionnel)
- [ ] WSL configuré (optionnel)
- [ ] Projet React créé (futur)

**🎉 Une fois cette checklist complète, votre environnement ERP HUB sera parfaitement opérationnel !**
