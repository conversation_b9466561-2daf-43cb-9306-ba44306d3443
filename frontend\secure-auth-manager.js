// 🔐 GESTIONNAIRE D'AUTHENTIFICATION SÉCURISÉ ERP HUB
// Version sécurisée avec httpOnly cookies et protection CSRF

class SecureERPAuthManager {
    constructor(apiBaseUrl = 'http://localhost:5000/api') {
        this.apiBaseUrl = apiBaseUrl;
        this.user = null;
        this.permissions = {};
        this.csrfToken = null;
        this.refreshTokenTimer = null;
        
        // Initialiser la sécurité
        this.initSecurity();
        this.checkAuthStatus();
    }

    // ===== INITIALISATION SÉCURITÉ =====

    initSecurity() {
        // Générer un token CSRF côté client
        this.generateCSRFToken();
        
        // Configurer les intercepteurs de requêtes
        this.setupRequestInterceptors();
        
        // Démarrer le refresh automatique des tokens
        this.startTokenRefresh();
    }

    generateCSRFToken() {
        // Générer un token CSRF aléatoire
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        this.csrfToken = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
        
        // Stocker en session storage (plus sécurisé que localStorage)
        sessionStorage.setItem('csrf_token', this.csrfToken);
    }

    setupRequestInterceptors() {
        // Intercepter toutes les requêtes fetch pour ajouter les headers de sécurité
        const originalFetch = window.fetch;
        window.fetch = async (url, options = {}) => {
            // Ajouter les headers de sécurité
            const secureOptions = {
                ...options,
                credentials: 'include', // Inclure les cookies httpOnly
                headers: {
                    'X-CSRF-Token': this.csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                }
            };

            return originalFetch(url, secureOptions);
        };
    }

    // ===== AUTHENTIFICATION SÉCURISÉE =====

    async login(username, password) {
        try {
            // Validation côté client
            if (!this.validateCredentials(username, password)) {
                return {
                    success: false,
                    error: 'Identifiants invalides'
                };
            }

            const response = await fetch(`${this.apiBaseUrl}/auth/secure-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.csrfToken
                },
                credentials: 'include',
                body: JSON.stringify({ 
                    username: this.sanitizeInput(username), 
                    password,
                    csrf_token: this.csrfToken
                })
            });

            const result = await response.json();

            if (result.success) {
                this.user = result.user;
                this.permissions = result.permissions || {};
                
                // Stocker les infos utilisateur (pas le token)
                sessionStorage.setItem('erp_user', JSON.stringify(this.user));
                sessionStorage.setItem('erp_permissions', JSON.stringify(this.permissions));
                
                // Démarrer le refresh automatique
                this.startTokenRefresh();
                
                this.showAuthStatus('success', `Bienvenue ${this.user.first_name} !`);
                
                // Logger la connexion sécurisée
                this.logSecurityEvent('login_success', { username });
                
                return { success: true, user: this.user };
            } else {
                this.logSecurityEvent('login_failed', { username, error: result.error });
                this.showAuthStatus('error', result.error);
                return { success: false, error: result.error };
            }

        } catch (error) {
            this.logSecurityEvent('login_error', { username, error: error.message });
            this.showAuthStatus('error', 'Erreur de connexion');
            return { success: false, error: 'Erreur de connexion' };
        }
    }

    async logout() {
        try {
            // Appeler l'endpoint de déconnexion sécurisée
            await fetch(`${this.apiBaseUrl}/auth/secure-logout`, {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': this.csrfToken
                },
                credentials: 'include'
            });

        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
        } finally {
            // Nettoyer les données locales
            this.clearAuthData();
            this.showAuthStatus('info', 'Déconnexion réussie');
            
            // Rediriger vers la page de connexion
            setTimeout(() => {
                this.showLoginForm();
            }, 1000);
        }
    }

    clearAuthData() {
        this.user = null;
        this.permissions = {};
        
        // Nettoyer le stockage
        sessionStorage.removeItem('erp_user');
        sessionStorage.removeItem('erp_permissions');
        
        // Arrêter le refresh des tokens
        if (this.refreshTokenTimer) {
            clearInterval(this.refreshTokenTimer);
            this.refreshTokenTimer = null;
        }
        
        // Régénérer le token CSRF
        this.generateCSRFToken();
    }

    // ===== REFRESH AUTOMATIQUE DES TOKENS =====

    startTokenRefresh() {
        // Refresh toutes les 45 minutes (token expire en 1h)
        this.refreshTokenTimer = setInterval(async () => {
            await this.refreshToken();
        }, 45 * 60 * 1000);
    }

    async refreshToken() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/refresh-token`, {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': this.csrfToken
                },
                credentials: 'include'
            });

            if (!response.ok) {
                // Token expiré, déconnecter
                await this.logout();
                return false;
            }

            const result = await response.json();
            if (result.success) {
                // Mettre à jour les infos utilisateur si nécessaire
                if (result.user) {
                    this.user = result.user;
                    sessionStorage.setItem('erp_user', JSON.stringify(this.user));
                }
                return true;
            }

            return false;

        } catch (error) {
            console.error('Erreur refresh token:', error);
            return false;
        }
    }

    // ===== VALIDATION ET SÉCURITÉ =====

    validateCredentials(username, password) {
        // Validation basique côté client
        if (!username || username.length < 3) return false;
        if (!password || password.length < 6) return false;
        
        // Vérifier les caractères dangereux
        const dangerousChars = /[<>\"'&]/;
        if (dangerousChars.test(username)) return false;
        
        return true;
    }

    sanitizeInput(input) {
        // Nettoyer les entrées utilisateur
        return input.trim().replace(/[<>\"'&]/g, '');
    }

    logSecurityEvent(event, data) {
        // Logger les événements de sécurité
        const logEntry = {
            timestamp: new Date().toISOString(),
            event,
            data,
            userAgent: navigator.userAgent,
            ip: 'client-side' // L'IP sera loggée côté serveur
        };
        
        console.log('🔒 Security Event:', logEntry);
        
        // Envoyer au serveur pour audit
        fetch(`${this.apiBaseUrl}/security/log`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.csrfToken
            },
            credentials: 'include',
            body: JSON.stringify(logEntry)
        }).catch(err => console.error('Erreur log sécurité:', err));
    }

    // ===== REQUÊTES SÉCURISÉES =====

    async secureRequest(url, options = {}) {
        try {
            const response = await fetch(url, {
                ...options,
                credentials: 'include',
                headers: {
                    'X-CSRF-Token': this.csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                }
            });

            // Vérifier les erreurs d'authentification
            if (response.status === 401) {
                await this.logout();
                throw new Error('Session expirée');
            }

            if (response.status === 403) {
                this.showAuthStatus('error', 'Accès refusé');
                throw new Error('Accès refusé');
            }

            return response;

        } catch (error) {
            this.logSecurityEvent('request_error', { url, error: error.message });
            throw error;
        }
    }

    // ===== VÉRIFICATION D'ÉTAT =====

    async checkAuthStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/status`, {
                credentials: 'include',
                headers: {
                    'X-CSRF-Token': this.csrfToken
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.authenticated) {
                    this.user = result.user;
                    this.permissions = result.permissions || {};
                    
                    // Restaurer depuis sessionStorage si nécessaire
                    if (!this.user) {
                        const storedUser = sessionStorage.getItem('erp_user');
                        if (storedUser) {
                            this.user = JSON.parse(storedUser);
                        }
                    }
                    
                    this.startTokenRefresh();
                    return true;
                }
            }

            // Pas authentifié
            this.clearAuthData();
            return false;

        } catch (error) {
            console.error('Erreur vérification auth:', error);
            this.clearAuthData();
            return false;
        }
    }

    // ===== INTERFACE UTILISATEUR =====

    showAuthStatus(type, message) {
        // Réutiliser la fonction existante mais avec améliorations sécurité
        let indicator = document.querySelector('.auth-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auth-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(indicator);
        }

        const styles = {
            'success': 'background: #10b981; border-left: 4px solid #059669;',
            'error': 'background: #ef4444; border-left: 4px solid #dc2626;',
            'warning': 'background: #f59e0b; border-left: 4px solid #d97706;',
            'info': 'background: #3b82f6; border-left: 4px solid #2563eb;'
        };

        indicator.style.cssText += styles[type] || styles['info'];
        
        // Échapper le HTML pour éviter XSS
        const safeMessage = this.escapeHtml(message);
        
        indicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span class="material-icons" style="font-size: 18px;">
                    ${type === 'success' ? 'check_circle' : 
                      type === 'error' ? 'error' : 
                      type === 'warning' ? 'warning' : 'info'}
                </span>
                <span>${safeMessage}</span>
            </div>
        `;

        setTimeout(() => {
            if (indicator && indicator.parentNode) {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    if (indicator && indicator.parentNode) {
                        indicator.parentNode.removeChild(indicator);
                    }
                }, 300);
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // ===== GETTERS =====

    isAuthenticated() {
        return !!(this.user);
    }

    getCurrentUser() {
        return this.user;
    }

    hasPermission(module, action) {
        if (!this.permissions || !this.permissions[module]) {
            return false;
        }
        return this.permissions[module].includes(action);
    }

    // ===== FORMULAIRE DE CONNEXION SÉCURISÉ =====

    showLoginForm() {
        // Supprimer le formulaire existant
        const existingForm = document.querySelector('.login-container');
        if (existingForm) {
            existingForm.remove();
        }

        const loginHtml = this.createSecureLoginForm();
        document.body.insertAdjacentHTML('beforeend', loginHtml);

        // Ajouter les gestionnaires d'événements sécurisés
        this.setupSecureLoginHandlers();
    }

    createSecureLoginForm() {
        return `
            <div class="login-container" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            ">
                <div class="login-form" style="
                    background: white;
                    padding: 40px;
                    border-radius: 16px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    width: 100%;
                    max-width: 400px;
                    text-align: center;
                ">
                    <div style="margin-bottom: 30px;">
                        <h1 style="color: #1f2937; margin: 0 0 8px 0; font-size: 28px;">🔒 ERP HUB</h1>
                        <p style="color: #6b7280; margin: 0; font-size: 16px;">Connexion Sécurisée</p>
                        <div style="margin-top: 10px; padding: 8px; background: #f0f9ff; border-radius: 6px; font-size: 12px; color: #0369a1;">
                            🛡️ Connexion protégée par CSRF et cookies sécurisés
                        </div>
                    </div>
                    
                    <form id="secureLoginForm" style="text-align: left;">
                        <input type="hidden" name="csrf_token" value="${this.csrfToken}">
                        
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; color: #374151; font-weight: 500;">
                                Nom d'utilisateur
                            </label>
                            <input 
                                type="text" 
                                id="secureUsername" 
                                name="username"
                                required
                                autocomplete="username"
                                maxlength="50"
                                pattern="[a-zA-Z0-9_-]{3,50}"
                                style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    transition: border-color 0.2s;
                                    box-sizing: border-box;
                                "
                                placeholder="Votre nom d'utilisateur"
                            >
                        </div>
                        
                        <div style="margin-bottom: 30px;">
                            <label style="display: block; margin-bottom: 8px; color: #374151; font-weight: 500;">
                                Mot de passe
                            </label>
                            <input 
                                type="password" 
                                id="securePassword" 
                                name="password"
                                required
                                autocomplete="current-password"
                                minlength="6"
                                style="
                                    width: 100%;
                                    padding: 12px 16px;
                                    border: 2px solid #e5e7eb;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    transition: border-color 0.2s;
                                    box-sizing: border-box;
                                "
                                placeholder="Votre mot de passe"
                            >
                        </div>
                        
                        <button 
                            type="submit"
                            id="loginButton"
                            style="
                                width: 100%;
                                padding: 14px;
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                border: none;
                                border-radius: 8px;
                                font-size: 16px;
                                font-weight: 600;
                                cursor: pointer;
                                transition: transform 0.2s;
                            "
                        >
                            🔐 Connexion Sécurisée
                        </button>
                    </form>
                    
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            Compte de démonstration :<br>
                            <strong>admin</strong> / <strong>Admin123!</strong>
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    setupSecureLoginHandlers() {
        const form = document.getElementById('secureLoginForm');
        const button = document.getElementById('loginButton');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Désactiver le bouton pour éviter les double-clics
            button.disabled = true;
            button.textContent = '🔄 Connexion...';
            
            const username = document.getElementById('secureUsername').value;
            const password = document.getElementById('securePassword').value;
            
            try {
                const result = await this.login(username, password);
                
                if (result.success) {
                    document.querySelector('.login-container').remove();
                    window.location.reload();
                } else {
                    // Réactiver le bouton
                    button.disabled = false;
                    button.textContent = '🔐 Connexion Sécurisée';
                }
            } catch (error) {
                button.disabled = false;
                button.textContent = '🔐 Connexion Sécurisée';
                this.showAuthStatus('error', 'Erreur de connexion');
            }
        });

        // Focus sur le champ username
        document.getElementById('secureUsername').focus();
    }
}

// ===== INSTANCE GLOBALE SÉCURISÉE =====
window.secureErpAuth = new SecureERPAuthManager();

console.log('🔐 Gestionnaire d\'authentification SÉCURISÉ ERP HUB initialisé');
console.log('🛡️ Fonctionnalités de sécurité activées:');
console.log('   - httpOnly cookies');
console.log('   - Protection CSRF');
console.log('   - Refresh automatique des tokens');
console.log('   - Validation et sanitisation des entrées');
console.log('   - Logging des événements de sécurité');
