<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent CRM - Relation Client | ERP HUB PostgreSQL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f9fafb;
            color: #1f2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ec4899 30%, #be185d 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #ec4899;
            color: white;
        }
        
        .btn-primary:hover {
            background: #be185d;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #ec4899;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .content-section {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.875rem; /* Réduction de la taille de police */
        }
        
        .data-table th,
        .data-table td {
            padding: 0.5rem; /* Réduction du padding de 1rem à 0.5rem */
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            line-height: 1.25; /* Réduction de la hauteur de ligne */
            vertical-align: middle;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 0.8125rem; /* Police encore plus petite pour les en-têtes */
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .data-table tbody tr {
            height: 2.5rem; /* Hauteur fixe pour les lignes */
        }
        
        .badge {
            display: inline-block;
            padding: 0.125rem 0.5rem; /* Réduction du padding des badges */
            border-radius: 0.75rem;
            font-size: 0.6875rem; /* Police plus petite pour les badges */
            font-weight: 600;
            line-height: 1.2;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #ec4899;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #92400e;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab:hover {
            color: #ec4899;
            background: #fdf2f8;
        }

        .nav-tab.active {
            color: #ec4899;
            border-bottom-color: #ec4899;
            background: #fdf2f8;
        }

        .nav-tab .material-icons {
            font-size: 1.2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .contact-priority {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .priority-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .priority-high {
            background: #ef4444;
        }

        .priority-medium {
            background: #f59e0b;
        }

        .priority-low {
            background: #10b981;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #1e293b;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .back-button:hover {
            background: #334155;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton de retour vers l'accueil -->
    <button class="back-button" onclick="goToHome()">
        <span class="material-icons">arrow_back</span>
        Accueil
    </button>
    <header class="header">
        <div class="logo">💼 Agent CRM - ERP HUB</div>
        <div class="nav-buttons">
            <div class="connection-status">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Connexion...</span>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
                <span class="material-icons" style="font-size: 1rem;">refresh</span>
                Actualiser
            </button>
            <a href="dashboard-global-postgresql.html" class="btn btn-secondary">
                <span class="material-icons" style="font-size: 1rem;">dashboard</span>
                Dashboard
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Gestion de la Relation Client</h1>
            <p class="page-subtitle">Contacts et interactions - Connecté à PostgreSQL</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalContacts">0</div>
                <div class="stat-label">Total Contacts</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalInteractions">0</div>
                <div class="stat-label">Interactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="decisionMakers">0</div>
                <div class="stat-label">Décideurs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="recentInteractions">0</div>
                <div class="stat-label">Cette Semaine</div>
            </div>
        </div>

        <!-- Alertes -->
        <div id="alertContainer"></div>

        <!-- Navigation par onglets -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('contacts')">
                <span class="material-icons">contacts</span>
                Contacts
            </button>
            <button class="nav-tab" onclick="showTab('interactions')">
                <span class="material-icons">chat</span>
                Interactions
            </button>
        </nav>

        <!-- Onglet Contacts -->
        <div id="contacts" class="tab-content active">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">👥 Contacts</h2>
                    <button class="btn btn-primary" onclick="loadContacts()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Nom Complet</th>
                                    <th>Entreprise</th>
                                    <th>Titre/Poste</th>
                                    <th>Email</th>
                                    <th>Téléphone</th>
                                    <th>Contact Principal</th>
                                    <th>Décideur</th>
                                </tr>
                            </thead>
                            <tbody id="contactsTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des contacts...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Interactions -->
        <div id="interactions" class="tab-content">
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">💬 Interactions</h2>
                    <button class="btn btn-primary" onclick="loadInteractions()">
                        <span class="material-icons" style="font-size: 1rem;">refresh</span>
                        Recharger
                    </button>
                </div>
                <div class="section-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Type</th>
                                    <th>Sujet</th>
                                    <th>Date</th>
                                    <th>Durée</th>
                                    <th>Résultat</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody id="interactionsTableBody">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 2rem;">
                                        <div class="loading"></div>
                                        Chargement des interactions...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let contacts = [];
        let interactions = [];
        let currentTab = 'contacts';

        // Fonction pour retourner à l'accueil
        function goToHome() {
            window.location.href = 'dashboard-global-postgresql.html';
        }

        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // Charger toutes les données
        async function loadAllData() {
            await checkConnection();
            await loadContacts();
            await loadInteractions();
            updateStats();
        }

        // Vérifier la connexion à l'API
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, data.database_status === 'connected');
                } else {
                    updateConnectionStatus(false, false);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
                updateConnectionStatus(false, false);
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(apiConnected, dbConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (apiConnected && dbConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PostgreSQL connecté';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Déconnecté';
            }
        }

        // Charger les contacts depuis PostgreSQL
        async function loadContacts() {
            try {
                showAlert('Chargement des contacts depuis PostgreSQL...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/contacts`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        contacts = data.data || [];
                        renderContactsTable();
                        showAlert(`${contacts.length} contacts chargés depuis PostgreSQL`, 'success');
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement contacts:', error);
                showAlert('Erreur lors du chargement des contacts: ' + error.message, 'error');
                contacts = [];
                renderContactsTable();
            }
        }

        // Charger les interactions depuis PostgreSQL
        async function loadInteractions() {
            try {
                const response = await fetch(`${API_BASE_URL}/interactions`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        interactions = data.data || [];
                        renderInteractionsTable();
                        console.log(`${interactions.length} interactions chargées depuis PostgreSQL`);
                    } else {
                        throw new Error(data.error || 'Erreur lors du chargement');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('Erreur chargement interactions:', error);
                showAlert('Erreur lors du chargement des interactions: ' + error.message, 'error');
                interactions = [];
                renderInteractionsTable();
            }
        }

        // Afficher le tableau des contacts
        function renderContactsTable() {
            const tbody = document.getElementById('contactsTableBody');
            
            if (contacts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucun contact trouvé
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = contacts.map(contact => `
                <tr>
                    <td><strong>${contact.firstName} ${contact.lastName}</strong></td>
                    <td>${contact.customerName || 'N/A'}</td>
                    <td>${contact.title || 'N/A'}</td>
                    <td>${contact.email || 'N/A'}</td>
                    <td>${contact.phone || 'N/A'}</td>
                    <td>${getBooleanBadge(contact.isPrimary, 'Principal', 'Secondaire')}</td>
                    <td>${getBooleanBadge(contact.isDecisionMaker, 'Décideur', 'Non décideur')}</td>
                </tr>
            `).join('');
        }

        // Afficher le tableau des interactions
        function renderInteractionsTable() {
            const tbody = document.getElementById('interactionsTableBody');
            
            if (interactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #6b7280;">
                            Aucune interaction trouvée
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = interactions.map(interaction => `
                <tr>
                    <td><strong>${interaction.customerName || 'N/A'}</strong></td>
                    <td>${getInteractionTypeBadge(interaction.interactionType)}</td>
                    <td>${interaction.subject || 'N/A'}</td>
                    <td>${formatDate(interaction.interactionDate)}</td>
                    <td>${interaction.durationMinutes ? interaction.durationMinutes + ' min' : 'N/A'}</td>
                    <td>${interaction.outcome || 'N/A'}</td>
                    <td>${getInteractionStatusBadge(interaction.status)}</td>
                </tr>
            `).join('');
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const totalContacts = contacts.length;
            const totalInteractions = interactions.length;
            const decisionMakers = contacts.filter(contact => contact.isDecisionMaker).length;
            
            // Interactions de cette semaine (simulation)
            const recentInteractions = Math.floor(totalInteractions * 0.3);

            document.getElementById('totalContacts').textContent = totalContacts;
            document.getElementById('totalInteractions').textContent = totalInteractions;
            document.getElementById('decisionMakers').textContent = decisionMakers;
            document.getElementById('recentInteractions').textContent = recentInteractions;
        }

        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus d'onglets
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher le contenu de l'onglet sélectionné
            document.getElementById(tabName).classList.add('active');
            
            // Activer l'onglet sélectionné
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // Fonctions utilitaires
        function getBooleanBadge(value, trueText, falseText) {
            if (value) {
                return `<span class="badge badge-success">${trueText}</span>`;
            } else {
                return `<span class="badge badge-warning">${falseText}</span>`;
            }
        }

        function getInteractionTypeBadge(type) {
            const types = {
                'call': '📞 Appel',
                'email': '📧 Email',
                'meeting': '🤝 Réunion',
                'demo': '🖥️ Démo',
                'support': '🛠️ Support'
            };
            return types[type] || type;
        }

        function getInteractionStatusBadge(status) {
            const badges = {
                'completed': '<span class="badge badge-success">Terminé</span>',
                'scheduled': '<span class="badge badge-info">Planifié</span>',
                'cancelled': '<span class="badge badge-danger">Annulé</span>',
                'pending': '<span class="badge badge-warning">En attente</span>'
            };
            return badges[status] || '<span class="badge badge-warning">Inconnu</span>';
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        }

        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            container.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            // Masquer l'alerte après 5 secondes
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function refreshData() {
            loadAllData();
        }
    </script>
</body>
</html>
